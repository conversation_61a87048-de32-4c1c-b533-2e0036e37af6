#!/usr/bin/env python3
"""
智能期货交易系统简化GUI
Simple GUI for Intelligent Futures Trading System

提供基本的图形界面功能，避免复杂的初始化问题
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import traceback
from datetime import datetime, timedelta
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

class SimpleTradingGUI:
    """简化的交易系统GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("智能期货交易系统 v1.0 (简化版)")
        self.root.geometry("1000x700")
        
        # 系统状态
        self.trading_system = None
        self.system_running = False
        
        # 创建界面
        self.create_interface()
        
    def create_interface(self):
        """创建界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = tk.Label(main_frame, text="智能期货交易系统", 
                              font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个选项卡
        self.create_demo_tab()
        self.create_backtest_tab()
        self.create_strategy_tab()
        self.create_log_tab()
        
        # 状态栏
        self.status_label = tk.Label(self.root, text="系统就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_demo_tab(self):
        """创建演示选项卡"""
        demo_frame = ttk.Frame(self.notebook)
        self.notebook.add(demo_frame, text="功能演示")
        
        # 账户设置
        settings_frame = ttk.LabelFrame(demo_frame, text="账户设置", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(settings_frame, text="账户净值:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.account_entry = ttk.Entry(settings_frame, width=15)
        self.account_entry.insert(0, "5000000")
        self.account_entry.grid(row=0, column=1, padx=5)
        ttk.Label(settings_frame, text="元").grid(row=0, column=2, sticky=tk.W)
        
        # 功能按钮
        buttons_frame = ttk.LabelFrame(demo_frame, text="功能演示", padding=10)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(buttons_frame, text="品种筛选演示", 
                  command=self.run_selection_demo).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(buttons_frame, text="参数优化演示", 
                  command=self.run_optimization_demo).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(buttons_frame, text="风险管理演示", 
                  command=self.run_risk_demo).pack(side=tk.LEFT, padx=5)
        
        # 结果显示
        result_frame = ttk.LabelFrame(demo_frame, text="演示结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, height=20, wrap=tk.WORD)
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
    def create_backtest_tab(self):
        """创建回测选项卡"""
        backtest_frame = ttk.Frame(self.notebook)
        self.notebook.add(backtest_frame, text="策略回测")
        
        # 回测设置
        settings_frame = ttk.LabelFrame(backtest_frame, text="回测设置", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 日期设置
        ttk.Label(settings_frame, text="开始日期:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.start_date_entry = ttk.Entry(settings_frame, width=12)
        self.start_date_entry.insert(0, "2024-01-01")
        self.start_date_entry.grid(row=0, column=1, padx=5)
        
        ttk.Label(settings_frame, text="结束日期:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.end_date_entry = ttk.Entry(settings_frame, width=12)
        self.end_date_entry.insert(0, datetime.now().strftime('%Y-%m-%d'))
        self.end_date_entry.grid(row=0, column=3, padx=5)
        
        ttk.Button(settings_frame, text="运行回测", 
                  command=self.run_backtest).grid(row=0, column=4, padx=10)
        
        # 回测结果
        result_frame = ttk.LabelFrame(backtest_frame, text="回测结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.backtest_text = scrolledtext.ScrolledText(result_frame, height=25, wrap=tk.WORD)
        self.backtest_text.pack(fill=tk.BOTH, expand=True)
        
    def create_strategy_tab(self):
        """创建策略选项卡"""
        strategy_frame = ttk.Frame(self.notebook)
        self.notebook.add(strategy_frame, text="策略管理")
        
        # 策略加载
        load_frame = ttk.LabelFrame(strategy_frame, text="策略加载", padding=10)
        load_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(load_frame, text="加载vnpy策略", 
                  command=self.load_strategies).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(load_frame, text="查看策略列表", 
                  command=self.show_strategies).pack(side=tk.LEFT, padx=5)
        
        self.strategy_count_label = ttk.Label(load_frame, text="策略数量: 0")
        self.strategy_count_label.pack(side=tk.LEFT, padx=20)
        
        # 策略信息
        info_frame = ttk.LabelFrame(strategy_frame, text="策略信息", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.strategy_text = scrolledtext.ScrolledText(info_frame, height=20, wrap=tk.WORD)
        self.strategy_text.pack(fill=tk.BOTH, expand=True)
        
    def create_log_tab(self):
        """创建日志选项卡"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="系统日志")
        
        # 日志控制
        control_frame = ttk.Frame(log_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(control_frame, text="清空日志", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="保存日志", 
                  command=self.save_log).pack(side=tk.LEFT, padx=5)
        
        # 日志显示
        self.log_text = scrolledtext.ScrolledText(log_frame, height=30, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 添加初始日志
        self.log_message("系统初始化完成")
        
    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 更新状态栏（如果存在）
        if hasattr(self, 'status_label') and self.status_label:
            self.status_label.config(text=message)
        
    def run_selection_demo(self):
        """运行品种筛选演示"""
        def demo_thread():
            try:
                self.log_message("开始品种筛选演示...")
                
                from core.instrument_selector import InstrumentSelector
                from core.data_manager import DataManager
                from contract_manager import get_contract_manager
                
                # 获取账户净值
                account_value = float(self.account_entry.get())
                
                # 初始化组件
                contract_manager = get_contract_manager()
                data_manager = DataManager()
                selector = InstrumentSelector(account_value)
                
                # 获取测试数据
                symbols = ['rb888.SHFE', 'cu888.SHFE', 'au888.SHFE', 'TA888.CZCE', 'm888.DCE']
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
                
                market_data = {}
                for symbol in symbols:
                    data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
                    if not data.empty:
                        market_data[symbol] = data
                
                # 执行筛选
                metrics_list = selector.select_instruments(market_data)
                selected = [m.symbol for m in metrics_list if m.is_selected]
                
                # 显示结果
                result = f"""品种筛选演示结果
账户净值: {account_value:,.0f} 元
评估品种数: {len(metrics_list)}
选中品种数: {len(selected)}
选中品种: {', '.join(selected)}

详细评估结果:
{'品种代码':<15} {'动量评分':<10} {'策略胜率':<10} {'综合评分':<10} {'状态':<8}
{'-'*70}
"""
                
                for metrics in metrics_list:
                    status = "✓选中" if metrics.is_selected else "✗未选"
                    result += f"{metrics.symbol:<15} {metrics.momentum_score:>8.3f} {metrics.strategy_winrate:>8.1%} {metrics.overall_score:>8.3f} {status:<8}\n"
                
                # 更新界面
                self.root.after(0, lambda: self.update_result_text(result))
                self.log_message("品种筛选演示完成")
                
            except Exception as e:
                error_msg = f"品种筛选演示失败: {e}"
                self.log_message(error_msg, "ERROR")
                self.root.after(0, lambda: self.update_result_text(error_msg))
        
        threading.Thread(target=demo_thread, daemon=True).start()
        
    def run_optimization_demo(self):
        """运行参数优化演示"""
        def demo_thread():
            try:
                self.log_message("开始参数优化演示...")
                
                from core.parameter_optimizer import ParameterOptimizer
                from core.data_manager import DataManager
                
                optimizer = ParameterOptimizer()
                data_manager = DataManager()
                
                # 获取测试数据
                symbol = 'rb888.SHFE'
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
                
                data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
                
                if not data.empty:
                    # 执行优化
                    result = optimizer.optimize_parameters(symbol, data, max_workers=1)
                    
                    # 显示结果
                    result_text = f"""参数优化演示结果
品种: {result.symbol}
数据长度: {len(data)} 条
优化时间: {result.optimization_time:.2f} 秒
稳定性评分: {result.stability_score:.3f}
是否稳定: {'是' if result.is_stable else '否'}

最佳参数:
"""
                    for param, value in result.best_params.items():
                        result_text += f"  {param}: {value}\n"
                    
                    result_text += f"\n性能指标:\n"
                    for metric, value in result.performance_metrics.items():
                        result_text += f"  {metric}: {value:.4f}\n"
                    
                    # 更新界面
                    self.root.after(0, lambda: self.update_result_text(result_text))
                    self.log_message("参数优化演示完成")
                else:
                    error_msg = "无法获取有效的优化数据"
                    self.log_message(error_msg, "WARNING")
                    self.root.after(0, lambda: self.update_result_text(error_msg))
                
            except Exception as e:
                error_msg = f"参数优化演示失败: {e}"
                self.log_message(error_msg, "ERROR")
                self.root.after(0, lambda: self.update_result_text(error_msg))
        
        threading.Thread(target=demo_thread, daemon=True).start()
        
    def run_risk_demo(self):
        """运行风险管理演示"""
        def demo_thread():
            try:
                self.log_message("开始风险管理演示...")
                
                from core.risk_manager import RiskManager, PositionInfo
                from contract_manager import get_contract_manager
                
                # 获取账户净值
                account_value = float(self.account_entry.get())
                
                contract_manager = get_contract_manager()
                risk_manager = RiskManager(account_value, contract_manager.contract_params)
                
                # 模拟持仓
                positions = {
                    'rb888.SHFE': PositionInfo(
                        symbol='rb888.SHFE',
                        current_lots=5,
                        suggested_lots=6,
                        risk_amount=100000,
                        risk_percentage=0.02,
                        atr_value=80.0,
                        contract_value=200000
                    ),
                    'cu888.SHFE': PositionInfo(
                        symbol='cu888.SHFE',
                        current_lots=3,
                        suggested_lots=3,
                        risk_amount=90000,
                        risk_percentage=0.018,
                        atr_value=1200.0,
                        contract_value=350000
                    )
                }
                
                # 计算风险指标
                risk_metrics = risk_manager.calculate_portfolio_risk(positions)
                warnings = risk_manager.check_risk_limits(positions)
                
                # 显示结果
                result_text = f"""风险管理演示结果
账户净值: {account_value:,.0f} 元

组合风险指标:
总风险暴露: {risk_metrics.total_risk_exposure:,.0f} 元
组合风险比例: {risk_metrics.portfolio_risk_percentage:.1%}
最大单品种风险: {risk_metrics.max_single_risk:.1%}
品种数量: {risk_metrics.instrument_count}

持仓详情:
"""
                for symbol, position in positions.items():
                    result_text += f"""
{symbol}:
  当前手数: {position.current_lots}
  建议手数: {position.suggested_lots}
  风险金额: {position.risk_amount:,.0f} 元
  风险比例: {position.risk_percentage:.1%}
  合约价值: {position.contract_value:,.0f} 元
"""
                
                # 风险警告
                result_text += f"\n风险检查结果:\n"
                total_warnings = 0
                for severity, warning_list in warnings.items():
                    if warning_list:
                        result_text += f"{severity.upper()}: {len(warning_list)} 条\n"
                        for warning in warning_list:
                            result_text += f"  - {warning}\n"
                        total_warnings += len(warning_list)
                
                if total_warnings == 0:
                    result_text += "✅ 无风险警告\n"
                
                # 更新界面
                self.root.after(0, lambda: self.update_result_text(result_text))
                self.log_message("风险管理演示完成")
                
            except Exception as e:
                error_msg = f"风险管理演示失败: {e}"
                self.log_message(error_msg, "ERROR")
                self.root.after(0, lambda: self.update_result_text(error_msg))
        
        threading.Thread(target=demo_thread, daemon=True).start()
        
    def run_backtest(self):
        """运行回测"""
        def backtest_thread():
            try:
                self.log_message("开始运行回测...")
                
                from core.backtest_engine import BacktestEngine
                from strategies.adaptive_trend_strategy import AdaptiveTrendStrategy
                from core.data_manager import DataManager
                
                # 获取设置
                start_date = self.start_date_entry.get()
                end_date = self.end_date_entry.get()
                account_value = float(self.account_entry.get())
                
                # 初始化组件
                backtest_engine = BacktestEngine(initial_capital=account_value)
                strategy = AdaptiveTrendStrategy()
                data_manager = DataManager()
                
                # 获取回测数据
                symbols = ['rb888.SHFE', 'cu888.SHFE']
                backtest_data = {}
                
                for symbol in symbols:
                    data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
                    if not data.empty:
                        backtest_data[symbol] = data
                        strategy.calculate_indicators(data, symbol)
                
                if backtest_data:
                    # 运行回测
                    result = backtest_engine.run_backtest(strategy, backtest_data, start_date, end_date)
                    
                    # 显示结果
                    result_text = f"""回测结果报告
回测期间: {start_date} 到 {end_date}
回测品种: {', '.join(backtest_data.keys())}
初始资金: {result.initial_capital:,.0f} 元
最终资金: {result.final_capital:,.0f} 元

收益指标:
总收益率: {result.total_return:.2%}
年化收益率: {result.annual_return:.2%}
最大回撤: {result.max_drawdown:.2%}

风险指标:
夏普比率: {result.sharpe_ratio:.3f}
Calmar比率: {result.calmar_ratio:.3f}
波动率: {result.daily_returns.std() * (252**0.5):.2%} (年化)

交易统计:
总交易次数: {result.total_trades}
胜率: {result.win_rate:.1%}
盈亏比: {result.profit_factor:.2f}
平均每笔盈亏: {result.avg_trade_pnl:.2f} 元
最大连续盈利: {result.max_consecutive_wins}
最大连续亏损: {result.max_consecutive_losses}
"""
                    
                    if result.trades:
                        result_text += f"\n最近交易记录:\n"
                        for i, trade in enumerate(result.trades[-5:], 1):
                            direction = "多头" if trade.direction == 1 else "空头"
                            result_text += f"{i}. {trade.symbol} {direction} {trade.quantity}手 盈亏:{trade.pnl:8.2f}元 时间:{trade.exit_time.strftime('%m-%d')}\n"
                    
                    # 更新界面
                    self.root.after(0, lambda: self.update_backtest_text(result_text))
                    self.log_message("回测完成")
                else:
                    error_msg = "无法获取有效的回测数据"
                    self.log_message(error_msg, "WARNING")
                    self.root.after(0, lambda: self.update_backtest_text(error_msg))
                
            except Exception as e:
                error_msg = f"回测失败: {e}\n{traceback.format_exc()}"
                self.log_message(f"回测失败: {e}", "ERROR")
                self.root.after(0, lambda: self.update_backtest_text(error_msg))
        
        threading.Thread(target=backtest_thread, daemon=True).start()
        
    def load_strategies(self):
        """加载策略"""
        def load_thread():
            try:
                self.log_message("开始加载策略...")
                
                from strategy_loader import load_all_strategies
                
                strategies = load_all_strategies()
                
                # 更新界面
                self.root.after(0, lambda: self.update_strategy_count(len(strategies)))
                self.log_message(f"成功加载 {len(strategies)} 个策略")
                
            except Exception as e:
                self.log_message(f"加载策略失败: {e}", "ERROR")
        
        threading.Thread(target=load_thread, daemon=True).start()
        
    def show_strategies(self):
        """显示策略列表"""
        def show_thread():
            try:
                self.log_message("获取策略列表...")
                
                from strategy_loader import get_strategy_loader
                
                strategy_loader = get_strategy_loader()
                strategies = strategy_loader.get_strategy_list()
                
                # 格式化策略信息
                strategy_text = f"策略列表 (共 {len(strategies)} 个)\n\n"
                
                for i, strategy in enumerate(strategies, 1):
                    strategy_text += f"{i}. {strategy['name']}\n"
                    strategy_text += f"   显示名称: {strategy['display_name']}\n"
                    strategy_text += f"   类型: {strategy['type']}\n"
                    strategy_text += f"   参数数量: {len(strategy['parameters'])}\n"
                    strategy_text += f"   描述: {strategy['description'][:100]}...\n"
                    
                    if strategy['parameters']:
                        strategy_text += f"   主要参数:\n"
                        for param_name, param_info in list(strategy['parameters'].items())[:3]:
                            strategy_text += f"     - {param_name}: {param_info.get('default', 'N/A')}\n"
                    
                    strategy_text += "\n"
                
                # 更新界面
                self.root.after(0, lambda: self.update_strategy_text(strategy_text))
                self.log_message("策略列表获取完成")
                
            except Exception as e:
                error_msg = f"获取策略列表失败: {e}"
                self.log_message(error_msg, "ERROR")
                self.root.after(0, lambda: self.update_strategy_text(error_msg))
        
        threading.Thread(target=show_thread, daemon=True).start()
        
    def update_result_text(self, text: str):
        """更新结果文本"""
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, text)
        
    def update_backtest_text(self, text: str):
        """更新回测文本"""
        self.backtest_text.delete(1.0, tk.END)
        self.backtest_text.insert(tk.END, text)
        
    def update_strategy_text(self, text: str):
        """更新策略文本"""
        self.strategy_text.delete(1.0, tk.END)
        self.strategy_text.insert(tk.END, text)
        
    def update_strategy_count(self, count: int):
        """更新策略数量"""
        self.strategy_count_label.config(text=f"策略数量: {count}")
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")
        
    def save_log(self):
        """保存日志"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"system_log_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
            
            self.log_message(f"日志已保存到: {filename}")
            messagebox.showinfo("成功", f"日志已保存到: {filename}")
            
        except Exception as e:
            self.log_message(f"保存日志失败: {e}", "ERROR")
            messagebox.showerror("错误", f"保存日志失败: {e}")
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = SimpleTradingGUI()
        app.run()
    except Exception as e:
        print(f"简化GUI启动失败: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
