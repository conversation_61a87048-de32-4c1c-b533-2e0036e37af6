"""
增强稳定性导向参数优化器使用示例
基于 optimization_example.py 中的参数配置

展示如何使用增强稳定性优化器进行高质量的参数优化：
1. 三层稳定性验证机制
2. 参数重要性分析
3. 自适应采样策略
4. 鲁棒性验证
5. 稳定性评分系统

使用方法：
python stability_optimization_example.py --demo      # 运行演示模式
python stability_optimization_example.py --single    # 单品种优化
python stability_optimization_example.py --batch     # 批量优化
"""

import sys
from pathlib import Path
from datetime import datetime
import warnings
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import json
import time

warnings.filterwarnings('ignore')

# 添加路径
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

# 导入所需模块
try:
    from vnpy.app.cta_strategy.strategies.fenzhouqiplus_strategy import FenZhouQiPlusStrategy
    from contract_info import ContractInfo
    VNPY_AVAILABLE = True
except ImportError:
    VNPY_AVAILABLE = False
    print("警告: VnPy相关模块未找到，将使用模拟策略")

from enhanced_stability_optimizer import EnhancedStabilityOptimizer, StabilityMetrics, OptimizationResult

# 只在VnPy可用时导入多品种回测
try:
    from enhanced_multi_symbol_backtest import EnhancedMultiSymbolBacktest
    MULTI_SYMBOL_AVAILABLE = True
except ImportError:
    MULTI_SYMBOL_AVAILABLE = False
    print("注意: 多品种回测模块不可用，将使用独立的稳定性优化器")


def generate_stability_param_spaces(contract_size: int = 5) -> Dict[str, Tuple]:
    """
    生成用于稳定性优化的参数空间
    基于 optimization_example.py 中的参数配置
    
    Args:
        contract_size: 合约大小，用于计算相关参数
        
    Returns:
        参数空间字典 {参数名: (最小值, 最大值, 步长, 类型)}
    """
    # 只返回需要优化的参数，不包含固定参数
    param_spaces = {
        # K线参数 (动态)
        'k_1': (1, 10, 1, int),
        'k_3': (5, 20, 1, int), 
        'k_5': (10, 30, 2, int),
        
        # 核心交易参数
        'sl_multiplier': (1.5, 9.0, 0.5, float),
        'macd_boll_count_fz': (0.08, 0.2, 0.02, float),
        'dk_fz': (0.7, 1.0, 0.05, float),
        'ping_zy': (0, 30, 1, int),
        'zy': (5, 60, 5, int),
        
        # 追踪止损参数
        'AF': (0.0002, 0.002, 0.0002, float),
        'AF_max': (0.01, 0.2, 0.02, float),
        'trailing_start_ratio': (0.3, 0.9, 0.2, float),
        
        # 风控参数
        'daily_loss_limit': (1000, 3000, 500, int),
    }
    
    return param_spaces


def get_stability_parameter_constraints() -> Dict[str, callable]:
    """
    定义稳定性优化的参数约束关系
    
    Returns:
        Dict[str, callable]: 约束函数字典
    """
    constraints = {
        # ping_zy (保本点) 必须小于 zy (部分止盈点)
        'ping_zy_less_than_zy': lambda params: params.get('ping_zy', 0) < params.get('zy', float('inf')),
        
        # AF (加速因子) 必须小于 AF_max (最大加速因子)
        'AF_less_than_AF_max': lambda params: params.get('AF', 0) < params.get('AF_max', float('inf')),
        
        # sl_multiplier (止损倍数) 应该在合理范围内
        'sl_multiplier_reasonable': lambda params: params.get('sl_multiplier', 2.0) >= 1.0,
        
        # trailing_start_ratio 应该在0-1之间
        'trailing_start_ratio_valid': lambda params: 0 < params.get('trailing_start_ratio', 0.5) < 1,
        
        # K线周期参数的合理性检查
        'k_periods_logical': lambda params: (
            params.get('k_1', 1) <= params.get('k_3', 3) and
            params.get('k_3', 3) <= params.get('k_5', 5)
        ),
        
        # 止盈参数应该有合理的差距
        'stop_profit_gap': lambda params: params.get('zy', 15) >= params.get('ping_zy', 5) + 5,
    }
    
    return constraints


def apply_stability_constraints(params: Dict[str, Any], param_spaces: Dict[str, Tuple]) -> Dict[str, Any]:
    """
    应用参数约束，确保参数满足稳定性要求
    
    Args:
        params: 原始参数字典
        param_spaces: 参数空间定义
        
    Returns:
        Dict: 调整后的参数字典
    """
    constrained_params = params.copy()
    constraints = get_stability_parameter_constraints()
    max_iterations = 10
    
    for iteration in range(max_iterations):
        all_constraints_satisfied = True
        
        for constraint_name, constraint_func in constraints.items():
            if not constraint_func(constrained_params):
                all_constraints_satisfied = False
                
                # 针对不同约束进行特定的参数调整
                if constraint_name == 'ping_zy_less_than_zy':
                    ping_zy = constrained_params.get('ping_zy', 10)
                    zy = constrained_params.get('zy', 20)
                    
                    if ping_zy >= zy:
                        min_val, max_val, step, param_type = param_spaces['ping_zy']
                        new_ping_zy = max(min_val, min(ping_zy, zy - 5))
                        constrained_params['ping_zy'] = new_ping_zy
                        
                        if new_ping_zy >= zy:
                            min_val, max_val, step, param_type = param_spaces['zy']
                            new_zy = max(new_ping_zy + 5, min_val)
                            new_zy = min(new_zy, max_val)
                            constrained_params['zy'] = new_zy
                
                elif constraint_name == 'AF_less_than_AF_max':
                    af = constrained_params.get('AF', 0.001)
                    af_max = constrained_params.get('AF_max', 0.15)
                    
                    if af >= af_max:
                        new_af = min(af, af_max * 0.8)
                        min_val, max_val, step, param_type = param_spaces['AF']
                        constrained_params['AF'] = max(new_af, min_val)
                
                elif constraint_name == 'stop_profit_gap':
                    ping_zy = constrained_params.get('ping_zy', 10)
                    zy = constrained_params.get('zy', 20)
                    
                    if zy < ping_zy + 5:
                        min_val, max_val, step, param_type = param_spaces['zy']
                        new_zy = min(ping_zy + 5, max_val)
                        constrained_params['zy'] = new_zy
                        
                        if new_zy < ping_zy + 5:
                            min_val, max_val, step, param_type = param_spaces['ping_zy']
                            new_ping_zy = max(new_zy - 5, min_val)
                            constrained_params['ping_zy'] = new_ping_zy
        
        if all_constraints_satisfied:
            break
    
    # 最终约束到参数范围内
    final_params = {}
    for param_name, value in constrained_params.items():
        if param_name in param_spaces:
            min_val, max_val, step, param_type = param_spaces[param_name]
            
            # 约束到范围内
            constrained_value = max(min_val, min(max_val, value))
            
            # 类型转换
            if param_type == int:
                constrained_value = int(round(constrained_value / step) * step)
            else:
                constrained_value = round(constrained_value / step) * step
            
            # 再次确保在范围内
            constrained_value = max(min_val, min(max_val, constrained_value))
            
            final_params[param_name] = constrained_value
        else:
            final_params[param_name] = value
    
    return final_params


def get_fixed_parameters() -> Dict[str, Any]:
    """
    获取固定参数值
    这些参数在优化过程中保持不变
    """
    return {
        # K线参数 (固定)
        'k_15': 15,
        'k_30': 30,
        
        # 技术指标参数
        'atr_window': 30,
        'donchian_period': 20,
        
        # 交易参数
        'lots': 1,
        'use_trailing_stop': 1,
        'contract_multiplier': 5,
    }


def create_mock_backtester(symbol: str, contract_size: float):
    """
    创建模拟回测器用于演示
    
    Args:
        symbol: 合约代码
        contract_size: 合约大小
        
    Returns:
        callable: 回测函数
    """
    def mock_backtest(params: Dict[str, Any]) -> Dict[str, Any]:
        """
        模拟回测函数
        基于参数生成合理的性能指标
        """
        # 应用参数约束
        param_spaces = generate_stability_param_spaces(contract_size)
        constrained_params = apply_stability_constraints(params, param_spaces)
        
        # 模拟性能计算（基于参数的合理性）
        base_return = 0.15
        base_volatility = 0.25
        base_sharpe = base_return / base_volatility
        
        # 参数影响因子
        sl_factor = min(constrained_params.get('sl_multiplier', 3.0) / 3.0, 1.5)
        zy_factor = min(constrained_params.get('zy', 30) / 30.0, 2.0)
        af_factor = min(constrained_params.get('AF', 0.001) * 1000, 2.0)
        
        # 添加随机噪声模拟市场不确定性
        noise = np.random.normal(0, 0.02)
        
        # 计算调整后的指标
        adjusted_return = base_return * (0.8 + 0.4 * sl_factor * zy_factor) + noise
        adjusted_volatility = base_volatility * (1.2 - 0.2 * af_factor)
        adjusted_sharpe = adjusted_return / adjusted_volatility
        
        # 其他指标
        max_drawdown = abs(np.random.normal(0.08, 0.02))
        win_rate = min(max(0.4 + np.random.normal(0, 0.1), 0.2), 0.8)
        profit_loss_ratio = max(1.0 + np.random.normal(0, 0.3), 0.5)
        
        return {
            'total_return': adjusted_return,
            'annual_return': adjusted_return,
            'sharpe_ratio': adjusted_sharpe,
            'max_drawdown': max_drawdown,
            'volatility': adjusted_volatility,
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'total_trades': int(np.random.uniform(100, 500)),
            'profit_trades': int(win_rate * np.random.uniform(100, 500)),
        }
    
    return mock_backtest


def run_single_symbol_stability_optimization():
    """运行单品种稳定性导向参数优化示例"""
    
    print("=" * 80)
    print("单品种稳定性导向参数优化示例")
    print("=" * 80)
    
    # 配置参数
    symbol = "cu888.SHFE"
    contract_size = 5
    
    print(f"\n测试品种: {symbol}")
    print(f"合约大小: {contract_size}")
    
    # 生成参数空间
    param_spaces = generate_stability_param_spaces(contract_size)
    fixed_params = get_fixed_parameters()
    
    print(f"参数空间维度: {len(param_spaces)}")
    print(f"固定参数数量: {len(fixed_params)}")
    
    print("\n主要优化参数:")
    for param_name, (min_val, max_val, step, param_type) in param_spaces.items():
        print(f"  {param_name}: {min_val} - {max_val} (步长: {step}, 类型: {param_type.__name__})")
    
    print("\n固定参数:")
    for param_name, value in fixed_params.items():
        print(f"  {param_name}: {value}")
        
    print("\n创建增强稳定性优化器...")
    
    # 创建回测器
    backtester = create_mock_backtester(symbol, contract_size)
    
    # 创建优化器
    optimizer = EnhancedStabilityOptimizer(
        backtester=backtester,
        param_spaces=param_spaces,
        stability_weight=0.4,  # 稳定性权重
        max_workers=2,         # 减少并行数用于演示
        random_seed=42
    )
    
    print("开始稳定性优化...")
    start_time = time.time()
    
    try:
        # 执行优化
        result = optimizer.stability_optimization(
            max_evaluations=100,   # 减少评估次数用于演示
            n_initial_samples=50,  # 减少初始样本数
            convergence_threshold=1e-6,
            max_stagnant_iterations=20
        )
        
        optimization_time = time.time() - start_time
        
        print(f"\n优化完成! 耗时: {optimization_time:.1f}秒")
        
        # 合并优化参数和固定参数
        final_params = {**result.best_params, **fixed_params}
        
        # 显示结果
        print("\n" + "="*50)
        print("优化结果摘要")
        print("="*50)
        print(f"最佳性能指标: {result.best_performance:.4f}")
        print(f"稳定性得分: {result.stability_score:.4f}")
        print(f"优化时间: {optimization_time:.1f}秒")
        
        print("\n最佳参数组合:")
        for param_name, value in final_params.items():
            if param_name in param_spaces:
                print(f"  {param_name}: {value} (优化)")
            else:
                print(f"  {param_name}: {value} (固定)")
        
        print("\n稳定性指标详情:")
        print(f"  性能稳定性: {result.stability_metrics.performance_stability:.4f}")
        print(f"  参数敏感性: {result.stability_metrics.parameter_sensitivity:.4f}")
        print(f"  时间一致性: {result.stability_metrics.temporal_consistency:.4f}")
        print(f"  鲁棒性得分: {result.stability_metrics.robustness_score:.4f}")
        print(f"  总体稳定性: {result.stability_metrics.overall_stability:.4f}")
        
        # 参数重要性分析
        if hasattr(result, 'parameter_importance') and result.parameter_importance:
            print("\n参数重要性排序:")
            sorted_importance = sorted(
                result.parameter_importance.items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            for param_name, importance in sorted_importance[:10]:
                print(f"  {param_name}: {importance:.4f}")
        
        # 保存结果
        result_data = {
            "symbol": symbol,
            "optimization_summary": {
                "best_performance": float(result.best_performance),
                "stability_score": float(result.stability_score),
                "optimization_time": optimization_time
            },
            "best_parameters": {k: (float(v) if isinstance(v, (int, float)) else v) 
                              for k, v in final_params.items()},
            "stability_metrics": {
                "performance_stability": float(result.stability_metrics.performance_stability),
                "parameter_sensitivity": float(result.stability_metrics.parameter_sensitivity),
                "temporal_consistency": float(result.stability_metrics.temporal_consistency),
                "robustness_score": float(result.stability_metrics.robustness_score),
                "overall_stability": float(result.stability_metrics.overall_stability)
            },
            "parameter_importance": {k: float(v) for k, v in result.parameter_importance.items()} 
                                  if hasattr(result, 'parameter_importance') else {}
        }
        
        # 保存到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"stability_optimization_result_{symbol.replace('.', '_')}_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n结果已保存到: {filename}")
        
    except Exception as e:
        print(f"\n优化过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*80)
    print("单品种稳定性优化完成!")
    print("="*80)


def run_batch_stability_optimization():
    """运行批量稳定性优化示例"""
    print("=" * 80)
    print("批量品种稳定性导向参数优化示例")
    print("=" * 80)
    
    # 选择测试品种
    test_symbols = ['cu888.SHFE', 'al888.SHFE', 'zn888.SHFE']
    
    # 模拟合约信息
    contract_info = {
        'cu888.SHFE': {'size': 5, 'pricetick': 10, 'rate': 0.0002},
        'al888.SHFE': {'size': 5, 'pricetick': 5, 'rate': 0.0002},
        'zn888.SHFE': {'size': 5, 'pricetick': 5, 'rate': 0.0002},
    }
    
    print(f"测试品种: {test_symbols}")
    
    batch_results = {}
    
    for symbol in test_symbols:
        print(f"\n{'='*20} 优化品种: {symbol} {'='*20}")
        
        # 获取合约信息
        info = contract_info[symbol]
        contract_size = info['size']
        
        # 生成参数空间
        param_spaces = generate_stability_param_spaces(contract_size)
        
        # 创建回测器
        backtester = create_mock_backtester(symbol, contract_size)
        
        # 创建优化器
        optimizer = EnhancedStabilityOptimizer(
            backtester=backtester,
            param_spaces=param_spaces,
            stability_weight=0.4,
            max_workers=2,  # 减少并行度用于演示
            random_seed=42
        )
        
        try:
            # 执行优化
            start_time = time.time()
            result = optimizer.stability_optimization(
                max_evaluations=100,   # 减少评估次数用于演示
                n_initial_samples=50,  # 减少初始样本数
                convergence_threshold=1e-6,
                max_stagnant_iterations=20
            )
            optimization_time = time.time() - start_time
            
            # 记录结果
            batch_results[symbol] = {
                'result': result,
                'optimization_time': optimization_time,
                'contract_size': contract_size
            }
            
            print(f"优化完成 - 性能: {result.best_performance:.4f}, 稳定性: {result.stability_score:.4f}, 耗时: {optimization_time:.1f}秒")
            
        except Exception as e:
            print(f"优化失败: {str(e)}")
            batch_results[symbol] = {'error': str(e)}
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("批量优化结果汇总")
    print("=" * 80)
    
    successful_results = []
    
    for symbol, data in batch_results.items():
        if 'error' not in data:
            result = data['result']
            optimization_time = data['optimization_time']
            contract_size = data['contract_size']
            
            print(f"\n品种: {symbol}")
            print(f"合约大小: {contract_size}")
            print(f"最佳性能: {result.best_performance:.4f}")
            print(f"稳定性得分: {result.stability_score:.4f}")
            print(f"优化耗时: {optimization_time:.1f}秒")
            
            # 显示关键参数
            key_params = ['sl_multiplier', 'zy', 'ping_zy', 'AF', 'AF_max']
            print("关键参数:")
            for param in key_params:
                if param in result.best_params:
                    value = result.best_params[param]
                    print(f"  {param}: {value}")
            
            successful_results.append((symbol, result))
        else:
            print(f"\n品种: {symbol} - 优化失败: {data['error']}")
    
    # 生成批量报告
    if successful_results:
        print(f"\n成功优化品种数: {len(successful_results)}/{len(test_symbols)}")
        
        # 计算平均稳定性指标
        avg_performance = np.mean([r[1].best_performance for r in successful_results])
        avg_stability = np.mean([r[1].stability_score for r in successful_results])
        
        print(f"平均性能指标: {avg_performance:.4f}")
        print(f"平均稳定性得分: {avg_stability:.4f}")
        
        # 保存批量报告
        batch_report_path = f"batch_stability_optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        batch_report = {
            'summary': {
                'total_symbols': len(test_symbols),
                'successful_symbols': len(successful_results),
                'average_performance': avg_performance,
                'average_stability': avg_stability,
            },
            'results': {}
        }
        
        for symbol, result in successful_results:
            batch_report['results'][symbol] = {
                'best_performance': result.best_performance,
                'stability_score': result.stability_score,
                'best_params': result.best_params,
                'stability_metrics': {
                    'performance_stability': result.stability_metrics.performance_stability,
                    'parameter_sensitivity': result.stability_metrics.parameter_sensitivity,
                    'temporal_consistency': result.stability_metrics.temporal_consistency,
                    'robustness_score': result.stability_metrics.robustness_score,
                    'overall_stability': result.stability_metrics.overall_stability,
                }
            }
        
        with open(batch_report_path, 'w', encoding='utf-8') as f:
            json.dump(batch_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n批量优化报告已保存: {batch_report_path}")
    
    return batch_results


def run_demo_mode():
    """运行演示模式"""
    print("=" * 80)
    print("增强稳定性导向参数优化器 - 演示模式")
    print("=" * 80)
    
    # 展示参数空间配置
    print("\n1. 参数空间配置演示")
    print("-" * 40)
    
    contract_size = 5
    param_spaces = generate_stability_param_spaces(contract_size)
    
    print(f"参数空间维度: {len(param_spaces)}")
    print("参数分类:")
    
    # 按类别显示参数
    categories = {
        'K线参数': ['k_1', 'k_3', 'k_5'],
        '核心交易参数': ['sl_multiplier', 'macd_boll_count_fz', 'dk_fz', 'ping_zy', 'zy'],
        '追踪止损参数': ['AF', 'AF_max', 'trailing_start_ratio'],
        '风控参数': ['daily_loss_limit'],
    }
    
    for category, params in categories.items():
        print(f"\n{category}:")
        for param in params:
            if param in param_spaces:
                min_val, max_val, step, param_type = param_spaces[param]
                if max_val != min_val:
                    print(f"  {param}: {min_val} - {max_val} (步长: {step}, 类型: {param_type.__name__})")
                else:
                    print(f"  {param}: {min_val} (固定值)")
    
    # 展示参数约束
    print("\n2. 参数约束演示")
    print("-" * 40)
    
    constraints = get_stability_parameter_constraints()
    print("定义的约束关系:")
    constraint_descriptions = {
        'ping_zy_less_than_zy': '保本点 < 止盈点',
        'AF_less_than_AF_max': '加速因子 < 最大加速因子',
        'sl_multiplier_reasonable': '止损倍数 >= 1.0',
        'trailing_start_ratio_valid': '0 < 启动比例 < 1',
        'k_periods_logical': 'K线周期递增关系',
        'stop_profit_gap': '止盈点至少比保本点大5个点',
    }
    
    for constraint_name in constraints:
        description = constraint_descriptions.get(constraint_name, constraint_name)
        print(f"  ✓ {description}")
    
    # 展示约束修正示例
    print("\n3. 约束修正示例")
    print("-" * 40)
    
    # 创建违反约束的参数示例
    bad_params = {
        'ping_zy': 30,  # 保本点过大
        'zy': 25,       # 止盈点过小
        'AF': 0.16,     # 加速因子过大
        'AF_max': 0.15, # 最大加速因子过小
        'sl_multiplier': 2.5,
        'trailing_start_ratio': 0.5,
    }
    
    print("违反约束的参数:")
    for param, value in bad_params.items():
        print(f"  {param}: {value}")
    
    # 应用约束修正
    corrected_params = apply_stability_constraints(bad_params, param_spaces)
    
    print("\n修正后的参数:")
    for param, value in corrected_params.items():
        if param in bad_params:
            original = bad_params[param]
            if value != original:
                print(f"  {param}: {original} → {value} ✓")
            else:
                print(f"  {param}: {value}")
    
    # 展示稳定性优化器特性
    print("\n4. 稳定性优化器特性")
    print("-" * 40)
    
    features = [
        "三层稳定性验证机制",
        "参数重要性分析 (XGBoost/随机森林)",
        "自适应采样策略",
        "蒙特卡洛鲁棒性测试",
        "Walk-Forward时间一致性验证",
        "分层优化流程 (全局→贝叶斯→精细)",
        "稳定性评分系统",
        "参数约束自动修正",
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"  {i}. {feature}")
    
    print("\n5. 优化流程预览")
    print("-" * 40)
    
    steps = [
        "参数空间验证和约束定义",
        "参数重要性分析 (200样本)",
        "全局搜索阶段 (自适应采样)",
        "贝叶斯优化阶段 (高效搜索)",
        "精细搜索阶段 (局部优化)",
        "稳定性验证 (蒙特卡洛+Walk-Forward)",
        "最终结果验证和报告生成",
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"  步骤{i}: {step}")
    
    # 预估计算量
    print("\n6. 计算量预估")
    print("-" * 40)
    
    total_params = len([p for p, (min_v, max_v, _, _) in param_spaces.items() if max_v != min_v])
    importance_samples = 200
    optimization_samples = 500
    stability_tests = 50  # 蒙特卡洛测试
    
    total_evaluations = importance_samples + optimization_samples + stability_tests
    
    print(f"需要优化的参数数量: {total_params}")
    print(f"参数重要性分析: {importance_samples} 次评估")
    print(f"优化搜索: {optimization_samples} 次评估")
    print(f"稳定性验证: {stability_tests} 次评估")
    print(f"总计评估次数: {total_evaluations}")
    print(f"预估耗时: {total_evaluations * 2 / 60:.1f} 分钟 (假设每次评估2秒)")
    
    print("\n固定参数:")
    fixed_params = get_fixed_parameters()
    for param_name, value in fixed_params.items():
        print(f"  {param_name}: {value} (固定值)")
    
    print("\n" + "=" * 80)
    print("演示模式完成！")
    print("使用 --single 运行单品种优化")
    print("使用 --batch 运行批量优化")
    print("=" * 80)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="增强稳定性导向参数优化器")
    parser.add_argument("--demo", action="store_true", help="运行演示模式")
    parser.add_argument("--single", action="store_true", help="运行单品种优化")
    parser.add_argument("--batch", action="store_true", help="运行批量优化")
    
    args = parser.parse_args()
    
    if args.demo:
        run_demo_mode()
    elif args.single:
        run_single_symbol_stability_optimization()
    elif args.batch:
        run_batch_stability_optimization()
    else:
        print("请选择运行模式:")
        print("python stability_optimization_example.py --demo      # 运行演示模式")
        print("python stability_optimization_example.py --single    # 单品种优化")
        print("python stability_optimization_example.py --batch     # 批量优化")
        
        # 默认运行演示模式
        run_demo_mode()


if __name__ == "__main__":
    # main()
    run_batch_stability_optimization()