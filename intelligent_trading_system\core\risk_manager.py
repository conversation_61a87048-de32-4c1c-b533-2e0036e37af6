"""
资金智能管理系统
Intelligent Risk Management System

功能：
1. 头寸计算模型 - 基于ATR和账户净值的动态头寸计算
2. 组合风险控制 - 实时监控总体风险暴露
3. 尾部风险防护 - 黑天鹅事件识别和应对
4. 动态风险调整 - 根据市场波动率调整风险参数
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class PositionInfo:
    """持仓信息"""
    symbol: str
    current_lots: int
    suggested_lots: int
    risk_amount: float
    risk_percentage: float
    atr_value: float
    contract_value: float

@dataclass
class RiskMetrics:
    """风险指标"""
    total_risk_exposure: float
    portfolio_risk_percentage: float
    max_single_risk: float
    correlation_risk: float
    volatility_risk: float
    tail_risk_score: float

@dataclass
class RiskLimits:
    """风险限制"""
    max_single_instrument_risk: float = 0.02  # 单品种最大风险2%
    max_portfolio_risk: float = 0.15  # 组合最大风险15%
    max_single_allocation: float = 0.20  # 单品种最大配置20%
    max_correlation: float = 0.7  # 最大相关系数
    daily_loss_limit: float = 0.03  # 单日最大亏损3%
    volatility_threshold: float = 3.0  # 波动率阈值（3倍标准差）

class RiskManager:
    """资金智能管理系统"""
    
    def __init__(self, account_value: float, contract_info: Dict):
        """
        初始化风险管理器
        
        Args:
            account_value: 账户净值
            contract_info: 合约信息字典
        """
        self.account_value = account_value
        self.contract_info = contract_info
        self.risk_limits = RiskLimits()
        self.positions = {}
        self.daily_pnl_history = []
        self.risk_events = []
        
    def calculate_position_size(self, symbol: str, atr_value: float, 
                              stop_multiplier: float = 2.5) -> int:
        """
        计算建议头寸大小
        
        Args:
            symbol: 品种代码
            atr_value: ATR值
            stop_multiplier: 止损倍数
            
        Returns:
            建议手数
        """
        if symbol not in self.contract_info:
            logger.warning(f"未找到 {symbol} 的合约信息")
            return 0
        
        # 单品种风险金额
        risk_amount = self.account_value * self.risk_limits.max_single_instrument_risk
        
        # 合约乘数
        multiplier = self.contract_info[symbol]['multiplier']
        
        # 单手风险金额
        risk_per_lot = atr_value * multiplier * stop_multiplier
        
        if risk_per_lot <= 0:
            return 0
        
        # 计算建议手数
        suggested_lots = int(risk_amount / risk_per_lot)
        
        # 确保至少1手，但不超过最大配置限制
        max_lots_by_allocation = int(
            (self.account_value * self.risk_limits.max_single_allocation) / 
            (atr_value * multiplier * 10)  # 假设价格为ATR的10倍
        )
        
        suggested_lots = max(1, min(suggested_lots, max_lots_by_allocation))
        
        return suggested_lots
    
    def calculate_portfolio_risk(self, positions: Dict[str, PositionInfo]) -> RiskMetrics:
        """
        计算组合风险指标
        
        Args:
            positions: 持仓信息字典
            
        Returns:
            风险指标
        """
        if not positions:
            return RiskMetrics(0, 0, 0, 0, 0, 0)
        
        # 总风险暴露
        total_risk = sum(pos.risk_amount for pos in positions.values())
        portfolio_risk_pct = total_risk / self.account_value
        
        # 最大单品种风险
        max_single_risk = max(pos.risk_percentage for pos in positions.values())
        
        # 相关性风险（简化计算）
        correlation_risk = self._estimate_correlation_risk(positions)
        
        # 波动率风险
        volatility_risk = self._calculate_volatility_risk(positions)
        
        # 尾部风险评分
        tail_risk = self._calculate_tail_risk_score(positions)
        
        return RiskMetrics(
            total_risk_exposure=total_risk,
            portfolio_risk_percentage=portfolio_risk_pct,
            max_single_risk=max_single_risk,
            correlation_risk=correlation_risk,
            volatility_risk=volatility_risk,
            tail_risk_score=tail_risk
        )
    
    def check_risk_limits(self, positions: Dict[str, PositionInfo]) -> Dict[str, List[str]]:
        """
        检查风险限制
        
        Args:
            positions: 持仓信息
            
        Returns:
            风险警告字典
        """
        warnings = {
            'critical': [],  # 严重警告
            'warning': [],   # 一般警告
            'info': []       # 信息提示
        }
        
        risk_metrics = self.calculate_portfolio_risk(positions)
        
        # 检查组合风险
        if risk_metrics.portfolio_risk_percentage > self.risk_limits.max_portfolio_risk:
            warnings['critical'].append(
                f"组合风险暴露过高: {risk_metrics.portfolio_risk_percentage:.1%} > "
                f"{self.risk_limits.max_portfolio_risk:.1%}"
            )
        
        # 检查单品种风险
        if risk_metrics.max_single_risk > self.risk_limits.max_single_instrument_risk:
            warnings['warning'].append(
                f"单品种风险过高: {risk_metrics.max_single_risk:.1%} > "
                f"{self.risk_limits.max_single_instrument_risk:.1%}"
            )
        
        # 检查相关性风险
        if risk_metrics.correlation_risk > self.risk_limits.max_correlation:
            warnings['warning'].append(
                f"品种相关性过高: {risk_metrics.correlation_risk:.2f} > "
                f"{self.risk_limits.max_correlation:.2f}"
            )
        
        # 检查波动率风险
        if risk_metrics.volatility_risk > self.risk_limits.volatility_threshold:
            warnings['critical'].append(
                f"市场波动率异常: {risk_metrics.volatility_risk:.1f}倍标准差"
            )
        
        # 检查尾部风险
        if risk_metrics.tail_risk_score > 0.8:
            warnings['critical'].append(
                f"尾部风险过高: {risk_metrics.tail_risk_score:.2f}"
            )
        
        return warnings
    
    def suggest_position_adjustments(self, current_positions: Dict[str, PositionInfo],
                                   market_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """
        建议头寸调整
        
        Args:
            current_positions: 当前持仓
            market_data: 市场数据
            
        Returns:
            调整建议
        """
        suggestions = {}
        
        for symbol, position in current_positions.items():
            if symbol not in market_data:
                continue
                
            data = market_data[symbol]
            if len(data) < 20:
                continue
            
            # 计算当前ATR
            current_atr = self._calculate_atr(data, 14)
            
            # 重新计算建议头寸
            new_suggested_lots = self.calculate_position_size(symbol, current_atr)
            
            # 比较当前持仓和建议持仓
            current_lots = position.current_lots
            difference = new_suggested_lots - current_lots
            
            if abs(difference) > 0:
                action = "增仓" if difference > 0 else "减仓"
                suggestions[symbol] = {
                    'action': action,
                    'current_lots': current_lots,
                    'suggested_lots': new_suggested_lots,
                    'difference': abs(difference),
                    'reason': self._get_adjustment_reason(position, current_atr),
                    'priority': self._get_adjustment_priority(difference, position)
                }
        
        return suggestions
    
    def monitor_daily_risk(self, daily_pnl: float) -> Dict[str, Any]:
        """
        监控日内风险
        
        Args:
            daily_pnl: 当日盈亏
            
        Returns:
            风险监控结果
        """
        # 记录日内盈亏
        self.daily_pnl_history.append({
            'date': datetime.now().date(),
            'pnl': daily_pnl,
            'pnl_percentage': daily_pnl / self.account_value
        })
        
        # 保留最近30天数据
        if len(self.daily_pnl_history) > 30:
            self.daily_pnl_history = self.daily_pnl_history[-30:]
        
        daily_loss_pct = daily_pnl / self.account_value
        
        result = {
            'daily_pnl': daily_pnl,
            'daily_loss_percentage': daily_loss_pct,
            'risk_level': 'normal',
            'actions': []
        }
        
        # 检查单日亏损限制
        if daily_loss_pct < -self.risk_limits.daily_loss_limit:
            result['risk_level'] = 'critical'
            result['actions'].append('强制减仓至半仓')
            result['actions'].append('暂停新开仓')
            
            # 记录风险事件
            self.risk_events.append({
                'timestamp': datetime.now(),
                'type': 'daily_loss_limit',
                'severity': 'critical',
                'description': f"单日亏损超限: {daily_loss_pct:.1%}"
            })
        
        elif daily_loss_pct < -self.risk_limits.daily_loss_limit * 0.7:
            result['risk_level'] = 'warning'
            result['actions'].append('减少新开仓')
            result['actions'].append('加强风险监控')
        
        return result
    
    def detect_black_swan_events(self, market_data: Dict[str, pd.DataFrame]) -> List[Dict]:
        """
        检测黑天鹅事件
        
        Args:
            market_data: 市场数据
            
        Returns:
            黑天鹅事件列表
        """
        events = []
        
        for symbol, data in market_data.items():
            if len(data) < 20:
                continue
            
            # 计算价格变化
            price_change = data['close'].pct_change().iloc[-1]
            
            # 计算历史波动率
            historical_vol = data['close'].pct_change().rolling(20).std().iloc[-1]
            
            # 检测异常价格变动
            if abs(price_change) > historical_vol * 3:
                events.append({
                    'symbol': symbol,
                    'type': 'price_shock',
                    'severity': 'high' if abs(price_change) > historical_vol * 5 else 'medium',
                    'price_change': price_change,
                    'volatility_multiple': abs(price_change) / historical_vol,
                    'timestamp': datetime.now()
                })
            
            # 检测成交量异常
            if len(data) >= 2:
                volume_change = data['volume'].iloc[-1] / data['volume'].iloc[-2] - 1
                if volume_change > 2.0:  # 成交量增长200%
                    events.append({
                        'symbol': symbol,
                        'type': 'volume_spike',
                        'severity': 'medium',
                        'volume_change': volume_change,
                        'timestamp': datetime.now()
                    })
        
        return events
    
    def _estimate_correlation_risk(self, positions: Dict[str, PositionInfo]) -> float:
        """估算相关性风险（简化版）"""
        # 简化的相关性风险计算
        # 实际应用中需要使用历史价格数据计算相关系数
        symbols = list(positions.keys())
        
        # 基于品种分类估算相关性
        categories = {
            'metal': ['cu888.SHFE', 'al888.SHFE', 'zn888.SHFE', 'ni888.SHFE'],
            'black': ['rb888.SHFE', 'hc888.SHFE', 'i888.DCE', 'j888.DCE'],
            'energy': ['sc888.INE', 'bu888.SHFE', 'br888.SHFE', 'lu888.INE'],
            'chemical': ['TA888.CZCE', 'MA888.CZCE', 'PX888.CZCE', 'SH888.CZCE'],
            'agriculture': ['m888.DCE', 'y888.DCE', 'p888.DCE', 'c888.DCE']
        }
        
        max_correlation = 0.0
        for category, category_symbols in categories.items():
            category_positions = [s for s in symbols if s in category_symbols]
            if len(category_positions) > 1:
                # 同类品种相关性较高
                max_correlation = max(max_correlation, 0.8)
        
        return max_correlation
    
    def _calculate_volatility_risk(self, positions: Dict[str, PositionInfo]) -> float:
        """计算波动率风险"""
        if not positions:
            return 0.0
        
        # 加权平均ATR
        total_value = sum(pos.contract_value * pos.current_lots for pos in positions.values())
        if total_value == 0:
            return 0.0
        
        weighted_atr = sum(
            pos.atr_value * pos.contract_value * pos.current_lots 
            for pos in positions.values()
        ) / total_value
        
        # 标准化到0-5的范围
        return min(weighted_atr / 100, 5.0)
    
    def _calculate_tail_risk_score(self, positions: Dict[str, PositionInfo]) -> float:
        """计算尾部风险评分"""
        if not positions:
            return 0.0
        
        # 基于持仓集中度和风险暴露计算
        risk_concentration = max(pos.risk_percentage for pos in positions.values())
        total_risk = sum(pos.risk_percentage for pos in positions.values())
        
        # 集中度风险
        concentration_risk = risk_concentration / 0.02  # 标准化到单品种2%风险
        
        # 总风险暴露
        exposure_risk = total_risk / 0.15  # 标准化到15%总风险
        
        # 综合尾部风险评分
        tail_risk = (concentration_risk * 0.6 + exposure_risk * 0.4)
        
        return min(tail_risk, 1.0)
    
    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """计算ATR"""
        if len(data) < period:
            return 0.0
        
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = true_range.rolling(period).mean().iloc[-1]
        
        return atr if not np.isnan(atr) else 0.0
    
    def _get_adjustment_reason(self, position: PositionInfo, current_atr: float) -> str:
        """获取调整原因"""
        if current_atr > position.atr_value * 1.2:
            return "波动率上升，建议减仓"
        elif current_atr < position.atr_value * 0.8:
            return "波动率下降，可以增仓"
        else:
            return "风险参数调整"
    
    def _get_adjustment_priority(self, difference: int, position: PositionInfo) -> str:
        """获取调整优先级"""
        if abs(difference) >= 3:
            return "高"
        elif abs(difference) >= 2:
            return "中"
        else:
            return "低"
    
    def update_account_value(self, new_value: float):
        """更新账户净值"""
        self.account_value = new_value
    
    def get_risk_report(self) -> Dict:
        """生成风险报告"""
        return {
            'account_value': self.account_value,
            'risk_limits': {
                'max_single_risk': f"{self.risk_limits.max_single_instrument_risk:.1%}",
                'max_portfolio_risk': f"{self.risk_limits.max_portfolio_risk:.1%}",
                'daily_loss_limit': f"{self.risk_limits.daily_loss_limit:.1%}"
            },
            'recent_events': self.risk_events[-10:] if self.risk_events else [],
            'daily_pnl_summary': {
                'recent_days': len(self.daily_pnl_history),
                'avg_daily_pnl': np.mean([h['pnl'] for h in self.daily_pnl_history]) if self.daily_pnl_history else 0,
                'max_daily_loss': min([h['pnl'] for h in self.daily_pnl_history]) if self.daily_pnl_history else 0
            }
        }
