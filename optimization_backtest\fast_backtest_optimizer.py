"""
高效回测优化器 - 专门针对回测速度优化
解决回测慢的问题，提供多种加速策略

主要优化策略：
1. 数据预加载和缓存
2. 快速筛选机制
3. 分层优化策略
4. 智能采样
5. 结果缓存
6. 并行优化
"""

import time
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Callable
from concurrent.futures import ProcessPoolExecutor, as_completed
import pickle
import hashlib
import os
from dataclasses import dataclass
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FastBacktestConfig:
    """快速回测配置"""
    # 基础配置
    max_workers: int = 8
    cache_enabled: bool = True
    cache_dir: str = "backtest_cache"
    
    # 快速筛选配置
    enable_fast_filter: bool = True
    fast_filter_ratio: float = 0.3  # 快速筛选保留比例
    fast_filter_samples: int = 50   # 快速筛选样本数
    
    # 分层优化配置
    enable_layered_optimization: bool = True
    layer1_samples: int = 100       # 第一层：粗筛
    layer2_samples: int = 50        # 第二层：精筛
    layer3_samples: int = 20        # 第三层：精细优化
    
    # 数据优化配置
    enable_data_cache: bool = True
    preload_data: bool = True
    use_sample_data: bool = False   # 是否使用采样数据进行初筛
    sample_ratio: float = 0.3       # 数据采样比例
    
    # 性能优化配置
    timeout_seconds: int = 300      # 单次回测超时时间
    memory_limit_mb: int = 2048     # 内存限制
    enable_gc: bool = True          # 启用垃圾回收


class FastBacktestOptimizer:
    """高效回测优化器"""
    
    def __init__(self, 
                 backtester: Callable,
                 param_spaces: Dict[str, Tuple],
                 config: Optional[FastBacktestConfig] = None):
        """
        初始化高效回测优化器
        
        Args:
            backtester: 回测函数
            param_spaces: 参数空间
            config: 配置对象
        """
        self.backtester = backtester
        self.param_spaces = param_spaces
        self.config = config or FastBacktestConfig()
        
        # 初始化缓存
        self.cache_dir = Path(self.config.cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.result_cache = {}
        
        # 性能统计
        self.stats = {
            'total_evaluations': 0,
            'cache_hits': 0,
            'fast_filter_savings': 0,
            'total_time': 0.0
        }
        
        logger.info(f"FastBacktestOptimizer 初始化完成")
        logger.info(f"配置: 并行度={self.config.max_workers}, 缓存={'启用' if self.config.cache_enabled else '禁用'}")
    
    def _get_param_hash(self, params: Dict[str, Any]) -> str:
        """生成参数哈希值用于缓存"""
        param_str = str(sorted(params.items()))
        return hashlib.md5(param_str.encode()).hexdigest()
    
    def _load_cache(self) -> Dict:
        """加载缓存"""
        cache_file = self.cache_dir / "backtest_cache.pkl"
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.warning(f"缓存加载失败: {e}")
        return {}
    
    def _save_cache(self):
        """保存缓存"""
        if not self.config.cache_enabled:
            return
            
        cache_file = self.cache_dir / "backtest_cache.pkl"
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(self.result_cache, f)
        except Exception as e:
            logger.warning(f"缓存保存失败: {e}")
    
    def _evaluate_with_cache(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """带缓存的参数评估"""
        param_hash = self._get_param_hash(params)
        
        # 检查缓存
        if self.config.cache_enabled and param_hash in self.result_cache:
            self.stats['cache_hits'] += 1
            return self.result_cache[param_hash]
        
        # 执行回测
        try:
            result = self.backtester(params)
            
            # 保存到缓存
            if self.config.cache_enabled:
                self.result_cache[param_hash] = result
                
            self.stats['total_evaluations'] += 1
            return result
            
        except Exception as e:
            logger.warning(f"回测执行失败: {e}")
            return {'sharpe_ratio': -np.inf, 'error': str(e)}
    
    def _fast_filter_phase(self, param_combinations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """快速筛选阶段 - 使用简化回测快速排除明显不好的参数"""
        if not self.config.enable_fast_filter:
            return param_combinations
        
        logger.info(f"🚀 快速筛选阶段: {len(param_combinations)} -> {int(len(param_combinations) * self.config.fast_filter_ratio)}")
        
        # 随机采样进行快速评估
        sample_size = min(self.config.fast_filter_samples, len(param_combinations))
        sampled_params = np.random.choice(len(param_combinations), sample_size, replace=False)
        
        quick_results = []
        for idx in sampled_params:
            params = param_combinations[idx]
            # 这里可以使用简化的回测逻辑，比如更短的时间周期
            result = self._evaluate_with_cache(params)
            quick_results.append((idx, params, result.get('sharpe_ratio', -np.inf)))
        
        # 根据快速评估结果筛选
        quick_results.sort(key=lambda x: x[2], reverse=True)
        keep_count = int(len(param_combinations) * self.config.fast_filter_ratio)
        
        # 保留表现好的参数 + 一些随机参数（保持多样性）
        good_indices = set([x[0] for x in quick_results[:keep_count//2]])
        random_indices = set(np.random.choice(len(param_combinations), keep_count//2, replace=False))
        
        selected_indices = good_indices.union(random_indices)
        filtered_params = [param_combinations[i] for i in selected_indices]
        
        self.stats['fast_filter_savings'] += len(param_combinations) - len(filtered_params)
        
        logger.info(f"✅ 快速筛选完成，保留 {len(filtered_params)} 个参数组合")
        return filtered_params
    
    def _parallel_evaluate_batch(self, param_combinations: List[Dict[str, Any]]) -> List[Tuple[Dict[str, Any], Dict[str, Any]]]:
        """并行批量评估参数"""
        results = []
        
        if self.config.max_workers <= 1 or len(param_combinations) < 3:
            # 串行处理
            for params in param_combinations:
                result = self._evaluate_with_cache(params)
                results.append((params, result))
        else:
            # 并行处理
            with ProcessPoolExecutor(max_workers=self.config.max_workers) as executor:
                future_to_params = {
                    executor.submit(self._evaluate_with_cache, params): params
                    for params in param_combinations
                }
                
                for future in as_completed(future_to_params, timeout=self.config.timeout_seconds):
                    params = future_to_params[future]
                    try:
                        result = future.result()
                        results.append((params, result))
                    except Exception as e:
                        logger.warning(f"参数评估失败: {e}")
                        results.append((params, {'sharpe_ratio': -np.inf, 'error': str(e)}))
        
        return results
    
    def _generate_smart_samples(self, n_samples: int, layer: int = 1) -> List[Dict[str, Any]]:
        """智能参数采样 - 根据层级调整采样策略"""
        param_combinations = []
        
        for _ in range(n_samples):
            params = {}
            for param_name, (min_val, max_val, step, param_type) in self.param_spaces.items():
                
                if layer == 1:
                    # 第一层：粗采样，覆盖整个空间
                    value = np.random.uniform(min_val, max_val)
                elif layer == 2:
                    # 第二层：中等密度采样
                    center = (min_val + max_val) / 2
                    std = (max_val - min_val) / 4
                    value = np.random.normal(center, std)
                    value = np.clip(value, min_val, max_val)
                else:
                    # 第三层：精细采样，集中在优秀区域
                    center = (min_val + max_val) / 2
                    std = (max_val - min_val) / 6
                    value = np.random.normal(center, std)
                    value = np.clip(value, min_val, max_val)
                
                # 类型转换和步长对齐
                if param_type == int:
                    value = int(round(value / step) * step)
                    value = max(int(min_val), min(int(max_val), value))
                else:
                    value = round(value / step) * step
                    value = max(min_val, min(max_val, value))
                
                params[param_name] = value
            
            param_combinations.append(params)
        
        return param_combinations
    
    def layered_optimization(self, 
                           performance_metric: str = 'sharpe_ratio',
                           target_evaluations: int = 500) -> Dict[str, Any]:
        """
        分层优化策略
        
        Args:
            performance_metric: 性能指标
            target_evaluations: 目标评估次数
            
        Returns:
            Dict: 优化结果
        """
        start_time = time.time()
        logger.info(f"🚀 开始分层优化，目标评估次数: {target_evaluations}")
        
        # 加载缓存
        if self.config.cache_enabled:
            self.result_cache = self._load_cache()
            logger.info(f"缓存加载完成，已有 {len(self.result_cache)} 条记录")
        
        all_results = []
        
        # 第一层：粗筛 (30% 评估次数)
        layer1_samples = min(self.config.layer1_samples, target_evaluations // 3)
        logger.info(f"📊 第一层优化 - 粗筛阶段 ({layer1_samples} 样本)")
        
        layer1_params = self._generate_smart_samples(layer1_samples, layer=1)
        if self.config.enable_fast_filter:
            layer1_params = self._fast_filter_phase(layer1_params)
        
        layer1_results = self._parallel_evaluate_batch(layer1_params)
        all_results.extend(layer1_results)
        
        # 选择第一层最佳结果
        layer1_results.sort(key=lambda x: x[1].get(performance_metric, -np.inf), reverse=True)
        top_layer1 = layer1_results[:self.config.layer2_samples//2]
        
        logger.info(f"✅ 第一层完成，最佳性能: {top_layer1[0][1].get(performance_metric, 0):.4f}")
        
        # 第二层：精筛 (40% 评估次数)
        layer2_samples = min(self.config.layer2_samples, target_evaluations // 2)
        logger.info(f"🎯 第二层优化 - 精筛阶段 ({layer2_samples} 样本)")
        
        # 基于第一层结果生成第二层参数
        layer2_params = self._generate_smart_samples(layer2_samples, layer=2)
        layer2_results = self._parallel_evaluate_batch(layer2_params)
        all_results.extend(layer2_results)
        
        # 合并前两层结果
        combined_results = all_results.copy()
        combined_results.sort(key=lambda x: x[1].get(performance_metric, -np.inf), reverse=True)
        top_combined = combined_results[:self.config.layer3_samples//2]
        
        logger.info(f"✅ 第二层完成，最佳性能: {top_combined[0][1].get(performance_metric, 0):.4f}")
        
        # 第三层：精细优化 (30% 评估次数)
        layer3_samples = min(self.config.layer3_samples, target_evaluations - len(all_results))
        if layer3_samples > 0:
            logger.info(f"🔬 第三层优化 - 精细阶段 ({layer3_samples} 样本)")
            
            layer3_params = self._generate_smart_samples(layer3_samples, layer=3)
            layer3_results = self._parallel_evaluate_batch(layer3_params)
            all_results.extend(layer3_results)
        
        # 最终结果排序
        all_results.sort(key=lambda x: x[1].get(performance_metric, -np.inf), reverse=True)
        best_result = all_results[0]
        
        # 保存缓存
        if self.config.cache_enabled:
            self._save_cache()
        
        # 统计信息
        total_time = time.time() - start_time
        self.stats['total_time'] = total_time
        
        logger.info(f"🎉 分层优化完成!")
        logger.info(f"📈 最佳性能: {best_result[1].get(performance_metric, 0):.4f}")
        logger.info(f"⏱️  总耗时: {total_time:.2f}秒")
        logger.info(f"📊 评估统计: 总计={self.stats['total_evaluations']}, 缓存命中={self.stats['cache_hits']}")
        logger.info(f"🚀 快筛节省: {self.stats['fast_filter_savings']} 次评估")
        
        return {
            'best_params': best_result[0],
            'best_performance': best_result[1].get(performance_metric, 0),
            'best_result': best_result[1],
            'all_results': all_results[:50],  # 返回前50个结果
            'optimization_time': total_time,
            'stats': self.stats.copy(),
            'total_evaluations': len(all_results)
        }


def create_fast_optimizer(backtester: Callable, 
                         param_spaces: Dict[str, Tuple],
                         max_workers: int = 8,
                         enable_cache: bool = True,
                         enable_fast_filter: bool = True) -> FastBacktestOptimizer:
    """
    创建快速回测优化器的便捷函数
    
    Args:
        backtester: 回测函数
        param_spaces: 参数空间
        max_workers: 最大并行数
        enable_cache: 是否启用缓存
        enable_fast_filter: 是否启用快速筛选
        
    Returns:
        FastBacktestOptimizer: 优化器实例
    """
    config = FastBacktestConfig(
        max_workers=max_workers,
        cache_enabled=enable_cache,
        enable_fast_filter=enable_fast_filter
    )
    
    return FastBacktestOptimizer(backtester, param_spaces, config)
