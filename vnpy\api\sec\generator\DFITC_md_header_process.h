void processFrontConnected(Task *task);

void processFrontDisconnected(Task *task);

void processRtnNotice(Task *task);

void processRspError(Task *task);

void processRspStockUserLogin(Task *task);

void processRspStockUserLogout(Task *task);

void processRspSOPUserLogin(Task *task);

void processRspSOPUserLogout(Task *task);

void processRspFASLUserLogin(Task *task);

void processRspFASLUserLogout(Task *task);

void processRspStockSubMarketData(Task *task);

void processRspStockUnSubMarketData(Task *task);

void processRspSOPSubMarketData(Task *task);

void processRspSOPUnSubMarketData(Task *task);

void processStockMarketData(Task *task);

void processSOPMarketData(Task *task);

void processRspStockAvailableQuot(Task *task);

void processRspSopAvailableQuot(Task *task);

void processRspUserMDPasswordUpdate(Task *task);

