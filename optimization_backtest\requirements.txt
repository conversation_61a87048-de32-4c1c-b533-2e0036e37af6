# 参数优化回测系统依赖包配置
# 使用命令安装：pip install -r requirements.txt

# 核心科学计算库
numpy>=1.21.0
pandas>=1.3.0

# 机器学习库（用于参数重要性分析）
scikit-learn>=1.0.0

# 统计和采样库（用于Sobol序列、拉丁超立方采样）
scipy>=1.7.0

# 贝叶斯优化库（可选，但推荐）
scikit-optimize>=0.9.0

# 并行处理增强
joblib>=1.1.0

# 数据可视化（可选，用于结果分析）
matplotlib>=3.4.0
seaborn>=0.11.0

# 进度条显示
tqdm>=4.62.0

# Excel文件读取
openpyxl>=3.0.7
xlrd>=2.0.1

# 日志和配置
pyyaml>=5.4.0

# VnPy相关（如果使用真实策略）
# vnpy>=3.0.0
# vnpy-ctp>=6.6.1
# vnpy-ctastrategy>=1.0.0

# 注意：
# 1. 如果系统已安装VnPy，无需重复安装vnpy相关包
# 2. scikit-optimize可能需要额外的依赖，如果安装失败可以跳过
# 3. 所有可选包都可以单独安装，不影响核心功能 