.def("reqStockUserLogin", &TdApi::reqStockUserLogin)
.def("reqStockUserLogout", &TdApi::reqStockUserLogout)
.def("reqStockUserPasswordUpdate", &TdApi::reqStockUserPasswordUpdate)
.def("reqStockEntrustOrder", &TdApi::reqStockEntrustOrder)
.def("reqStockWithdrawOrder", &TdApi::reqStockWithdrawOrder)
.def("reqStockQryEntrustOrder", &TdApi::reqStockQryEntrustOrder)
.def("reqStockQryRealTimeTrade", &TdApi::reqStockQryRealTimeTrade)
.def("reqStockQrySerialTrade", &TdApi::reqStockQrySerialTrade)
.def("reqStockQryPosition", &TdApi::reqStockQryPosition)
.def("reqStockQryCapitalAccountInfo", &TdApi::reqStockQryCapitalAccountInfo)
.def("reqStockQryAccountInfo", &TdApi::reqStockQryAccountInfo)
.def("reqStockQryShareholderInfo", &TdApi::reqStockQryShareholderInfo)
.def("reqStockTransferFunds", &TdApi::reqStockTransferFunds)
.def("reqStockEntrustBatchOrder", &TdApi::reqStockEntrustBatchOrder)
.def("reqStockWithdrawBatchOrder", &TdApi::reqStockWithdrawBatchOrder)
.def("reqStockCalcAbleEntrustQty", &TdApi::reqStockCalcAbleEntrustQty)
.def("reqStockCalcAblePurchaseETFQty", &TdApi::reqStockCalcAblePurchaseETFQty)
.def("reqStockQryFreezeFundsDetail", &TdApi::reqStockQryFreezeFundsDetail)
.def("reqStockQryFreezeStockDetail", &TdApi::reqStockQryFreezeStockDetail)
.def("reqStockQryTransferFundsDetail", &TdApi::reqStockQryTransferFundsDetail)
.def("reqStockQryTransferStockDetail", &TdApi::reqStockQryTransferStockDetail)
.def("reqStockQryStockInfo", &TdApi::reqStockQryStockInfo)
.def("reqStockQryStockStaticInfo", &TdApi::reqStockQryStockStaticInfo)
.def("reqStockQryTradeTime", &TdApi::reqStockQryTradeTime)
.def("reqSOPUserLogin", &TdApi::reqSOPUserLogin)
.def("reqSOPUserLogout", &TdApi::reqSOPUserLogout)
.def("reqSOPUserPasswordUpdate", &TdApi::reqSOPUserPasswordUpdate)
.def("reqSOPEntrustOrder", &TdApi::reqSOPEntrustOrder)
.def("reqSOPQuoteEntrustOrder", &TdApi::reqSOPQuoteEntrustOrder)
.def("reqSOPGroupSplit", &TdApi::reqSOPGroupSplit)
.def("reqSOPGroupExectueOrder", &TdApi::reqSOPGroupExectueOrder)
.def("reqSOPQryGroupPosition", &TdApi::reqSOPQryGroupPosition)
.def("reqSOPLockOUnLockStock", &TdApi::reqSOPLockOUnLockStock)
.def("reqSOPWithdrawOrder", &TdApi::reqSOPWithdrawOrder)
.def("reqSOPQryEntrustOrder", &TdApi::reqSOPQryEntrustOrder)
.def("reqSOPQrySerialTrade", &TdApi::reqSOPQrySerialTrade)
.def("reqSOPQryPosition", &TdApi::reqSOPQryPosition)
.def("reqSOPQryCollateralPosition", &TdApi::reqSOPQryCollateralPosition)
.def("reqSOPQryCapitalAccountInfo", &TdApi::reqSOPQryCapitalAccountInfo)
.def("reqSOPQryAccountInfo", &TdApi::reqSOPQryAccountInfo)
.def("reqSOPQryShareholderInfo", &TdApi::reqSOPQryShareholderInfo)
.def("reqSOPCalcAbleEntrustQty", &TdApi::reqSOPCalcAbleEntrustQty)
.def("reqSOPQryAbleLockStock", &TdApi::reqSOPQryAbleLockStock)
.def("reqSOPQryContactInfo", &TdApi::reqSOPQryContactInfo)
.def("reqSOPExectueOrder", &TdApi::reqSOPExectueOrder)
.def("reqSOPQryExecAssiInfo", &TdApi::reqSOPQryExecAssiInfo)
.def("reqSOPQryTradeTime", &TdApi::reqSOPQryTradeTime)
.def("reqSOPQryExchangeInfo", &TdApi::reqSOPQryExchangeInfo)
.def("reqSOPQryCommission", &TdApi::reqSOPQryCommission)
.def("reqSOPQryDeposit", &TdApi::reqSOPQryDeposit)
.def("reqSOPQryContractObjectInfo", &TdApi::reqSOPQryContractObjectInfo)
.def("reqFASLUserLogin", &TdApi::reqFASLUserLogin)
.def("reqFASLUserLogout", &TdApi::reqFASLUserLogout)
.def("reqFASLQryAbleFinInfo", &TdApi::reqFASLQryAbleFinInfo)
.def("reqFASLQryAbleSloInfo", &TdApi::reqFASLQryAbleSloInfo)
.def("reqFASLTransferCollateral", &TdApi::reqFASLTransferCollateral)
.def("reqFASLDirectRepayment", &TdApi::reqFASLDirectRepayment)
.def("reqFASLRepayStockTransfer", &TdApi::reqFASLRepayStockTransfer)
.def("reqFASLEntrustCrdtOrder", &TdApi::reqFASLEntrustCrdtOrder)
.def("reqFASLEntrsuctOrder", &TdApi::reqFASLEntrsuctOrder)
.def("reqFASLWithdrawOrder", &TdApi::reqFASLWithdrawOrder)
.def("reqFASLCalcAbleEntrustCrdtQty", &TdApi::reqFASLCalcAbleEntrustCrdtQty)
.def("reqFASLQryCrdtFunds", &TdApi::reqFASLQryCrdtFunds)
.def("reqFASLQryCrdtContract", &TdApi::reqFASLQryCrdtContract)
.def("reqFASLQryCrdtConChangeInfo", &TdApi::reqFASLQryCrdtConChangeInfo)
.def("reqFASLTransferFunds", &TdApi::reqFASLTransferFunds)
.def("reqFASLQryAccountInfo", &TdApi::reqFASLQryAccountInfo)
.def("reqFASLQryCapitalAccountInfo", &TdApi::reqFASLQryCapitalAccountInfo)
.def("reqFASLQryShareholderInfo", &TdApi::reqFASLQryShareholderInfo)
.def("reqFASLQryPosition", &TdApi::reqFASLQryPosition)
.def("reqFASLQryEntrustOrder", &TdApi::reqFASLQryEntrustOrder)
.def("reqFASLQrySerialTrade", &TdApi::reqFASLQrySerialTrade)
.def("reqFASLQryRealTimeTrade", &TdApi::reqFASLQryRealTimeTrade)
.def("reqFASLQryFreezeFundsDetail", &TdApi::reqFASLQryFreezeFundsDetail)
.def("reqFASLQryFreezeStockDetail", &TdApi::reqFASLQryFreezeStockDetail)
.def("reqFASLQryTransferFundsDetail", &TdApi::reqFASLQryTransferFundsDetail)
.def("reqFASLQrySystemTime", &TdApi::reqFASLQrySystemTime)
.def("reqFASLQryTransferredContract", &TdApi::reqFASLQryTransferredContract)
.def("reqFASLDesirableFundsOut", &TdApi::reqFASLDesirableFundsOut)
.def("reqFASLQryGuaranteedContract", &TdApi::reqFASLQryGuaranteedContract)
.def("reqFASLQryUnderlyingContract", &TdApi::reqFASLQryUnderlyingContract)

.def("onFrontConnected", &TdApi::onFrontConnected)
.def("onFrontDisconnected", &TdApi::onFrontDisconnected)
.def("onRtnNotice", &TdApi::onRtnNotice)
.def("onRspError", &TdApi::onRspError)
.def("onRspStockUserLogin", &TdApi::onRspStockUserLogin)
.def("onRspStockUserLogout", &TdApi::onRspStockUserLogout)
.def("onRspStockUserPasswordUpdate", &TdApi::onRspStockUserPasswordUpdate)
.def("onRspStockEntrustOrder", &TdApi::onRspStockEntrustOrder)
.def("onRspStockWithdrawOrder", &TdApi::onRspStockWithdrawOrder)
.def("onRspStockQryEntrustOrder", &TdApi::onRspStockQryEntrustOrder)
.def("onRspStockQryRealTimeTrade", &TdApi::onRspStockQryRealTimeTrade)
.def("onRspStockQrySerialTrade", &TdApi::onRspStockQrySerialTrade)
.def("onRspStockQryPosition", &TdApi::onRspStockQryPosition)
.def("onRspStockQryCapitalAccountInfo", &TdApi::onRspStockQryCapitalAccountInfo)
.def("onRspStockQryAccountInfo", &TdApi::onRspStockQryAccountInfo)
.def("onRspStockQryShareholderInfo", &TdApi::onRspStockQryShareholderInfo)
.def("onRspStockTransferFunds", &TdApi::onRspStockTransferFunds)
.def("onRspStockEntrustBatchOrder", &TdApi::onRspStockEntrustBatchOrder)
.def("onRspStockWithdrawBatchOrder", &TdApi::onRspStockWithdrawBatchOrder)
.def("onRspStockCalcAbleEntrustQty", &TdApi::onRspStockCalcAbleEntrustQty)
.def("onRspStockCalcAblePurchaseETFQty", &TdApi::onRspStockCalcAblePurchaseETFQty)
.def("onRspStockQryFreezeFundsDetail", &TdApi::onRspStockQryFreezeFundsDetail)
.def("onRspStockQryFreezeStockDetail", &TdApi::onRspStockQryFreezeStockDetail)
.def("onRspStockQryTransferStockDetail", &TdApi::onRspStockQryTransferStockDetail)
.def("onRspStockQryTransferFundsDetail", &TdApi::onRspStockQryTransferFundsDetail)
.def("onRspStockQryStockInfo", &TdApi::onRspStockQryStockInfo)
.def("onRspStockQryStockStaticInfo", &TdApi::onRspStockQryStockStaticInfo)
.def("onRspStockQryTradeTime", &TdApi::onRspStockQryTradeTime)
.def("onStockEntrustOrderRtn", &TdApi::onStockEntrustOrderRtn)
.def("onStockTradeRtn", &TdApi::onStockTradeRtn)
.def("onStockWithdrawOrderRtn", &TdApi::onStockWithdrawOrderRtn)
.def("onRspSOPUserLogin", &TdApi::onRspSOPUserLogin)
.def("onRspSOPUserLogout", &TdApi::onRspSOPUserLogout)
.def("onRspSOPUserPasswordUpdate", &TdApi::onRspSOPUserPasswordUpdate)
.def("onRspSOPEntrustOrder", &TdApi::onRspSOPEntrustOrder)
.def("onRspSOPQuoteEntrustOrder", &TdApi::onRspSOPQuoteEntrustOrder)
.def("onRspSOPGroupSplit", &TdApi::onRspSOPGroupSplit)
.def("onRspSOPGroupExectueOrder", &TdApi::onRspSOPGroupExectueOrder)
.def("onRspSOPQryGroupPosition", &TdApi::onRspSOPQryGroupPosition)
.def("onRspSOPLockOUnLockStock", &TdApi::onRspSOPLockOUnLockStock)
.def("onRspSOPWithdrawOrder", &TdApi::onRspSOPWithdrawOrder)
.def("onRspSOPQryEntrustOrder", &TdApi::onRspSOPQryEntrustOrder)
.def("onRspSOPQrySerialTrade", &TdApi::onRspSOPQrySerialTrade)
.def("onRspSOPQryPosition", &TdApi::onRspSOPQryPosition)
.def("onRspSOPQryCollateralPosition", &TdApi::onRspSOPQryCollateralPosition)
.def("onRspSOPQryCapitalAccountInfo", &TdApi::onRspSOPQryCapitalAccountInfo)
.def("onRspSOPQryAccountInfo", &TdApi::onRspSOPQryAccountInfo)
.def("onRspSOPQryShareholderInfo", &TdApi::onRspSOPQryShareholderInfo)
.def("onRspSOPCalcAbleEntrustQty", &TdApi::onRspSOPCalcAbleEntrustQty)
.def("onRspSOPQryAbleLockStock", &TdApi::onRspSOPQryAbleLockStock)
.def("onRspSOPQryContactInfo", &TdApi::onRspSOPQryContactInfo)
.def("onRspSOPExectueOrder", &TdApi::onRspSOPExectueOrder)
.def("onRspSOPQryExecAssiInfo", &TdApi::onRspSOPQryExecAssiInfo)
.def("onRspSOPQryTradeTime", &TdApi::onRspSOPQryTradeTime)
.def("onRspSOPQryExchangeInfo", &TdApi::onRspSOPQryExchangeInfo)
.def("onRspSOPQryCommission", &TdApi::onRspSOPQryCommission)
.def("onRspSOPQryDeposit", &TdApi::onRspSOPQryDeposit)
.def("onRspSOPQryContractObjectInfo", &TdApi::onRspSOPQryContractObjectInfo)
.def("onSOPEntrustOrderRtn", &TdApi::onSOPEntrustOrderRtn)
.def("onSOPTradeRtn", &TdApi::onSOPTradeRtn)
.def("onSOPWithdrawOrderRtn", &TdApi::onSOPWithdrawOrderRtn)
.def("onRspFASLUserLogin", &TdApi::onRspFASLUserLogin)
.def("onRspFASLUserLogout", &TdApi::onRspFASLUserLogout)
.def("onRspFASLQryAbleFinInfo", &TdApi::onRspFASLQryAbleFinInfo)
.def("onRspFASLQryAbleSloInfo", &TdApi::onRspFASLQryAbleSloInfo)
.def("onRspFASLTransferCollateral", &TdApi::onRspFASLTransferCollateral)
.def("onRspFASLDirectRepayment", &TdApi::onRspFASLDirectRepayment)
.def("onRspFASLRepayStockTransfer", &TdApi::onRspFASLRepayStockTransfer)
.def("onRspFASLEntrustCrdtOrder", &TdApi::onRspFASLEntrustCrdtOrder)
.def("onRspFASLEntrustOrder", &TdApi::onRspFASLEntrustOrder)
.def("onRspFASLCalcAbleEntrustCrdtQty", &TdApi::onRspFASLCalcAbleEntrustCrdtQty)
.def("onRspFASLQryCrdtFunds", &TdApi::onRspFASLQryCrdtFunds)
.def("onRspFASLQryCrdtContract", &TdApi::onRspFASLQryCrdtContract)
.def("onRspFASLQryCrdtConChangeInfo", &TdApi::onRspFASLQryCrdtConChangeInfo)
.def("onRspFASLTransferFunds", &TdApi::onRspFASLTransferFunds)
.def("onRspFASLQryAccountInfo", &TdApi::onRspFASLQryAccountInfo)
.def("onRspFASLQryCapitalAccountInfo", &TdApi::onRspFASLQryCapitalAccountInfo)
.def("onRspFASLQryShareholderInfo", &TdApi::onRspFASLQryShareholderInfo)
.def("onRspFASLQryPosition", &TdApi::onRspFASLQryPosition)
.def("onRspFASLQryEntrustOrder", &TdApi::onRspFASLQryEntrustOrder)
.def("onRspFASLQrySerialTrade", &TdApi::onRspFASLQrySerialTrade)
.def("onRspFASLQryRealTimeTrade", &TdApi::onRspFASLQryRealTimeTrade)
.def("onRspFASLQryFreezeFundsDetail", &TdApi::onRspFASLQryFreezeFundsDetail)
.def("onRspFASLQryFreezeStockDetail", &TdApi::onRspFASLQryFreezeStockDetail)
.def("onRspFASLQryTransferFundsDetail", &TdApi::onRspFASLQryTransferFundsDetail)
.def("onRspFASLWithdrawOrder", &TdApi::onRspFASLWithdrawOrder)
.def("onRspFASLQrySystemTime", &TdApi::onRspFASLQrySystemTime)
.def("onRspFASLQryTransferredContract", &TdApi::onRspFASLQryTransferredContract)
.def("onRspFASLDesirableFundsOut", &TdApi::onRspFASLDesirableFundsOut)
.def("onRspFASLQryGuaranteedContract", &TdApi::onRspFASLQryGuaranteedContract)
.def("onRspFASLQryUnderlyingContract", &TdApi::onRspFASLQryUnderlyingContract)
.def("onFASLEntrustOrderRtn", &TdApi::onFASLEntrustOrderRtn)
.def("onFASLTradeRtn", &TdApi::onFASLTradeRtn)
.def("onFASLWithdrawOrderRtn", &TdApi::onFASLWithdrawOrderRtn)
;
