#!/usr/bin/env python3
"""
智能期货交易系统GUI启动器
GUI Launcher for Intelligent Futures Trading System

专门用于启动图形界面的脚本
"""

import sys
import os
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_gui_requirements():
    """检查GUI运行要求"""
    print("🔧 检查GUI运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    print(f"  ✅ Python {sys.version.split()[0]}")
    
    # 检查tkinter
    try:
        import tkinter
        _ = tkinter
        print("  ✅ tkinter可用")
    except ImportError:
        print("  ❌ tkinter不可用，无法启动GUI")
        print("  💡 请安装tkinter: sudo apt-get install python3-tk (Linux)")
        return False
    
    # 检查核心模块
    try:
        from core.data_manager import DataManager
        print("  ✅ 核心模块可用")
    except ImportError as e:
        print(f"  ❌ 核心模块导入失败: {e}")
        return False
    
    return True

def start_simple_gui():
    """启动简化GUI"""
    print("\n🖥️  启动简化GUI界面...")
    
    try:
        from simple_gui import SimpleTradingGUI
        
        print("  🎨 创建简化图形界面...")
        app = SimpleTradingGUI()
        
        print("  🚀 启动GUI界面...")
        print("  💡 GUI界面已打开，请在新窗口中操作")
        print("  📖 使用说明:")
        print("    - 功能演示: 测试各个核心功能")
        print("    - 策略回测: 运行策略回测分析")
        print("    - 策略管理: 加载和管理交易策略")
        print("    - 系统日志: 查看系统运行日志")
        
        app.run()
        return True
        
    except Exception as e:
        print(f"❌ 简化GUI启动失败: {e}")
        traceback.print_exc()
        return False

def start_full_gui():
    """启动完整GUI"""
    print("\n🖥️  启动完整GUI界面...")
    
    try:
        # 检查matplotlib
        try:
            import matplotlib
            _ = matplotlib
            print("  ✅ matplotlib可用，支持图表功能")
        except ImportError:
            print("  ⚠️  matplotlib不可用，图表功能将受限")
        
        from gui_interface import TradingSystemGUI
        
        print("  🎨 创建完整图形界面...")
        app = TradingSystemGUI()
        
        print("  🚀 启动GUI界面...")
        print("  💡 完整GUI界面已打开，包含所有功能")
        
        app.run()
        return True
        
    except Exception as e:
        print(f"❌ 完整GUI启动失败: {e}")
        print("  🔄 尝试启动简化GUI...")
        return start_simple_gui()

def main():
    """主函数"""
    print("🚀 智能期货交易系统 GUI启动器")
    print("="*60)
    
    # 检查环境
    if not check_gui_requirements():
        input("\n按回车键退出...")
        return
    
    # 选择GUI版本
    print(f"\n{'='*60}")
    print("请选择GUI版本:")
    print("1. 简化GUI (推荐，稳定性好)")
    print("2. 完整GUI (功能丰富，需要matplotlib)")
    print("3. 自动选择 (先尝试完整GUI，失败则使用简化GUI)")
    print("="*60)
    
    try:
        choice = input("请输入选择 (1-3, 默认1): ").strip()
        
        if choice == "" or choice == "1":
            success = start_simple_gui()
        elif choice == "2":
            success = start_full_gui()
        elif choice == "3":
            print("🔄 自动选择GUI版本...")
            success = start_full_gui()  # 会自动降级到简化GUI
        else:
            print("❌ 无效选择")
            return
        
        if success:
            print("\n✅ GUI已正常关闭")
        else:
            print("\n❌ GUI启动失败")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
