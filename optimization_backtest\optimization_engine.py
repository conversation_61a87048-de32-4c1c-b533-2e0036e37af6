"""
参数优化回测引擎
实现分层参数优化：参数重要性分析 -> 粗搜索 -> 贝叶斯优化 -> 精细搜索 -> 鲁棒性验证
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Any, Callable, Optional
from concurrent.futures import ProcessPoolExecutor, as_completed
import itertools
import warnings
warnings.filterwarnings('ignore')

# 必需的机器学习库
try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: sklearn未安装，将使用简化的参数重要性分析")

# 采样库
try:
    from scipy.stats import qmc
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("警告: scipy未安装，将使用均匀随机采样代替Sobol序列")

# 贝叶斯优化库
try:
    import skopt
    from skopt import gp_minimize, forest_minimize
    from skopt.space import Real, Integer
    from skopt.utils import use_named_args
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False
    # 定义占位符类以避免运行时错误
    class Real:
        def __init__(self, low, high, name=None, **kwargs):
            self.name = name
            self.low = low
            self.high = high
    class Integer:
        def __init__(self, low, high, name=None, **kwargs):
            self.name = name
            self.low = low
            self.high = high
    def use_named_args(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def gp_minimize(*args, **kwargs):
        # 占位符函数，永远不会被调用，因为会在 SKOPT_AVAILABLE 检查时跳过
        raise NotImplementedError("scikit-optimize not available")
    print("警告: scikit-optimize未安装，将跳过贝叶斯优化步骤")

from vnpy.app.cta_strategy.backtesting import BacktestingEngine


class OptimizationLogger:
    """优化过程日志记录器"""
    
    def __init__(self, log_file: str = None):
        self.logger = logging.getLogger('optimization')
        self.logger.setLevel(logging.INFO)
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if log_file:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def info(self, message: str):
        self.logger.info(message)
    
    def warning(self, message: str):
        self.logger.warning(message)
    
    def error(self, message: str):
        self.logger.error(message)


class EarlyStopper:
    """早停机制实现"""
    
    def __init__(self, patience: int = 20, min_improvement: float = 0.005):
        self.patience = patience
        self.min_improvement = min_improvement
        self.counter = 0
        self.best_score = -float('inf')
    
    def __call__(self, current_score: float) -> bool:
        """
        检查是否应该停止迭代
        
        Args:
            current_score: 当前得分
            
        Returns:
            bool: True表示应该停止，False表示继续
        """
        if current_score > self.best_score + self.min_improvement:
            self.best_score = current_score
            self.counter = 0
            return False  # 继续迭代
        else:
            self.counter += 1
            if self.counter >= self.patience:
                return True  # 触发停止
            return False


class ParameterSampler:
    """参数采样器"""
    
    @staticmethod
    def constrain_params(params: Dict, param_spaces: Dict[str, Tuple]) -> Dict:
        """
        约束参数在定义范围内
        
        Args:
            params: 原始参数字典
            param_spaces: 参数空间定义
            
        Returns:
            Dict: 约束后的参数字典
        """
        constrained_params = {}
        
        for param_name, value in params.items():
            if param_name in param_spaces:
                min_val, max_val = param_spaces[param_name][:2]
                step = param_spaces[param_name][2] if len(param_spaces[param_name]) > 2 else None
                
                # 约束到范围内
                constrained_value = max(min_val, min(max_val, value))
                
                # 如果有步长定义，对齐到步长
                if step is not None:
                    if isinstance(step, int) or (isinstance(step, float) and step == int(step)):
                        # 整数参数
                        constrained_value = int(round(constrained_value / step) * step)
                    else:
                        # 浮点参数
                        constrained_value = round(constrained_value / step) * step
                
                # 再次确保在范围内（避免舍入误差）
                constrained_value = max(min_val, min(max_val, constrained_value))
                constrained_params[param_name] = constrained_value
            else:
                # 未定义的参数直接保留
                constrained_params[param_name] = value
        
        return constrained_params
    
    @staticmethod
    def validate_params_range(params: Dict, param_spaces: Dict[str, Tuple]) -> Tuple[bool, List[str]]:
        """
        验证参数是否在定义范围内
        
        Args:
            params: 参数字典
            param_spaces: 参数空间定义
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 违规参数列表)
        """
        violations = []
        
        for param_name, value in params.items():
            if param_name in param_spaces:
                min_val, max_val = param_spaces[param_name][:2]
                
                if value < min_val or value > max_val:
                    violations.append(f"{param_name}: {value} 不在范围 [{min_val}, {max_val}] 内")
        
        return len(violations) == 0, violations
    
    @staticmethod
    def sobol_sample(param_spaces: Dict[str, Tuple], n_samples: int) -> List[Dict]:
        """
        使用Sobol序列生成参数样本
        
        Args:
            param_spaces: 参数空间定义 {param_name: (min_val, max_val, step)}
            n_samples: 样本数量
            
        Returns:
            List[Dict]: 参数样本列表
        """
        if not SCIPY_AVAILABLE:
            return ParameterSampler.uniform_sample(param_spaces, n_samples)
        
        param_names = list(param_spaces.keys())
        n_params = len(param_names)
        
        # 创建Sobol序列采样器
        sampler = qmc.Sobol(d=n_params, scramble=True)
        samples = sampler.random(n_samples)
        
        # 将[0,1]区间的样本映射到实际参数范围
        param_samples = []
        for sample in samples:
            param_dict = {}
            for i, param_name in enumerate(param_names):
                min_val, max_val = param_spaces[param_name][:2]
                
                # 处理整数参数
                if len(param_spaces[param_name]) > 2:
                    step = param_spaces[param_name][2]
                    if isinstance(step, int) or (isinstance(step, float) and step.is_integer()):
                        param_value = int(min_val + sample[i] * (max_val - min_val))
                        param_value = max(min_val, min(max_val, param_value))
                    else:
                        param_value = min_val + sample[i] * (max_val - min_val)
                else:
                    param_value = min_val + sample[i] * (max_val - min_val)
                
                param_dict[param_name] = param_value
            
            # 确保参数在范围内
            param_dict = ParameterSampler.constrain_params(param_dict, param_spaces)
            param_samples.append(param_dict)
        
        return param_samples
    
    @staticmethod
    def uniform_sample(param_spaces: Dict[str, Tuple], n_samples: int) -> List[Dict]:
        """
        均匀随机采样（备选方案）
        """
        param_samples = []
        for _ in range(n_samples):
            param_dict = {}
            for param_name, space_def in param_spaces.items():
                min_val, max_val = space_def[:2]
                
                if len(space_def) > 2:
                    step = space_def[2]
                    if isinstance(step, int) or (isinstance(step, float) and step.is_integer()):
                        param_value = np.random.randint(min_val, max_val + 1)
                    else:
                        param_value = np.random.uniform(min_val, max_val)
                else:
                    param_value = np.random.uniform(min_val, max_val)
                
                param_dict[param_name] = param_value
            
            # 确保参数在范围内
            param_dict = ParameterSampler.constrain_params(param_dict, param_spaces)
            param_samples.append(param_dict)
        
        return param_samples
    
    @staticmethod
    def lhs_sample(param_spaces: Dict[str, Tuple], n_samples: int, 
                   importance_weights: Dict[str, float] = None) -> List[Dict]:
        """
        拉丁超立方采样
        
        Args:
            param_spaces: 参数空间定义
            n_samples: 样本数量
            importance_weights: 参数重要性权重
            
        Returns:
            List[Dict]: 参数样本列表
        """
        if not SCIPY_AVAILABLE:
            return ParameterSampler.uniform_sample(param_spaces, n_samples)
        
        param_names = list(param_spaces.keys())
        n_params = len(param_names)
        
        # 创建拉丁超立方采样器（处理版本兼容性）
        try:
            # 尝试使用新版本的参数
            sampler = qmc.LatinHypercube(d=n_params, scramble=True)
        except TypeError:
            # 如果不支持scramble参数，使用旧版本的方式
            try:
                sampler = qmc.LatinHypercube(d=n_params, centered=False)
            except TypeError:
                # 如果都不支持，使用最基本的方式
                sampler = qmc.LatinHypercube(d=n_params)
        
        samples = sampler.random(n_samples)
        
        # 如果有重要性权重，调整采样分布
        if importance_weights:
            for i, param_name in enumerate(param_names):
                weight = importance_weights.get(param_name, 1.0)
                # 重要参数在中心区域采样更密集
                if weight > 0.5:
                    samples[:, i] = 0.5 + 0.5 * (samples[:, i] - 0.5) * (2 - weight)
        
        # 映射到实际参数范围
        param_samples = []
        for sample in samples:
            param_dict = {}
            for i, param_name in enumerate(param_names):
                min_val, max_val = param_spaces[param_name][:2]
                
                if len(param_spaces[param_name]) > 2:
                    step = param_spaces[param_name][2]
                    if isinstance(step, int) or (isinstance(step, float) and step.is_integer()):
                        param_value = int(min_val + sample[i] * (max_val - min_val))
                        param_value = max(min_val, min(max_val, param_value))
                    else:
                        param_value = min_val + sample[i] * (max_val - min_val)
                else:
                    param_value = min_val + sample[i] * (max_val - min_val)
                
                param_dict[param_name] = param_value
            
            # 确保参数在范围内
            param_dict = ParameterSampler.constrain_params(param_dict, param_spaces)
            param_samples.append(param_dict)
        
        return param_samples


class ParameterImportanceAnalyzer:
    """参数重要性分析器"""
    
    @staticmethod
    def analyze_importance(param_samples: List[Dict], performance_scores: List[float]) -> Dict[str, float]:
        """
        分析参数重要性
        
        Args:
            param_samples: 参数样本列表
            performance_scores: 对应的性能得分列表
            
        Returns:
            Dict[str, float]: 参数重要性字典
        """
        if not SKLEARN_AVAILABLE:
            # 简化的重要性分析：计算相关性
            return ParameterImportanceAnalyzer._simple_correlation_analysis(param_samples, performance_scores)
        
        # 准备数据
        param_names = list(param_samples[0].keys())
        X = np.array([[sample[param] for param in param_names] for sample in param_samples])
        y = np.array(performance_scores)
        
        # 移除无效值
        valid_indices = ~np.isnan(y) & ~np.isinf(y)
        X = X[valid_indices]
        y = y[valid_indices]
        
        if len(X) < 10:  # 样本太少
            return {param: 1.0 / len(param_names) for param in param_names}
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 使用随机森林分析重要性
        rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(X_scaled, y)
        
        # 获取特征重要性
        importance_scores = rf.feature_importances_
        importance_dict = {param_names[i]: float(importance_scores[i]) for i in range(len(param_names))}
        
        return importance_dict
    
    @staticmethod
    def _simple_correlation_analysis(param_samples: List[Dict], performance_scores: List[float]) -> Dict[str, float]:
        """简化的相关性分析"""
        param_names = list(param_samples[0].keys())
        importance_dict = {}
        
        for param_name in param_names:
            param_values = [sample[param_name] for sample in param_samples]
            
            # 计算相关系数
            correlation = np.corrcoef(param_values, performance_scores)[0, 1]
            
            # 使用相关系数的绝对值作为重要性
            importance_dict[param_name] = abs(correlation) if not np.isnan(correlation) else 0.1
        
        # 归一化重要性
        total_importance = sum(importance_dict.values())
        if total_importance > 0:
            importance_dict = {k: v / total_importance for k, v in importance_dict.items()}
        else:
            importance_dict = {param: 1.0 / len(param_names) for param in param_names}
        
        return importance_dict


class ParallelBacktestEngine:
    """并行回测引擎"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
    
    def run_backtest_batch(self, 
                          param_list: List[Dict],
                          backtest_func: Callable,
                          **backtest_kwargs) -> List[Tuple[Dict, float, Dict]]:
        """
        并行运行多组回测
        
        Args:
            param_list: 参数列表
            backtest_func: 回测函数
            **backtest_kwargs: 回测函数的其他参数
            
        Returns:
            List[Tuple[Dict, float, Dict]]: [(参数, 目标值, 统计结果), ...]
        """
        results = []
        
        # 使用进程池并行执行
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_params = {
                executor.submit(self._run_single_backtest, params, backtest_func, **backtest_kwargs): params
                for params in param_list
            }
            
            # 收集结果
            for future in as_completed(future_to_params):
                params = future_to_params[future]
                try:
                    target_value, stats = future.result()
                    results.append((params, target_value, stats))
                except Exception as e:
                    print(f"回测失败，参数: {params}, 错误: {str(e)}")
                    results.append((params, -999.0, {}))
        
        return results
    
    @staticmethod
    def _run_single_backtest(params: Dict, backtest_func: Callable, **kwargs) -> Tuple[float, Dict]:
        """
        运行单次回测
        
        Args:
            params: 参数字典
            backtest_func: 回测函数
            **kwargs: 其他回测参数
            
        Returns:
            Tuple[float, Dict]: (目标值, 统计结果)
        """
        try:
            # 注意：这里不进行参数约束，因为参数应该在上层已经约束过
            # 这避免了重复约束和潜在的性能问题
            target_value, stats = backtest_func(params, **kwargs)
            return target_value, stats
        except Exception as e:
            return -999.0, {}


class RobustnessValidator:
    """鲁棒性验证器"""
    
    @staticmethod
    def walk_forward_validation(data_periods: List[Tuple[datetime, datetime]],
                               best_params: Dict,
                               backtest_func: Callable,
                               **kwargs) -> List[Dict]:
        """
        Walk-Forward验证
        
        Args:
            data_periods: 数据周期列表 [(train_start, train_end, test_start, test_end), ...]
            best_params: 最佳参数
            backtest_func: 回测函数
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 各周期验证结果
        """
        results = []
        
        for i, (train_start, train_end, test_start, test_end) in enumerate(data_periods):
            print(f"Walk-Forward验证 {i+1}/{len(data_periods)}: {test_start} - {test_end}")
            
            # 在测试集上运行回测
            try:
                kwargs_copy = kwargs.copy()
                kwargs_copy['start'] = test_start
                kwargs_copy['end'] = test_end
                
                target_value, stats = backtest_func(best_params, **kwargs_copy)
                
                result = {
                    'period': i + 1,
                    'test_start': test_start,
                    'test_end': test_end,
                    'target_value': target_value,
                    **stats
                }
                results.append(result)
                
            except Exception as e:
                print(f"Walk-Forward验证失败，周期 {i+1}: {str(e)}")
                results.append({
                    'period': i + 1,
                    'test_start': test_start,
                    'test_end': test_end,
                    'target_value': -999.0,
                    'error': str(e)
                })
        
        return results
    
    @staticmethod
    def parameter_sensitivity_test(best_params: Dict,
                                  param_spaces: Dict[str, Tuple],
                                  backtest_func: Callable,
                                  perturbation_ratio: float = 0.05,
                                  n_tests: int = 100,
                                  **kwargs) -> Dict:
        """
        参数敏感性测试
        
        Args:
            best_params: 最佳参数
            param_spaces: 参数空间定义
            backtest_func: 回测函数
            perturbation_ratio: 扰动比例
            n_tests: 测试次数
            **kwargs: 其他参数
            
        Returns:
            Dict: 敏感性测试结果
        """
        print(f"开始参数敏感性测试，扰动比例: {perturbation_ratio:.1%}")
        
        baseline_target, baseline_stats = backtest_func(best_params, **kwargs)
        
        results = []
        for _ in range(n_tests):
            # 生成扰动参数
            perturbed_params = {}
            for param_name, param_value in best_params.items():
                if param_name in param_spaces:
                    min_val, max_val = param_spaces[param_name][:2]
                    
                    # 计算扰动范围
                    param_range = max_val - min_val
                    perturbation = np.random.uniform(-perturbation_ratio, perturbation_ratio) * param_range
                    
                    new_value = param_value + perturbation
                    new_value = max(min_val, min(max_val, new_value))
                    
                    # 处理整数参数
                    if len(param_spaces[param_name]) > 2:
                        step = param_spaces[param_name][2]
                        if isinstance(step, int) or (isinstance(step, float) and step.is_integer()):
                            new_value = int(round(new_value))
                    
                    perturbed_params[param_name] = new_value
                else:
                    perturbed_params[param_name] = param_value
            
            # 运行回测
            try:
                target_value, stats = backtest_func(perturbed_params, **kwargs)
                results.append(target_value)
            except:
                results.append(-999.0)
        
        # 计算统计信息
        valid_results = [r for r in results if r != -999.0]
        
        if valid_results:
            sensitivity_stats = {
                'baseline_target': baseline_target,
                'mean_perturbed': np.mean(valid_results),
                'std_perturbed': np.std(valid_results),
                'min_perturbed': np.min(valid_results),
                'max_perturbed': np.max(valid_results),
                'success_rate': len(valid_results) / len(results),
                'stability_score': 1.0 - (np.std(valid_results) / abs(baseline_target)) if baseline_target != 0 else 0.0
            }
        else:
            sensitivity_stats = {
                'baseline_target': baseline_target,
                'success_rate': 0.0,
                'stability_score': 0.0
            }
        
        return sensitivity_stats


class OptimizationEngine:
    """主优化引擎"""
    
    def __init__(self, log_file: str = None, max_workers: int = 4):
        self.logger = OptimizationLogger(log_file)
        self.parallel_engine = ParallelBacktestEngine(max_workers)
        self.results_history = []
    
    def run_optimization_workflow(self,
                                 param_spaces: Dict[str, Tuple],
                                 backtest_func: Callable,
                                 target_name: str = 'sharpe_ratio',
                                 **backtest_kwargs) -> Dict:
        """
        运行完整的优化工作流程
        
        Args:
            param_spaces: 参数空间定义 {param_name: (min_val, max_val, step)}
            backtest_func: 回测函数
            target_name: 优化目标名称
            **backtest_kwargs: 回测函数的其他参数
            
        Returns:
            Dict: 优化结果
        """
        self.logger.info("=" * 60)
        self.logger.info("开始参数优化工作流程")
        self.logger.info(f"参数空间: {param_spaces}")
        self.logger.info(f"优化目标: {target_name}")
        self.logger.info("=" * 60)
        
        start_time = datetime.now()
        
        # 阶段1：参数重要性分析
        self.logger.info("阶段1：参数重要性分析")
        importance_results = self._stage1_importance_analysis(param_spaces, backtest_func, **backtest_kwargs)
        
        # 阶段2：分层优化
        self.logger.info("阶段2：分层优化")
        optimization_results = self._stage2_layered_optimization(
            param_spaces, importance_results, backtest_func, **backtest_kwargs
        )
        
        # 阶段3：鲁棒性验证
        self.logger.info("阶段3：鲁棒性验证")
        validation_results = self._stage3_robustness_validation(
            optimization_results['best_params'], param_spaces, backtest_func, **backtest_kwargs
        )
        
        end_time = datetime.now()
        total_time = end_time - start_time
        
        # 最终参数验证和约束
        best_params = optimization_results['best_params']
        self.logger.info("执行最终参数验证和约束...")
        
        # 验证参数是否在范围内
        is_valid, violations = ParameterSampler.validate_params_range(best_params, param_spaces)
        
        if not is_valid:
            self.logger.warning("发现参数超出范围，正在修正:")
            for violation in violations:
                self.logger.warning(f"  {violation}")
            
            # 应用参数约束
            best_params = ParameterSampler.constrain_params(best_params, param_spaces)
            self.logger.info("参数已修正并约束在定义范围内")
            
            # 验证修正后的参数
            is_valid_after, _ = ParameterSampler.validate_params_range(best_params, param_spaces)
            if is_valid_after:
                self.logger.info("✅ 参数验证通过，所有参数均在定义范围内")
            else:
                self.logger.error("❌ 参数修正失败，仍有参数超出范围")
        else:
            self.logger.info("✅ 参数验证通过，所有参数均在定义范围内")
        
        # 整合最终结果
        final_results = {
            'best_params': best_params,  # 使用验证和约束后的参数
            'best_target_value': optimization_results['best_target_value'],
            'importance_analysis': importance_results,
            'optimization_history': optimization_results['optimization_history'],
            'validation_results': validation_results,
            'parameter_validation': {
                'is_valid': is_valid,
                'violations': violations if not is_valid else [],
                'constrained': not is_valid
            },
            'total_time': total_time.total_seconds(),
            'timestamp': end_time
        }
        
        self.logger.info("=" * 60)
        self.logger.info("参数优化工作流程完成")
        self.logger.info(f"总耗时: {total_time}")
        self.logger.info(f"最佳参数: {optimization_results['best_params']}")
        self.logger.info(f"最佳目标值: {optimization_results['best_target_value']:.4f}")
        self.logger.info("=" * 60)
        
        return final_results
    
    def _stage1_importance_analysis(self, param_spaces: Dict[str, Tuple], 
                                   backtest_func: Callable, **kwargs) -> Dict:
        """阶段1：参数重要性分析"""
        self.logger.info("使用Sobol序列生成1000组参数样本...")
        
        # 生成Sobol样本
        sobol_samples = ParameterSampler.sobol_sample(param_spaces, 1000)
        
        self.logger.info("开始并行回测评估...")
        
        # 并行回测
        results = self.parallel_engine.run_backtest_batch(sobol_samples, backtest_func, **kwargs)
        
        # 提取性能得分
        performance_scores = [result[1] for result in results]
        valid_count = len([score for score in performance_scores if score != -999.0])
        
        self.logger.info(f"完成{len(results)}组回测，成功{valid_count}组")
        
        # 计算参数重要性
        importance_scores = ParameterImportanceAnalyzer.analyze_importance(sobol_samples, performance_scores)
        
        self.logger.info("参数重要性分析结果:")
        for param, importance in sorted(importance_scores.items(), key=lambda x: x[1], reverse=True):
            self.logger.info(f"  {param}: {importance:.4f}")
        
        return {
            'samples': sobol_samples,
            'performance_scores': performance_scores,
            'importance_scores': importance_scores,
            'valid_rate': valid_count / len(results)
        }
    
    def _stage2_layered_optimization(self, param_spaces: Dict[str, Tuple],
                                    importance_results: Dict,
                                    backtest_func: Callable, **kwargs) -> Dict:
        """阶段2：分层优化"""
        optimization_history = []
        
        # 步骤1：全局粗搜索
        self.logger.info("步骤1：全局粗搜索（拉丁超立方采样）")
        
        lhs_samples = ParameterSampler.lhs_sample(
            param_spaces, 500, importance_results['importance_scores']
        )
        
        coarse_results = self.parallel_engine.run_backtest_batch(lhs_samples, backtest_func, **kwargs)
        
        # 选择Top 50
        sorted_results = sorted(coarse_results, key=lambda x: x[1], reverse=True)
        top_50_results = sorted_results[:50]
        
        best_coarse_params = top_50_results[0][0]
        best_coarse_target = top_50_results[0][1]
        
        self.logger.info(f"粗搜索完成，最佳目标值: {best_coarse_target:.4f}")
        
        optimization_history.append({
            'stage': 'coarse_search',
            'best_params': best_coarse_params,
            'best_target': best_coarse_target,
            'n_evaluated': len(coarse_results)
        })
        
        # 步骤2：贝叶斯优化
        bayes_result = None
        if SKOPT_AVAILABLE:
            self.logger.info("步骤2：贝叶斯优化")
            bayes_result = self._bayesian_optimization(
                param_spaces, top_50_results, backtest_func, **kwargs
            )
            
            if bayes_result:
                optimization_history.append({
                    'stage': 'bayesian_optimization',
                    'best_params': bayes_result['best_params'],
                    'best_target': bayes_result['best_target'],
                    'n_evaluated': bayes_result['n_evaluated']
                })
        else:
            self.logger.warning("跳过贝叶斯优化（scikit-optimize未安装）")
        
        # 步骤3：局部精细搜索
        self.logger.info("步骤3：局部精细搜索")
        
        base_params = bayes_result['best_params'] if bayes_result else best_coarse_params
        
        fine_result = self._local_fine_search(base_params, param_spaces, backtest_func, **kwargs)
        
        optimization_history.append({
            'stage': 'fine_search',
            'best_params': fine_result['best_params'],
            'best_target': fine_result['best_target'],
            'n_evaluated': fine_result['n_evaluated']
        })
        
        return {
            'best_params': fine_result['best_params'],
            'best_target_value': fine_result['best_target'],
            'optimization_history': optimization_history
        }
    
    def _bayesian_optimization(self, param_spaces: Dict[str, Tuple],
                              initial_results: List[Tuple],
                              backtest_func: Callable, **kwargs) -> Optional[Dict]:
        """贝叶斯优化"""
        try:
            # 构建skopt参数空间
            param_names = list(param_spaces.keys())
            dimensions = []
            
            self.logger.info(f"构建贝叶斯优化参数空间，参数数量: {len(param_names)}")
            
            for param_name in param_names:
                space_def = param_spaces[param_name]
                min_val, max_val = space_def[:2]
                
                # 检查参数边界是否有效
                if min_val >= max_val:
                    self.logger.warning(f"参数 {param_name} 的边界无效: min={min_val}, max={max_val}，跳过此参数")
                    continue
                
                # 确保最小间隔
                if len(space_def) > 2:
                    step = space_def[2]
                    if isinstance(step, int) or (isinstance(step, float) and step.is_integer()):
                        # 整数参数：确保至少有1的差值
                        if max_val - min_val < 1:
                            self.logger.warning(f"整数参数 {param_name} 范围太小: [{min_val}, {max_val}]，扩展范围")
                            center = (min_val + max_val) / 2
                            min_val = int(center) - 1
                            max_val = int(center) + 1
                        dimensions.append(Integer(int(min_val), int(max_val), name=param_name))
                    else:
                        # 浮点数参数：确保至少有0.01的差值
                        if max_val - min_val < 0.01:
                            self.logger.warning(f"浮点参数 {param_name} 范围太小: [{min_val}, {max_val}]，扩展范围")
                            center = (min_val + max_val) / 2
                            min_val = center - 0.05
                            max_val = center + 0.05
                        dimensions.append(Real(min_val, max_val, name=param_name))
                else:
                    # 浮点数参数：确保至少有0.01的差值
                    if max_val - min_val < 0.01:
                        self.logger.warning(f"浮点参数 {param_name} 范围太小: [{min_val}, {max_val}]，扩展范围")
                        center = (min_val + max_val) / 2
                        min_val = center - 0.05
                        max_val = center + 0.05
                    dimensions.append(Real(min_val, max_val, name=param_name))
                
                self.logger.info(f"参数 {param_name}: [{min_val}, {max_val}]")
            
            # 检查是否有有效的参数维度
            if len(dimensions) == 0:
                self.logger.error("没有有效的参数维度，跳过贝叶斯优化")
                return None
            
            # 更新参数名称列表（只包含有效参数）
            valid_param_names = [dim.name for dim in dimensions]
            
            # 准备初始数据
            x0 = []
            y0 = []
            
            for params, target, _ in initial_results[:20]:  # 只使用前20个作为初始点
                if target != -999.0:
                    # 只包含有效参数
                    param_values = []
                    skip_sample = False
                    for param_name in valid_param_names:
                        if param_name in params:
                            param_values.append(params[param_name])
                        else:
                            skip_sample = True
                            break
                    
                    if not skip_sample:
                        x0.append(param_values)
                        y0.append(-target)  # 注意：skopt最小化，所以取负值
            
            if len(x0) < 5:
                self.logger.warning("初始样本不足，跳过贝叶斯优化")
                return None
            
            self.logger.info(f"准备了 {len(x0)} 个初始样本进行贝叶斯优化")
            
            # 定义目标函数
            @use_named_args(dimensions)
            def objective(**params):
                try:
                    # 补充原始参数空间中的其他参数（使用默认值）
                    full_params = {}
                    for original_param in param_spaces.keys():
                        if original_param in params:
                            full_params[original_param] = params[original_param]
                        else:
                            # 使用参数空间的中间值作为默认值
                            min_val, max_val = param_spaces[original_param][:2]
                            full_params[original_param] = (min_val + max_val) / 2
                    
                    # 应用参数约束，确保所有参数在定义范围内
                    full_params = ParameterSampler.constrain_params(full_params, param_spaces)
                    
                    target_value, _ = backtest_func(full_params, **kwargs)
                    return -target_value  # 返回负值用于最小化
                except Exception as e:
                    self.logger.warning(f"贝叶斯优化目标函数执行失败: {str(e)}")
                    return 999.0  # 失败时返回大值
            
            # 运行贝叶斯优化
            self.logger.info("开始运行贝叶斯优化...")
            result = gp_minimize(
                func=objective,
                dimensions=dimensions,
                n_calls=100,
                x0=x0,
                y0=y0,
                acq_func='EI',
                n_initial_points=0,
                random_state=42
            )
            
            # 构建最佳参数字典
            best_params = {}
            for i, param_name in enumerate(valid_param_names):
                best_params[param_name] = result.x[i]
            
            # 补充未优化的参数（使用默认值）
            for param_name in param_spaces.keys():
                if param_name not in best_params:
                    min_val, max_val = param_spaces[param_name][:2]
                    best_params[param_name] = (min_val + max_val) / 2
            
            best_target = -result.fun
            
            self.logger.info(f"贝叶斯优化完成，最佳目标值: {best_target:.4f}")
            
            return {
                'best_params': best_params,
                'best_target': best_target,
                'n_evaluated': result.func_vals.shape[0]
            }
            
        except Exception as e:
            self.logger.error(f"贝叶斯优化失败: {str(e)}")
            return None
    
    def _local_fine_search(self, base_params: Dict, param_spaces: Dict[str, Tuple],
                          backtest_func: Callable, **kwargs) -> Dict:
        """局部精细搜索"""
        # 选择最重要的3个参数进行精细搜索
        # 这里简化处理，可以根据重要性分析结果选择
        important_params = list(param_spaces.keys())[:3]
        
        search_params = []
        param_combinations = []
        
        # 为每个重要参数生成邻域网格
        for param_name in important_params:
            if param_name in base_params:
                base_value = base_params[param_name]
                min_val, max_val = param_spaces[param_name][:2]
                
                # 在±10%范围内取5个点
                param_range = max_val - min_val
                search_range = param_range * 0.1
                
                search_values = []
                for i in range(5):
                    offset = (i - 2) * search_range / 2
                    new_value = base_value + offset
                    new_value = max(min_val, min(max_val, new_value))
                    
                    # 处理整数参数
                    if len(param_spaces[param_name]) > 2:
                        step = param_spaces[param_name][2]
                        if isinstance(step, int) or (isinstance(step, float) and step.is_integer()):
                            new_value = int(round(new_value))
                    
                    search_values.append(new_value)
                
                search_params.append(search_values)
            else:
                search_params.append([base_params.get(param_name, 0)])
        
        # 生成所有组合
        for combination in itertools.product(*search_params):
            test_params = base_params.copy()
            for i, param_name in enumerate(important_params):
                test_params[param_name] = combination[i]
            
            # 应用参数约束，确保参数在定义范围内
            test_params = ParameterSampler.constrain_params(test_params, param_spaces)
            param_combinations.append(test_params)
        
        self.logger.info(f"精细搜索：评估{len(param_combinations)}个参数组合")
        
        # 并行评估所有组合
        fine_results = self.parallel_engine.run_backtest_batch(
            param_combinations, backtest_func, **kwargs
        )
        
        # 选择最佳结果
        best_result = max(fine_results, key=lambda x: x[1])
        
        self.logger.info(f"精细搜索完成，最佳目标值: {best_result[1]:.4f}")
        
        return {
            'best_params': best_result[0],
            'best_target': best_result[1],
            'n_evaluated': len(fine_results)
        }
    
    def _stage3_robustness_validation(self, best_params: Dict, param_spaces: Dict[str, Tuple],
                                     backtest_func: Callable, **kwargs) -> Dict:
        """阶段3：鲁棒性验证"""
        validation_results = {}
        
        # Walk-Forward验证
        if 'start' in kwargs and 'end' in kwargs:
            self.logger.info("执行Walk-Forward验证...")
            
            # 生成时间窗口
            start_date = kwargs['start']
            end_date = kwargs['end']
            total_days = (end_date - start_date).days
            
            if total_days > 60:  # 至少需要60天数据
                periods = []
                window_size = total_days // 5
                
                for i in range(1, 5):
                    test_start = start_date + timedelta(days=window_size * i)
                    test_end = start_date + timedelta(days=window_size * (i + 1))
                    periods.append((start_date, test_start, test_start, test_end))
                
                wf_results = RobustnessValidator.walk_forward_validation(
                    periods, best_params, backtest_func, **kwargs
                )
                
                validation_results['walk_forward'] = wf_results
                
                # 计算验证统计
                valid_results = [r for r in wf_results if r.get('target_value', -999) != -999]
                if valid_results:
                    targets = [r['target_value'] for r in valid_results]
                    validation_results['wf_stats'] = {
                        'mean_target': np.mean(targets),
                        'std_target': np.std(targets),
                        'success_rate': len(valid_results) / len(wf_results),
                        'consistency_score': 1.0 - (np.std(targets) / abs(np.mean(targets))) if np.mean(targets) != 0 else 0.0
                    }
                    
                    self.logger.info(f"Walk-Forward验证完成，一致性得分: {validation_results['wf_stats']['consistency_score']:.4f}")
        
        # 参数敏感性测试
        self.logger.info("执行参数敏感性测试...")
        
        sensitivity_results = RobustnessValidator.parameter_sensitivity_test(
            best_params, param_spaces, backtest_func, **kwargs
        )
        
        validation_results['sensitivity'] = sensitivity_results
        
        self.logger.info(f"敏感性测试完成，稳定性得分: {sensitivity_results.get('stability_score', 0):.4f}")
        
        return validation_results 