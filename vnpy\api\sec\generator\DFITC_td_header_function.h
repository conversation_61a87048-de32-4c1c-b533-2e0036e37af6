int reqStockUserLogin(const dict &req);

int reqStockUserLogout(const dict &req);

int reqStockUserPasswordUpdate(const dict &req);

int reqStockEntrustOrder(const dict &req);

int reqStockWithdrawOrder(const dict &req);

int reqStockQryEntrustOrder(const dict &req);

int reqStockQryRealTimeTrade(const dict &req);

int reqStockQrySerialTrade(const dict &req);

int reqStockQryPosition(const dict &req);

int reqStockQryCapitalAccountInfo(const dict &req);

int reqStockQryAccountInfo(const dict &req);

int reqStockQryShareholderInfo(const dict &req);

int reqStockTransferFunds(const dict &req);

int reqStockEntrustBatchOrder(const dict &req);

int reqStockWithdrawBatchOrder(const dict &req);

int reqStockCalcAbleEntrustQty(const dict &req);

int reqStockCalcAblePurchaseETFQty(const dict &req);

int reqStockQryFreezeFundsDetail(const dict &req);

int reqStockQryFreezeStockDetail(const dict &req);

int reqStockQryTransferFundsDetail(const dict &req);

int reqStockQryTransferStockDetail(const dict &req);

int reqStockQryStockInfo(const dict &req);

int reqStockQryStockStaticInfo(const dict &req);

int reqStockQryTradeTime(const dict &req);

int reqSOPUserLogin(const dict &req);

int reqSOPUserLogout(const dict &req);

int reqSOPUserPasswordUpdate(const dict &req);

int reqSOPEntrustOrder(const dict &req);

int reqSOPQuoteEntrustOrder(const dict &req);

int reqSOPGroupSplit(const dict &req);

int reqSOPGroupExectueOrder(const dict &req);

int reqSOPQryGroupPosition(const dict &req);

int reqSOPLockOUnLockStock(const dict &req);

int reqSOPWithdrawOrder(const dict &req);

int reqSOPQryEntrustOrder(const dict &req);

int reqSOPQrySerialTrade(const dict &req);

int reqSOPQryPosition(const dict &req);

int reqSOPQryCollateralPosition(const dict &req);

int reqSOPQryCapitalAccountInfo(const dict &req);

int reqSOPQryAccountInfo(const dict &req);

int reqSOPQryShareholderInfo(const dict &req);

int reqSOPCalcAbleEntrustQty(const dict &req);

int reqSOPQryAbleLockStock(const dict &req);

int reqSOPQryContactInfo(const dict &req);

int reqSOPExectueOrder(const dict &req);

int reqSOPQryExecAssiInfo(const dict &req);

int reqSOPQryTradeTime(const dict &req);

int reqSOPQryExchangeInfo(const dict &req);

int reqSOPQryCommission(const dict &req);

int reqSOPQryDeposit(const dict &req);

int reqSOPQryContractObjectInfo(const dict &req);

int reqFASLUserLogin(const dict &req);

int reqFASLUserLogout(const dict &req);

int reqFASLQryAbleFinInfo(const dict &req);

int reqFASLQryAbleSloInfo(const dict &req);

int reqFASLTransferCollateral(const dict &req);

int reqFASLDirectRepayment(const dict &req);

int reqFASLRepayStockTransfer(const dict &req);

int reqFASLEntrustCrdtOrder(const dict &req);

int reqFASLEntrsuctOrder(const dict &req);

int reqFASLWithdrawOrder(const dict &req);

int reqFASLCalcAbleEntrustCrdtQty(const dict &req);

int reqFASLQryCrdtFunds(const dict &req);

int reqFASLQryCrdtContract(const dict &req);

int reqFASLQryCrdtConChangeInfo(const dict &req);

int reqFASLTransferFunds(const dict &req);

int reqFASLQryAccountInfo(const dict &req);

int reqFASLQryCapitalAccountInfo(const dict &req);

int reqFASLQryShareholderInfo(const dict &req);

int reqFASLQryPosition(const dict &req);

int reqFASLQryEntrustOrder(const dict &req);

int reqFASLQrySerialTrade(const dict &req);

int reqFASLQryRealTimeTrade(const dict &req);

int reqFASLQryFreezeFundsDetail(const dict &req);

int reqFASLQryFreezeStockDetail(const dict &req);

int reqFASLQryTransferFundsDetail(const dict &req);

int reqFASLQrySystemTime(const dict &req);

int reqFASLQryTransferredContract(const dict &req);

int reqFASLDesirableFundsOut(const dict &req);

int reqFASLQryGuaranteedContract(const dict &req);

int reqFASLQryUnderlyingContract(const dict &req);

