"""
数据管理模块
Data Management Module

功能：
1. 数据获取 - 从多个数据源获取实时和历史数据
2. 数据清洗 - 处理缺失值、异常值和重复数据
3. 数据存储 - 高效存储时序数据
4. 数据缓存 - 内存缓存提高访问速度
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
import sqlite3
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 导入vnpy相关模块
try:
    from vnpy.trader.database import get_database
    from vnpy.trader.object import BarData
    from vnpy.trader.constant import Exchange, Interval
    VNPY_AVAILABLE = True
except ImportError:
    VNPY_AVAILABLE = False
    print("警告: vnpy未安装或导入失败，将使用模拟数据")

logger = logging.getLogger(__name__)

class DataManager:
    """数据管理器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化数据管理器
        
        Args:
            data_dir: 数据存储目录
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # 数据库文件
        self.db_path = self.data_dir / "market_data.db"
        
        # 内存缓存
        self.cache = {}
        self.cache_timeout = 300  # 5分钟缓存
        
        # 初始化数据库
        self._init_database()
        
        # 支持的数据源
        self.data_sources = {
            'vnpy': self._get_vnpy_data,
            'tushare': self._get_tushare_data,
            'simulation': self._get_simulation_data
        }
        
    def _init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            # 创建日线数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS daily_data (
                    symbol TEXT,
                    datetime TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume INTEGER,
                    open_interest INTEGER,
                    PRIMARY KEY (symbol, datetime)
                )
            """)
            
            # 创建分钟数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS minute_data (
                    symbol TEXT,
                    datetime TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume INTEGER,
                    open_interest INTEGER,
                    PRIMARY KEY (symbol, datetime)
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_daily_symbol ON daily_data(symbol)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_daily_datetime ON daily_data(datetime)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_minute_symbol ON minute_data(symbol)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_minute_datetime ON minute_data(datetime)")
            
            conn.commit()
    
    def get_historical_data(self, symbol: str, start_date: str, end_date: str,
                          frequency: str = 'daily', source: str = 'simulation') -> pd.DataFrame:
        """
        获取历史数据
        
        Args:
            symbol: 品种代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            frequency: 数据频率 ('daily', 'minute')
            source: 数据源 ('vnpy', 'tushare', 'simulation')
            
        Returns:
            历史数据DataFrame
        """
        # 检查缓存
        cache_key = f"{symbol}_{start_date}_{end_date}_{frequency}_{source}"
        if cache_key in self.cache:
            cache_time, data = self.cache[cache_key]
            if (datetime.now() - cache_time).seconds < self.cache_timeout:
                logger.debug(f"从缓存获取 {symbol} 数据")
                return data.copy()
        
        # 先尝试从数据库获取
        data = self._get_data_from_db(symbol, start_date, end_date, frequency)
        
        # 如果数据不足，从数据源获取
        if data.empty or len(data) < 10:
            logger.info(f"从 {source} 获取 {symbol} 数据")
            if source in self.data_sources:
                data = self.data_sources[source](symbol, start_date, end_date, frequency)
                
                # 保存到数据库
                if not data.empty:
                    self._save_data_to_db(data, symbol, frequency)
            else:
                logger.warning(f"不支持的数据源: {source}")
                data = pd.DataFrame()
        
        # 数据清洗
        if not data.empty:
            data = self._clean_data(data)
            
            # 更新缓存
            self.cache[cache_key] = (datetime.now(), data.copy())
        
        return data
    
    def get_real_time_data(self, symbols: List[str]) -> Dict[str, Dict]:
        """
        获取实时数据
        
        Args:
            symbols: 品种代码列表
            
        Returns:
            实时数据字典
        """
        real_time_data = {}
        
        for symbol in symbols:
            try:
                # 模拟实时数据（实际应用中连接实时数据源）
                data = self._get_simulation_tick_data(symbol)
                real_time_data[symbol] = data
            except Exception as e:
                logger.error(f"获取 {symbol} 实时数据失败: {e}")
                real_time_data[symbol] = {}
        
        return real_time_data
    
    def _get_data_from_db(self, symbol: str, start_date: str, end_date: str,
                         frequency: str) -> pd.DataFrame:
        """从数据库获取数据"""
        table_name = f"{frequency}_data"
        
        query = f"""
            SELECT * FROM {table_name}
            WHERE symbol = ? AND datetime >= ? AND datetime <= ?
            ORDER BY datetime
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                data = pd.read_sql_query(query, conn, params=(symbol, start_date, end_date))
                
                if not data.empty:
                    data['datetime'] = pd.to_datetime(data['datetime'])
                    data.set_index('datetime', inplace=True)
                
                return data
                
        except Exception as e:
            logger.error(f"从数据库获取数据失败: {e}")
            return pd.DataFrame()
    
    def _save_data_to_db(self, data: pd.DataFrame, symbol: str, frequency: str):
        """保存数据到数据库"""
        if data.empty:
            return
        
        table_name = f"{frequency}_data"
        
        try:
            # 准备数据
            data_to_save = data.copy()
            data_to_save['symbol'] = symbol
            data_to_save.reset_index(inplace=True)
            
            # 确保datetime列是字符串格式
            data_to_save['datetime'] = data_to_save['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            with sqlite3.connect(self.db_path) as conn:
                data_to_save.to_sql(table_name, conn, if_exists='append', index=False)
                
            logger.debug(f"保存 {len(data_to_save)} 条 {symbol} 数据到数据库")
            
        except Exception as e:
            logger.error(f"保存数据到数据库失败: {e}")
    
    def _get_vnpy_data(self, symbol: str, start_date: str, end_date: str,
                      frequency: str) -> pd.DataFrame:
        """从VnPy获取数据"""
        if not VNPY_AVAILABLE:
            logger.info(f"VnPy不可用，使用模拟数据")
            return self._get_simulation_data(symbol, start_date, end_date, frequency)

        try:
            # 解析symbol格式 (如 rb888.SHFE -> rb888, SHFE)
            if '.' in symbol:
                vt_symbol, exchange_str = symbol.split('.')
                exchange = Exchange(exchange_str)
            else:
                vt_symbol = symbol
                exchange = Exchange.SHFE  # 默认上期所

            # 设置时间间隔
            interval = Interval.DAILY if frequency == 'daily' else Interval.MINUTE

            # 从数据库获取数据
            database = get_database()
            bars = database.load_bar_data(
                symbol=vt_symbol,
                exchange=exchange,
                interval=interval,
                start=datetime.strptime(start_date, '%Y-%m-%d'),
                end=datetime.strptime(end_date, '%Y-%m-%d')
            )

            if not bars:
                logger.warning(f"VnPy数据库中没有找到 {symbol} 的数据，使用模拟数据")
                return self._get_simulation_data(symbol, start_date, end_date, frequency)

            # 转换为DataFrame
            data = []
            for bar in bars:
                data.append({
                    'datetime': bar.datetime,
                    'open': bar.open_price,
                    'high': bar.high_price,
                    'low': bar.low_price,
                    'close': bar.close_price,
                    'volume': bar.volume,
                    'open_interest': getattr(bar, 'open_interest', 0)
                })

            df = pd.DataFrame(data)
            if not df.empty:
                df.set_index('datetime', inplace=True)
                logger.info(f"从VnPy获取到 {len(df)} 条 {symbol} 数据")

            return df

        except Exception as e:
            logger.error(f"VnPy数据获取失败: {e}")
            return self._get_simulation_data(symbol, start_date, end_date, frequency)
    
    def _get_tushare_data(self, symbol: str, start_date: str, end_date: str,
                         frequency: str) -> pd.DataFrame:
        """从Tushare获取数据"""
        try:
            # 这里应该连接Tushare接口
            # 由于没有Tushare token，使用模拟数据
            logger.info(f"Tushare数据源暂不可用，使用模拟数据")
            return self._get_simulation_data(symbol, start_date, end_date, frequency)
            
        except Exception as e:
            logger.error(f"Tushare数据获取失败: {e}")
            return pd.DataFrame()
    
    def _get_simulation_data(self, symbol: str, start_date: str, end_date: str,
                           frequency: str) -> pd.DataFrame:
        """生成模拟数据"""
        try:
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            if frequency == 'daily':
                dates = pd.date_range(start_dt, end_dt, freq='D')
                # 过滤掉周末
                dates = dates[dates.weekday < 5]
            else:  # minute
                dates = pd.date_range(start_dt, end_dt, freq='1min')
                # 只保留交易时间（简化版）
                dates = dates[(dates.hour >= 9) & (dates.hour <= 15)]
            
            if len(dates) == 0:
                return pd.DataFrame()
            
            # 生成模拟价格数据
            np.random.seed(hash(symbol) % 2**32)  # 确保同一品种数据一致
            
            # 基础价格（根据品种设置不同的价格水平）
            base_prices = {
                'rb888.SHFE': 4000, 'hc888.SHFE': 3800, 'i888.DCE': 800,
                'cu888.SHFE': 70000, 'al888.SHFE': 19000, 'zn888.SHFE': 25000,
                'au888.SHFE': 450, 'ag888.SHFE': 5500,
                'sc888.INE': 600, 'bu888.SHFE': 3200,
                'TA888.CZCE': 6000, 'MA888.CZCE': 2500,
                'm888.DCE': 3200, 'y888.DCE': 8500
            }
            
            base_price = base_prices.get(symbol, 3000)
            
            # 生成价格序列（几何布朗运动）
            n = len(dates)
            returns = np.random.normal(0.0001, 0.02, n)  # 日收益率
            price_series = base_price * np.exp(np.cumsum(returns))
            
            # 生成OHLC数据
            data = []
            for i, (date, price) in enumerate(zip(dates, price_series)):
                # 生成日内波动
                daily_vol = abs(np.random.normal(0, 0.01))
                high = price * (1 + daily_vol)
                low = price * (1 - daily_vol)
                open_price = price_series[i-1] if i > 0 else price
                close_price = price
                
                # 确保OHLC逻辑正确
                high = max(high, open_price, close_price)
                low = min(low, open_price, close_price)
                
                # 生成成交量
                volume = int(np.random.lognormal(10, 1))
                open_interest = int(np.random.lognormal(12, 0.5))
                
                data.append({
                    'datetime': date,
                    'open': round(open_price, 2),
                    'high': round(high, 2),
                    'low': round(low, 2),
                    'close': round(close_price, 2),
                    'volume': volume,
                    'open_interest': open_interest
                })
            
            df = pd.DataFrame(data)
            df.set_index('datetime', inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"生成模拟数据失败: {e}")
            return pd.DataFrame()
    
    def _get_simulation_tick_data(self, symbol: str) -> Dict:
        """生成模拟实时数据"""
        try:
            # 获取最近的历史数据作为基准
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d')
            
            historical = self.get_historical_data(symbol, start_date, end_date)
            
            if historical.empty:
                return {}
            
            # 基于最后价格生成实时数据
            last_price = historical['close'].iloc[-1]
            
            # 模拟实时价格变动
            price_change = np.random.normal(0, last_price * 0.001)
            current_price = last_price + price_change
            
            return {
                'symbol': symbol,
                'last_price': round(current_price, 2),
                'bid_price': round(current_price - 0.5, 2),
                'ask_price': round(current_price + 0.5, 2),
                'volume': int(np.random.lognormal(8, 1)),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"生成实时数据失败: {e}")
            return {}
    
    def _clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据清洗"""
        if data.empty:
            return data
        
        cleaned_data = data.copy()
        
        # 删除重复数据
        cleaned_data = cleaned_data[~cleaned_data.index.duplicated(keep='first')]
        
        # 处理缺失值
        cleaned_data = cleaned_data.dropna()
        
        # 检查OHLC逻辑
        mask = (
            (cleaned_data['high'] >= cleaned_data['open']) &
            (cleaned_data['high'] >= cleaned_data['close']) &
            (cleaned_data['low'] <= cleaned_data['open']) &
            (cleaned_data['low'] <= cleaned_data['close']) &
            (cleaned_data['volume'] >= 0)
        )
        
        cleaned_data = cleaned_data[mask]
        
        # 检测异常值（价格变动超过20%的数据点）
        if len(cleaned_data) > 1:
            price_change = cleaned_data['close'].pct_change().abs()
            outlier_mask = price_change < 0.2  # 过滤掉变动超过20%的数据
            outlier_mask.iloc[0] = True  # 保留第一个数据点
            cleaned_data = cleaned_data[outlier_mask]
        
        return cleaned_data
    
    def get_data_quality_report(self, symbol: str, start_date: str, end_date: str) -> Dict:
        """生成数据质量报告"""
        data = self.get_historical_data(symbol, start_date, end_date)
        
        if data.empty:
            return {'symbol': symbol, 'status': 'no_data'}
        
        # 计算数据质量指标
        total_days = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days
        actual_days = len(data)
        completeness = actual_days / max(total_days, 1)
        
        # 检查缺失值
        missing_values = data.isnull().sum().sum()
        
        # 检查异常值
        price_changes = data['close'].pct_change().abs()
        outliers = (price_changes > 0.1).sum()  # 变动超过10%的数据点
        
        return {
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date,
            'total_records': len(data),
            'completeness': round(completeness, 3),
            'missing_values': int(missing_values),
            'outliers': int(outliers),
            'data_range': {
                'min_price': float(data[['open', 'high', 'low', 'close']].min().min()),
                'max_price': float(data[['open', 'high', 'low', 'close']].max().max()),
                'avg_volume': int(data['volume'].mean())
            },
            'quality_score': round((1 - missing_values/len(data)) * completeness * (1 - outliers/len(data)), 3)
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        logger.info("数据缓存已清空")
    
    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        return {
            'cache_size': len(self.cache),
            'cache_timeout': self.cache_timeout,
            'cached_symbols': list(set(key.split('_')[0] for key in self.cache.keys()))
        }
