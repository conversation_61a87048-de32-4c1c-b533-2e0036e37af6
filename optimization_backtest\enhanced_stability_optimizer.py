"""
增强稳定性导向参数优化器

核心功能：
1. 三层稳定性验证机制
2. 参数重要性分析 (XGBoost + 自适应采样)
3. 分层优化流程 (全局搜索 → 贝叶斯优化 → 精细搜索)
4. 鲁棒性验证 (蒙特卡洛测试 + Walk-Forward验证)
5. 稳定性评分系统

作者: AI Assistant
版本: 2.1 (安全修复版)
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Callable
from dataclasses import dataclass
import json
import warnings
from datetime import datetime
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
from itertools import product
import time
import gc
import os
import psutil

# 机器学习相关
try:
    import xgboost as xgb
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    from skopt import gp_minimize
    from skopt.space import Real, Integer
    from skopt.utils import use_named_args
    XGB_AVAILABLE = True
except ImportError:
    XGB_AVAILABLE = False
    warnings.warn("XGBoost或scikit-optimize不可用，将使用基础实现")

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 安全配置
MAX_MEMORY_USAGE_PERCENT = 80  # 最大内存使用率
MAX_WORKERS_LIMIT = min(4, mp.cpu_count() // 2)  # 限制最大并行数
GPU_MEMORY_LIMIT = 0.5  # GPU内存使用限制 (50%)

@dataclass
class StabilityMetrics:
    """稳定性指标数据类"""
    performance_stability: float  # 性能稳定性 (变异系数倒数)
    parameter_sensitivity: float  # 参数敏感性 (扰动测试)
    temporal_consistency: float   # 时间一致性 (Walk-Forward)
    robustness_score: float      # 鲁棒性得分 (综合)
    overall_stability: float     # 总体稳定性得分


@dataclass
class OptimizationResult:
    """优化结果数据类"""
    best_params: Dict[str, Any]
    best_performance: float
    stability_metrics: StabilityMetrics
    stability_score: float
    parameter_importance: Dict[str, float]
    validation_results: Dict[str, Any]


def check_system_resources() -> Dict[str, Any]:
    """检查系统资源状态"""
    try:
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        
        return {
            'memory_percent': memory.percent,
            'memory_available_gb': memory.available / (1024**3),
            'cpu_percent': cpu_percent,
            'safe_to_proceed': memory.percent < MAX_MEMORY_USAGE_PERCENT and cpu_percent < 90
        }
    except:
        return {
            'memory_percent': 50,
            'memory_available_gb': 4,
            'cpu_percent': 50,
            'safe_to_proceed': True
        }


class EnhancedStabilityOptimizer:
    """增强的稳定性导向参数优化器（安全版）"""
    
    def __init__(
        self,
        backtester: Callable,
        param_spaces: Dict[str, Tuple[float, float, float, type]],
        stability_weight: float = 0.4,
        performance_metric: str = 'sharpe_ratio',
        max_workers: Optional[int] = None,
        random_seed: int = 42,
        safe_mode: bool = True,
        silent_backtest: bool = True,
        enable_multiprocessing: bool = True
    ):
        """
        初始化增强稳定性优化器
        
        Args:
            backtester: 回测函数，接受参数字典，返回性能指标字典
            param_spaces: 参数空间 {param_name: (min, max, step, type)}
            stability_weight: 稳定性权重 (0-1)
            performance_metric: 主要性能指标名称
            max_workers: 最大并行工作进程数
            random_seed: 随机种子
            safe_mode: 安全模式，限制资源使用
            silent_backtest: 控制回测过程中的日志输出
            enable_multiprocessing: 是否启用多进程加速
        """
        self.backtester = backtester
        self.param_spaces = param_spaces
        self.stability_weight = stability_weight
        self.performance_metric = performance_metric
        self.random_seed = random_seed
        self.safe_mode = safe_mode
        self.silent_backtest = silent_backtest
        self.enable_multiprocessing = enable_multiprocessing
        
        # 智能的并行数设置
        if max_workers is None:
            cpu_count = mp.cpu_count()
            if safe_mode:
                # 安全模式：使用一半CPU核心，最多4个进程
                self.max_workers = min(8, max(1, cpu_count // 2))
            else:
                # 高性能模式：使用更多CPU核心，但留出1-2个核心给系统
                self.max_workers = min(14, max(1, cpu_count - 1))
        else:
            self.max_workers = min(max_workers, MAX_WORKERS_LIMIT)
        
        # 如果禁用多进程，强制使用单进程
        if not enable_multiprocessing:
            self.max_workers = 1
        
        # 初始化随机种子
        np.random.seed(random_seed)
        
        # 参数空间验证
        self._validate_param_spaces()
        
        # 缓存和状态
        self.evaluation_cache = {}
        self.importance_scores = {}
        self.optimization_history = []
        
        # 检查系统资源
        resource_status = check_system_resources()
        if not resource_status['safe_to_proceed']:
            logger.warning(f"系统资源紧张 - 内存: {resource_status['memory_percent']:.1f}%, CPU: {resource_status['cpu_percent']:.1f}%")
            if enable_multiprocessing:
                self.max_workers = max(1, self.max_workers // 2)  # 减半并行数
                logger.info(f"资源紧张，调整并行数为: {self.max_workers}")
        
        logger.info(f"增强稳定性优化器初始化完成 - 参数数量: {len(param_spaces)}, 并行度: {self.max_workers}, 安全模式: {safe_mode}, 多进程: {enable_multiprocessing}")
    
    def _validate_param_spaces(self):
        """验证参数空间格式"""
        for param_name, space in self.param_spaces.items():
            if len(space) != 4:
                raise ValueError(f"参数 {param_name} 空间格式错误，应为 (min, max, step, type)")
            
            min_val, max_val, step, param_type = space
            if min_val >= max_val:
                raise ValueError(f"参数 {param_name} 最小值应小于最大值")
            
            if param_type not in [int, float]:
                raise ValueError(f"参数 {param_name} 类型应为 int 或 float")
    
    def _generate_param_combinations(
        self,
        n_samples: Optional[int] = None,
        sampling_strategy: str = 'adaptive'
    ) -> List[Dict[str, Any]]:
        """
        生成参数组合
        
        Args:
            n_samples: 采样数量，None表示全部组合
            sampling_strategy: 采样策略 ('uniform', 'adaptive', 'importance_based')
        """
        # 安全模式下限制采样数量
        if self.safe_mode and n_samples and n_samples > 100:
            logger.warning(f"安全模式下限制采样数量从 {n_samples} 减少到 100")
            n_samples = 100
            
        if sampling_strategy == 'uniform' or not self.importance_scores:
            return self._uniform_sampling(n_samples)
        elif sampling_strategy == 'adaptive':
            return self._adaptive_sampling(n_samples)
        elif sampling_strategy == 'importance_based':
            return self._importance_based_sampling(n_samples)
        else:
            raise ValueError(f"未知采样策略: {sampling_strategy}")
    
    def _uniform_sampling(self, n_samples: Optional[int]) -> List[Dict[str, Any]]:
        """均匀采样"""
        param_ranges = []
        param_names = []
        
        for param_name, (min_val, max_val, step, param_type) in self.param_spaces.items():
            if param_type == int:
                values = list(range(int(min_val), int(max_val) + 1, int(step)))
            else:
                values = np.arange(min_val, max_val + step, step).tolist()
            
            param_ranges.append(values)
            param_names.append(param_name)
        
        # 生成所有组合或随机采样
        combinations = []
        
        if n_samples is None:
            # 没有指定采样数量，生成所有组合（小心内存使用）
            try:
                # 先检查组合数量是否过大，避免内存溢出
                range_sizes = [len(r) for r in param_ranges]
                max_combinations = 10000  # 设置最大组合数限制
                
                # 估算总组合数（使用对数避免溢出）
                log_total = sum(np.log(max(1, size)) for size in range_sizes if size > 0)
                
                if log_total > np.log(max_combinations):
                    logger.warning(f"参数组合数量过大，强制使用随机采样 {max_combinations} 个")
                    for _ in range(max_combinations):
                        combo = [np.random.choice(param_range) for param_range in param_ranges]
                        combinations.append(tuple(combo))
                else:
                    combinations = list(product(*param_ranges))
                    
            except (MemoryError, OverflowError, ValueError) as e:
                logger.warning(f"组合生成失败，改为随机采样: {e}")
                for _ in range(1000):  # 默认采样1000个
                    combo = [np.random.choice(param_range) for param_range in param_ranges]
                    combinations.append(tuple(combo))
        else:
            # 指定了采样数量，进行安全检查
            range_sizes = [len(r) for r in param_ranges]
            
            # 安全检查：如果任何一个参数范围为空，返回空列表
            if any(size == 0 for size in range_sizes):
                logger.warning("发现空的参数范围，返回空组合列表")
                return []
            
            # 使用对数估算是否需要随机采样
            try:
                log_total = sum(np.log(max(1, size)) for size in range_sizes)
                estimated_total = np.exp(log_total)
                
                if n_samples >= estimated_total or estimated_total <= 10000:
                    # 如果采样数大于等于总数，或总数较小，生成所有组合
                    combinations = list(product(*param_ranges))
                else:
                    # 随机采样
                    for _ in range(n_samples):
                        combo = [np.random.choice(param_range) for param_range in param_ranges]
                        combinations.append(tuple(combo))
                        
            except (OverflowError, ValueError) as e:
                # 如果计算出错，直接使用随机采样
                logger.info(f"使用随机采样方式生成参数组合: {e}")
                for _ in range(n_samples):
                    combo = [np.random.choice(param_range) for param_range in param_ranges]
                    combinations.append(tuple(combo))
        
        # 转换为字典格式
        param_combinations = []
        for combo in combinations:
            param_dict = dict(zip(param_names, combo))
            param_combinations.append(param_dict)
        
        return param_combinations
    
    def _adaptive_sampling(self, n_samples: int) -> List[Dict[str, Any]]:
        """基于重要性的自适应采样"""
        if not self.importance_scores:
            return self._uniform_sampling(n_samples)
        
        param_combinations = []
        
        # 根据重要性分配采样密度
        total_importance = sum(self.importance_scores.values())
        
        for _ in range(n_samples):
            param_dict = {}
            for param_name, (min_val, max_val, step, param_type) in self.param_spaces.items():
                importance = self.importance_scores.get(param_name, 1.0)
                importance_ratio = importance / total_importance
                
                # 重要参数使用更精细的采样
                if importance_ratio > 0.3:  # 高重要性
                    density_factor = 1.0
                elif importance_ratio > 0.15:  # 中等重要性
                    density_factor = 0.7
                else:  # 低重要性
                    density_factor = 0.5
                
                # 计算采样范围
                if param_type == int:
                    effective_step = max(1, int(step / density_factor))
                    values = list(range(int(min_val), int(max_val) + 1, effective_step))
                else:
                    effective_step = step / density_factor
                    values = np.arange(min_val, max_val + effective_step, effective_step).tolist()
                
                param_dict[param_name] = np.random.choice(values)
            
            param_combinations.append(param_dict)
        
        return param_combinations
    
    def _importance_based_sampling(self, n_samples: int) -> List[Dict[str, Any]]:
        """基于重要性的非均匀采样"""
        if not self.importance_scores:
            return self._uniform_sampling(n_samples)
        
        param_combinations = []
        
        for _ in range(n_samples):
            param_dict = {}
            for param_name, (min_val, max_val, step, param_type) in self.param_spaces.items():
                importance = self.importance_scores.get(param_name, 0.5)
                
                # 重要参数倾向于最优区域，不重要参数随机
                if importance > 0.6:
                    # 高重要性：正态分布采样，中心为参数范围中点
                    center = (min_val + max_val) / 2
                    std = (max_val - min_val) / 6  # 3sigma覆盖范围
                    value = np.random.normal(center, std)
                    value = np.clip(value, min_val, max_val)
                else:
                    # 低重要性：均匀分布采样
                    value = np.random.uniform(min_val, max_val)
                
                # 类型转换和步长对齐
                if param_type == int:
                    value = int(round(value / step) * step)
                    value = max(int(min_val), min(int(max_val), value))
                else:
                    value = round(value / step) * step
                    value = max(min_val, min(max_val, value))
                
                param_dict[param_name] = value
            
            param_combinations.append(param_dict)
        
        return param_combinations
    
    def _evaluate_parameter_set(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """评估单个参数集合"""
        param_key = str(sorted(params.items()))
        
        # 检查缓存
        if param_key in self.evaluation_cache:
            return self.evaluation_cache[param_key]
        
        try:
            # 在静默模式下临时抑制日志输出
            if self.silent_backtest:
                # 保存当前日志级别
                original_level = logging.getLogger().level
                # 设置为更高级别以抑制INFO和DEBUG日志
                logging.getLogger().setLevel(logging.WARNING)
                
                # 也可以抑制print输出（重定向到null）
                import sys
                from io import StringIO
                original_stdout = sys.stdout
                sys.stdout = StringIO()
                
                try:
                    # 执行回测
                    result = self.backtester(params)
                finally:
                    # 恢复日志级别和stdout
                    logging.getLogger().setLevel(original_level)
                    sys.stdout = original_stdout
            else:
                # 正常执行回测
                result = self.backtester(params)
            
            # 确保结果包含必要的指标
            if not isinstance(result, dict):
                result = {self.performance_metric: result}
            
            # 缓存结果
            self.evaluation_cache[param_key] = result
            
            return result
            
        except Exception as e:
            # 恢复输出（如果出现异常）
            if self.silent_backtest:
                try:
                    logging.getLogger().setLevel(original_level)
                    sys.stdout = original_stdout
                except:
                    pass
            logger.warning(f"参数评估失败 {params}: {str(e)}")
            return {self.performance_metric: -np.inf}
    
    def analyze_parameter_importance(
        self,
        n_samples: int = 200,
        method: str = 'xgboost'
    ) -> Dict[str, float]:
        """
        分析参数重要性
        
        Args:
            n_samples: 采样数量
            method: 分析方法 ('xgboost', 'random_forest', 'correlation')
        """
        # 安全模式下限制采样数量
        if self.safe_mode:
            n_samples = min(n_samples, 50)
        
        logger.info(f"开始参数重要性分析 - 采样数量: {n_samples}, 方法: {method}")
        
        # 生成参数组合
        param_combinations = self._uniform_sampling(n_samples)
        # 并行评估
        results = self._parallel_evaluate(param_combinations)
        
        # 准备数据
        X, y = self._prepare_importance_data(param_combinations, results)
        
        # 计算重要性
        if method == 'xgboost' and XGB_AVAILABLE:
            importance_scores = self._xgboost_importance(X, y)
        elif method == 'random_forest':
            importance_scores = self._random_forest_importance(X, y)
        else:
            importance_scores = self._correlation_importance(X, y)
        
        self.importance_scores = importance_scores
        
        logger.info("参数重要性分析完成")
        return importance_scores
    
    def _prepare_importance_data(
        self,
        param_combinations: List[Dict[str, Any]],
        results: List[Dict[str, Any]]
    ) -> Tuple[np.ndarray, np.ndarray]:
        """准备重要性分析数据"""
        param_names = list(self.param_spaces.keys())
        
        # 构建特征矩阵
        X = []
        y = []
        
        for params, result in zip(param_combinations, results):
            if result.get(self.performance_metric, -np.inf) != -np.inf:
                feature_vector = [params[name] for name in param_names]
                X.append(feature_vector)
                y.append(result[self.performance_metric])
        
        return np.array(X), np.array(y)
    
    def _xgboost_importance(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """使用XGBoost计算特征重要性（安全版本）"""
        try:
            # 检查GPU可用性（安全模式下更严格的检查）
            gpu_available = False
            gpu_memory_threshold = GPU_MEMORY_LIMIT if not self.safe_mode else 0.3  # 安全模式下更严格的内存限制
            
            try:
                import GPUtil
                gpus = GPUtil.getGPUs()
                if len(gpus) > 0:
                    # 检查GPU内存使用率
                    gpu = gpus[0]  # 使用第一个GPU
                    if gpu.memoryUtil < gpu_memory_threshold:
                        gpu_available = True
                        if self.safe_mode:
                            logger.info(f"安全模式：检测到GPU可用，内存使用率: {gpu.memoryUtil:.1%} (限制: {gpu_memory_threshold:.1%})")
                        else:
                            logger.info(f"检测到GPU，内存使用率: {gpu.memoryUtil:.1%}")
                    else:
                        if self.safe_mode:
                            logger.info(f"安全模式：GPU内存使用率过高 ({gpu.memoryUtil:.1%} > {gpu_memory_threshold:.1%})，使用CPU")
                        else:
                            logger.info(f"GPU内存使用率过高 ({gpu.memoryUtil:.1%})，使用CPU")
                else:
                    logger.info("未检测到GPU，使用CPU版本XGBoost")
            except ImportError:
                logger.info("GPUtil未安装，无法检测GPU状态，使用CPU版本XGBoost")
            except Exception as e:
                logger.warning(f"GPU检测失败: {str(e)}，使用CPU版本XGBoost")
            
            # 训练XGBoost模型（安全配置）
            if gpu_available:
                # 安全模式下使用更保守的GPU配置
                if self.safe_mode:
                    model = xgb.XGBRegressor(
                        n_estimators=30,  # 安全模式下进一步减少树的数量
                        max_depth=3,      # 安全模式下减少深度
                        learning_rate=0.1,
                        tree_method='gpu_hist',
                        gpu_id=0,
                        random_state=self.random_seed,
                        verbosity=0,
                        n_jobs=1,  # GPU模式下使用单线程
                        max_bin=128  # 限制分箱数量以减少GPU内存使用
                    )
                    logger.info("安全模式：使用保守配置的GPU版本XGBoost进行参数重要性分析")
                else:
                    model = xgb.XGBRegressor(
                        n_estimators=50,  # 减少树的数量
                        max_depth=4,      # 减少深度
                        learning_rate=0.1,
                        tree_method='gpu_hist',
                        gpu_id=0,
                        random_state=self.random_seed,
                        verbosity=0,
                        n_jobs=1  # GPU模式下使用单线程
                    )
                    logger.info("使用GPU版本XGBoost进行参数重要性分析")
            else:
                # CPU版本配置
                if self.safe_mode:
                    model = xgb.XGBRegressor(
                        n_estimators=30,  # 安全模式下减少树的数量
                        max_depth=3,      # 安全模式下减少深度
                        learning_rate=0.1,
                        tree_method='hist',
                        random_state=self.random_seed,
                        verbosity=0,
                        n_jobs=self.max_workers  # 使用用户设置的并行度
                    )
                    logger.info(f"安全模式：使用保守配置的CPU版本XGBoost进行参数重要性分析 (并行度: {self.max_workers})")
                else:
                    model = xgb.XGBRegressor(
                        n_estimators=50,  # 减少树的数量
                        max_depth=4,      # 减少深度
                        learning_rate=0.1,
                        tree_method='hist',
                        random_state=self.random_seed,
                        verbosity=0,
                        n_jobs=self.max_workers  # 使用用户设置的并行度
                    )
                    logger.info(f"使用CPU版本XGBoost进行参数重要性分析 (并行度: {self.max_workers})")
            
            # 训练模型
            model.fit(X, y)
            
            # 获取特征重要性
            importance_values = model.feature_importances_
            param_names = list(self.param_spaces.keys())
            
            # 归一化重要性分数
            importance_sum = np.sum(importance_values)
            if importance_sum > 0:
                importance_values = importance_values / importance_sum
            
            # 清理内存
            del model
            gc.collect()
            
            return dict(zip(param_names, importance_values))
            
        except Exception as e:
            logger.warning(f"XGBoost重要性分析失败: {str(e)}")
            return self._correlation_importance(X, y)
    
    def _random_forest_importance(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """使用随机森林计算特征重要性（安全版本）"""
        try:
            model = RandomForestRegressor(
                n_estimators=50,  # 减少树的数量
                max_depth=6,      # 减少深度
                random_state=self.random_seed,
                n_jobs=self.max_workers  # 使用用户设置的并行度
            )
            model.fit(X, y)
            
            importance_values = model.feature_importances_
            param_names = list(self.param_spaces.keys())
            
            # 归一化
            importance_sum = np.sum(importance_values)
            if importance_sum > 0:
                importance_values = importance_values / importance_sum
            
            # 清理内存
            del model
            gc.collect()
            
            return dict(zip(param_names, importance_values))
            
        except Exception as e:
            logger.warning(f"随机森林重要性分析失败: {str(e)}")
            return self._correlation_importance(X, y)
    
    def _correlation_importance(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """基于相关性的重要性分析"""
        param_names = list(self.param_spaces.keys())
        correlations = []
        
        for i in range(X.shape[1]):
            corr = np.abs(np.corrcoef(X[:, i], y)[0, 1])
            correlations.append(0.0 if np.isnan(corr) else corr)
        
        # 归一化
        corr_sum = sum(correlations)
        if corr_sum > 0:
            correlations = [c / corr_sum for c in correlations]
        else:
            correlations = [1.0 / len(correlations)] * len(correlations)
        
        return dict(zip(param_names, correlations))
    
    def _parallel_evaluate(self, param_combinations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """评估参数组合（优化版多进程）"""
        
        results = []
        total_count = len(param_combinations)
        # 检查系统资源
        resource_status = check_system_resources()
        
        # 详细检查多进程启用条件
        conditions = {
            'enable_multiprocessing': self.enable_multiprocessing,
            'max_workers_gt_1': self.max_workers > 1,
            'task_count_sufficient': total_count >= 3,
            'system_resources_safe': resource_status['safe_to_proceed']
        }
        
        # 智能决策是否使用多进程
        use_multiprocessing = all(conditions.values())
        
        # 动态调整并发数
        if use_multiprocessing:
            # 根据任务数量和系统资源动态调整并发数
            if total_count < 10:
                actual_workers = min(2, self.max_workers)
            elif total_count < 50:
                actual_workers = min(4, self.max_workers)
            else:
                actual_workers = self.max_workers
                
            # 根据内存使用率进一步调整
            if resource_status['memory_percent'] > 70:
                actual_workers = max(1, actual_workers // 2)
                logger.info(f"内存使用率较高({resource_status['memory_percent']:.1f}%)，调整并发数为: {actual_workers}")
        else:
            actual_workers = 1
        
        if actual_workers == 1 or not use_multiprocessing:
            # 串行处理
            print(f"\n📊 开始参数优化回测（串行模式）...")
            print(f"总回测数量: {total_count}")
            if not use_multiprocessing:
                print("原因: 多进程已禁用或条件不满足")
                
                # 显示具体不满足的条件
                failed_conditions = []
                if not conditions['enable_multiprocessing']:
                    failed_conditions.append("❌ 多进程功能被禁用 (enable_multiprocessing=False)")
                if not conditions['max_workers_gt_1']:
                    failed_conditions.append(f"❌ 工作进程数不足 (max_workers={self.max_workers})")
                if not conditions['task_count_sufficient']:
                    failed_conditions.append(f"❌ 任务数量太少 (需要≥3个，当前{total_count}个)")
                if not conditions['system_resources_safe']:
                    failed_conditions.append(f"❌ 系统资源不足 (内存:{resource_status['memory_percent']:.1f}%, CPU:{resource_status['cpu_percent']:.1f}%)")
                
                for condition in failed_conditions:
                    print(f"      {condition}")
                    
            print("=" * 50)
            
            for i, params in enumerate(param_combinations, 1):
                result = self._evaluate_parameter_set(params)
                results.append(result)
                
                # 显示简洁的进度信息
                if i % max(1, total_count // 20) == 0 or i == total_count:
                    progress_percent = (i / total_count) * 100
                    print(f"\r🔄 回测进度: {i}/{total_count} ({progress_percent:.1f}%)", end='', flush=True)
                    
                # 定期检查内存使用和垃圾回收
                if i % 10 == 0:
                    gc.collect()
            
            print("\n" + "=" * 50)
            print("✅ 参数回测完成!")
                    
        else:
            # 多进程并行处理
            print(f"\n🚀 开始并行参数优化回测...")
            print(f"总回测数量: {total_count}, 并发进程数: {actual_workers}")
            print(f"系统状态: CPU {resource_status['cpu_percent']:.1f}%, 内存 {resource_status['memory_percent']:.1f}%")
            print("=" * 50)
            
            try:
                with ProcessPoolExecutor(max_workers=actual_workers) as executor:
                    # 提交所有任务
                    future_to_params = {
                        executor.submit(self._evaluate_parameter_set, params): params
                        for params in param_combinations
                    }
                    
                    completed_count = 0
                    failed_count = 0
                    start_time = time.time()
                    
                    # 处理完成的任务
                    for future in as_completed(future_to_params):
                        try:
                            # 设置超时时间（根据任务复杂度调整）
                            timeout = 180 if total_count > 100 else 120  # 大批量任务给更多时间
                            result = future.result(timeout=timeout)
                            results.append(result)
                            completed_count += 1
                            
                            # 显示详细的进度信息
                            if completed_count % max(1, total_count // 20) == 0 or completed_count == total_count:
                                progress_percent = (completed_count / total_count) * 100
                                elapsed_time = time.time() - start_time
                                avg_time_per_task = elapsed_time / completed_count
                                remaining_tasks = total_count - completed_count
                                eta_seconds = remaining_tasks * avg_time_per_task
                                
                                print(f"\r🔄 回测进度: {completed_count}/{total_count} ({progress_percent:.1f}%) | "
                                      f"平均耗时: {avg_time_per_task:.2f}s/任务 | "
                                      f"预计剩余: {eta_seconds:.0f}s", end='', flush=True)
                            
                        except Exception as e:
                            logger.warning(f"参数评估任务失败: {str(e)}")
                            results.append({self.performance_metric: -np.inf})
                            failed_count += 1
                            completed_count += 1
                            
                            # 如果失败率过高，考虑降级到串行模式
                            if failed_count > total_count * 0.3:  # 失败率超过30%
                                logger.error("多进程失败率过高，建议检查回测函数的多进程兼容性")
                    
                    total_time = time.time() - start_time
                    print(f"\n📈 并行处理统计:")
                    print(f"   总耗时: {total_time:.2f}秒")
                    print(f"   平均速度: {total_count/total_time:.2f}任务/秒")
                    print(f"   成功率: {(total_count-failed_count)/total_count*100:.1f}%")
                    if failed_count > 0:
                        print(f"   失败任务: {failed_count}个")
                            
            except Exception as e:
                print(f"\n⚠️ 多进程处理失败，自动切换到串行模式: {str(e)}")
                logger.warning(f"多进程执行异常: {e}")
                
                # 降级到串行处理
                results = []
                print(f"🔄 串行模式重新处理...")
                for i, params in enumerate(param_combinations, 1):
                    result = self._evaluate_parameter_set(params)
                    results.append(result)
                    if i % max(1, total_count // 20) == 0 or i == total_count:
                        progress_percent = (i / total_count) * 100
                        print(f"\r🔄 回测进度: {i}/{total_count} ({progress_percent:.1f}%)", end='', flush=True)
            
            print("\n" + "=" * 50)
            print("✅ 参数回测完成!")
        
        # 最终统计
        valid_results = [r for r in results if r.get(self.performance_metric, -np.inf) != -np.inf]
        logger.info(f"参数评估完成 - 总数: {total_count}, 有效: {len(valid_results)}, 并发数: {actual_workers if use_multiprocessing else 1}")
        
        return results
    
    def calculate_stability_metrics(
        self,
        params: Dict[str, Any],
        n_monte_carlo: int = 20,  # 减少默认次数
        n_walk_forward: int = 3,  # 减少默认次数
        perturbation_ratio: float = 0.1
    ) -> StabilityMetrics:
        """
        计算参数的稳定性指标（安全版本）
        
        Args:
            params: 参数字典
            n_monte_carlo: 蒙特卡洛测试次数
            n_walk_forward: Walk-Forward验证次数
            perturbation_ratio: 参数扰动比例
        """
        # 安全模式下进一步减少计算量
        if self.safe_mode:
            n_monte_carlo = min(n_monte_carlo, 10)
            n_walk_forward = min(n_walk_forward, 2)
        
        # 1. 性能稳定性 (蒙特卡洛测试)
        performance_stability = self._monte_carlo_stability(params, n_monte_carlo)
        
        # 2. 参数敏感性 (参数扰动测试)
        parameter_sensitivity = self._parameter_sensitivity_test(params, perturbation_ratio)
        
        # 3. 时间一致性 (Walk-Forward验证)
        temporal_consistency = self._walk_forward_consistency(params, n_walk_forward)
        
        # 4. 鲁棒性得分 (综合指标)
        robustness_score = np.mean([
            performance_stability,
            parameter_sensitivity,
            temporal_consistency
        ])
        
        # 5. 总体稳定性得分
        overall_stability = (
            0.4 * performance_stability +
            0.3 * parameter_sensitivity +
            0.3 * temporal_consistency
        )
        
        return StabilityMetrics(
            performance_stability=performance_stability,
            parameter_sensitivity=parameter_sensitivity,
            temporal_consistency=temporal_consistency,
            robustness_score=robustness_score,
            overall_stability=overall_stability
        )
    
    def _monte_carlo_stability(self, params: Dict[str, Any], n_trials: int) -> float:
        """蒙特卡洛稳定性测试"""
        performances = []
        
        # 基准性能
        base_result = self._evaluate_parameter_set(params)
        base_performance = base_result.get(self.performance_metric, 0)
        
        # 多次随机测试 (模拟数据随机性)
        for _ in range(n_trials):
            try:
                # 这里可以添加数据扰动逻辑
                # 目前使用相同参数多次评估来模拟随机性
                result = self._evaluate_parameter_set(params)
                performance = result.get(self.performance_metric, 0)
                performances.append(performance)
            except:
                performances.append(base_performance)
        
        # 计算稳定性 (变异系数的倒数)
        if len(performances) > 1 and np.std(performances) > 0:
            cv = np.std(performances) / (np.abs(np.mean(performances)) + 1e-8)
            stability = 1.0 / (1.0 + cv)
        else:
            stability = 1.0
        
        return min(1.0, max(0.0, stability))
    
    def _parameter_sensitivity_test(self, params: Dict[str, Any], perturbation_ratio: float) -> float:
        """参数敏感性测试"""
        base_result = self._evaluate_parameter_set(params)
        base_performance = base_result.get(self.performance_metric, 0)
        
        sensitivity_scores = []
        
        for param_name, param_value in params.items():
            min_val, max_val, step, param_type = self.param_spaces[param_name]
            
            # 计算扰动范围
            param_range = max_val - min_val
            perturbation = param_range * perturbation_ratio
            
            # 正向扰动
            perturbed_value = param_value + perturbation
            if param_type == int:
                perturbed_value = int(np.clip(perturbed_value, min_val, max_val))
            else:
                perturbed_value = np.clip(perturbed_value, min_val, max_val)
            
            perturbed_params = params.copy()
            perturbed_params[param_name] = perturbed_value
            
            try:
                perturbed_result = self._evaluate_parameter_set(perturbed_params)
                perturbed_performance = perturbed_result.get(self.performance_metric, 0)
                
                # 计算性能变化比例
                if base_performance != 0:
                    performance_change = abs(perturbed_performance - base_performance) / abs(base_performance)
                else:
                    performance_change = abs(perturbed_performance)
                
                # 敏感性 = 性能变化 / 参数变化
                param_change = abs(perturbed_value - param_value) / (param_range + 1e-8)
                sensitivity = performance_change / (param_change + 1e-8)
                
                sensitivity_scores.append(sensitivity)
                
            except:
                # 如果扰动导致错误，说明参数很敏感
                sensitivity_scores.append(10.0)
        
        # 计算总体敏感性 (敏感性越低越好)
        avg_sensitivity = np.mean(sensitivity_scores)
        sensitivity_stability = 1.0 / (1.0 + avg_sensitivity)
        
        return min(1.0, max(0.0, sensitivity_stability))
    
    def _walk_forward_consistency(self, params: Dict[str, Any], n_splits: int) -> float:
        """Walk-Forward时间一致性测试"""
        try:
            # 这里需要根据实际回测器实现时间分割逻辑
            # 目前使用简化版本：多次评估取方差
            performances = []
            
            for _ in range(n_splits):
                result = self._evaluate_parameter_set(params)
                performance = result.get(self.performance_metric, 0)
                performances.append(performance)
            
            # 计算时间一致性
            if len(performances) > 1 and np.std(performances) > 0:
                cv = np.std(performances) / (np.abs(np.mean(performances)) + 1e-8)
                consistency = 1.0 / (1.0 + cv)
            else:
                consistency = 1.0
            
            return min(1.0, max(0.0, consistency))
            
        except Exception as e:
            logger.warning(f"Walk-Forward测试失败: {str(e)}")
            return 0.5
    
    def calculate_stability_score(
        self,
        params: Dict[str, Any],
        performance: float,
        stability_metrics: Optional[StabilityMetrics] = None
    ) -> float:
        """
        计算综合稳定性评分
        
        稳定性评分 = 稳定性权重 × 稳定性指标 + (1-权重) × 标准化性能
        """
        if stability_metrics is None:
            stability_metrics = self.calculate_stability_metrics(params)
        
        # 性能标准化 (假设合理范围)
        performance_normalized = self._normalize_performance(performance)
        
        # 综合评分
        stability_score = (
            self.stability_weight * stability_metrics.overall_stability +
            (1.0 - self.stability_weight) * performance_normalized
        )
        
        return stability_score
    
    def _normalize_performance(self, performance: float) -> float:
        """性能标准化"""
        # 这里可以根据具体指标调整
        if self.performance_metric.lower() in ['sharpe_ratio', 'sharpe']:
            # Sharpe比率：好的范围 [0, 3]
            return min(1.0, max(0.0, performance / 3.0))
        elif self.performance_metric.lower() in ['return', 'total_return']:
            # 收益率：假设好的范围 [0, 100%]
            return min(1.0, max(0.0, performance))
        else:
            # 默认：假设正值越大越好
            return min(1.0, max(0.0, performance / (abs(performance) + 1.0)))
    
    def stability_optimization(
        self,
        max_evaluations: int = 300,  # 减少默认评估次数
        n_initial_samples: int = 50,  # 减少初始采样
        convergence_threshold: float = 1e-6,
        max_stagnant_iterations: int = 20  # 减少停滞迭代次数
    ) -> OptimizationResult:
        """
        执行稳定性导向优化（安全版本）
        
        优化流程：
        1. 参数重要性分析
        2. 全局搜索阶段
        3. 贝叶斯优化阶段  
        4. 精细搜索阶段
        5. 稳定性验证
        """
        # 安全模式下进一步限制
        if self.safe_mode:
            max_evaluations = min(max_evaluations, 100)
            n_initial_samples = min(n_initial_samples, 50)
        
        logger.info("开始稳定性导向优化（安全版本）")
        start_time = time.time()
        
        # 第一阶段：参数重要性分析
        if not self.importance_scores:
            logger.info("第一阶段：参数重要性分析")
            self.analyze_parameter_importance(n_samples=n_initial_samples)
        
        # 第二阶段：全局搜索
        logger.info("第二阶段：自适应全局搜索")
        global_results = self._global_search_phase(max_evaluations // 2)
        
        # 第三阶段：精细搜索（跳过贝叶斯优化以提高安全性）
        logger.info("第三阶段：最优区域精细搜索")
        final_results = self._fine_search_phase(
                global_results,
            max_evaluations // 2
        )
        
        # 第四阶段：稳定性验证
        logger.info("第四阶段：最终稳定性验证")
        best_result = self._final_stability_validation(final_results)
        
        optimization_time = time.time() - start_time
        logger.info(f"稳定性优化完成 - 用时: {optimization_time:.2f}秒")
        
        return best_result
    
    def _global_search_phase(self, max_evaluations: int) -> List[Tuple[Dict[str, Any], float, StabilityMetrics]]:
        """全局搜索阶段"""
        # 使用自适应采样生成参数组合
        param_combinations = self._generate_param_combinations(
            n_samples=max_evaluations,
            sampling_strategy='adaptive'
        )
        
        # 并行评估
        results = self._parallel_evaluate(param_combinations)
        
        # 计算稳定性指标和评分
        evaluated_results = []
        for params, result in zip(param_combinations, results):
            performance = result.get(self.performance_metric, -np.inf)
            
            if performance != -np.inf:
                # 计算稳定性指标
                stability_metrics = self.calculate_stability_metrics(params)
                evaluated_results.append((params, performance, stability_metrics))
        
        # 按稳定性评分排序
        evaluated_results.sort(
            key=lambda x: self.calculate_stability_score(x[0], x[1], x[2]),
            reverse=True
        )
        
        return evaluated_results[:20]  # 返回前20个结果
    
    def _fine_search_phase(
        self,
        candidate_results: List[Tuple[Dict[str, Any], float, StabilityMetrics]],
        max_evaluations: int
    ) -> List[Tuple[Dict[str, Any], float, StabilityMetrics]]:
        """精细搜索阶段"""
        fine_results = candidate_results.copy()
        
        # 选择最佳候选参数进行邻域搜索
        top_candidates = candidate_results[:5]  # 减少候选数量
        
        evaluations_per_candidate = max_evaluations // len(top_candidates)
        
        for base_params, _, _ in top_candidates:
            # 在候选参数附近生成邻域
            neighbor_params_list = self._generate_neighborhood(
                base_params,
                n_neighbors=evaluations_per_candidate,
                radius_ratio=0.1
            )
            
            # 评估邻域参数
            neighbor_results = self._parallel_evaluate(neighbor_params_list)
            
            for params, result in zip(neighbor_params_list, neighbor_results):
                performance = result.get(self.performance_metric, -np.inf)
                
                if performance != -np.inf:
                    stability_metrics = self.calculate_stability_metrics(params)
                    fine_results.append((params, performance, stability_metrics))
        
        # 最终排序
        fine_results.sort(
            key=lambda x: self.calculate_stability_score(x[0], x[1], x[2]),
            reverse=True
        )
        
        return fine_results[:20]  # 返回前20个结果
    
    def _generate_neighborhood(
        self,
        base_params: Dict[str, Any],
        n_neighbors: int,
        radius_ratio: float = 0.1
    ) -> List[Dict[str, Any]]:
        """生成参数邻域"""
        neighbors = []
        
        for _ in range(n_neighbors):
            neighbor_params = base_params.copy()
            
            for param_name, base_value in base_params.items():
                min_val, max_val, step, param_type = self.param_spaces[param_name]
                
                # 计算邻域半径
                param_range = max_val - min_val
                radius = param_range * radius_ratio
                
                # 生成邻域值
                if param_type == int:
                    neighbor_value = base_value + np.random.randint(
                        -max(1, int(radius)), max(1, int(radius)) + 1
                    )
                    neighbor_value = max(int(min_val), min(int(max_val), neighbor_value))
                else:
                    neighbor_value = base_value + np.random.uniform(-radius, radius)
                    neighbor_value = max(min_val, min(max_val, neighbor_value))
                    # 对齐到步长
                    neighbor_value = round(neighbor_value / step) * step
                
                neighbor_params[param_name] = neighbor_value
            
            neighbors.append(neighbor_params)
        
        return neighbors
    
    def _final_stability_validation(
        self,
        candidate_results: List[Tuple[Dict[str, Any], float, StabilityMetrics]]
    ) -> OptimizationResult:
        """最终稳定性验证"""
        if not candidate_results:
            raise ValueError("没有有效的优化结果")
        
        # 选择最佳候选进行深度验证
        best_candidate = candidate_results[0]
        best_params, best_performance, best_stability_metrics = best_candidate
        
        # 进行更严格的稳定性测试（安全版本）
        enhanced_stability_metrics = self.calculate_stability_metrics(
            best_params,
            n_monte_carlo=30 if not self.safe_mode else 10,
            n_walk_forward=5 if not self.safe_mode else 2,
            perturbation_ratio=0.05
        )
        
        # 计算最终稳定性评分
        final_stability_score = self.calculate_stability_score(
            best_params,
            best_performance,
            enhanced_stability_metrics
        )
        
        # 构建优化结果
        optimization_result = OptimizationResult(
            best_params=best_params,
            best_performance=best_performance,
            stability_metrics=enhanced_stability_metrics,
            stability_score=final_stability_score,
            parameter_importance=self.importance_scores,
            validation_results={
                'n_evaluations': len(self.evaluation_cache),
                'optimization_candidates': len(candidate_results),
                'final_validation_passed': True,
                'safe_mode': self.safe_mode
            }
        )
        
        return optimization_result
    
    def save_stability_report(
        self,
        optimization_result: OptimizationResult,
        filepath: str
    ):
        """保存稳定性优化报告"""
        
        def convert_numpy_types(obj):
            """递归转换numpy类型为Python原生类型"""
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, type):
                return obj.__name__  # 将type对象转换为字符串
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj
        
        report = {
            'optimization_summary': {
                'best_params': convert_numpy_types(optimization_result.best_params),
                'best_performance': convert_numpy_types(optimization_result.best_performance),
                'stability_score': convert_numpy_types(optimization_result.stability_score),
                'optimization_date': datetime.now().isoformat()
            },
            'stability_metrics': {
                'performance_stability': convert_numpy_types(optimization_result.stability_metrics.performance_stability),
                'parameter_sensitivity': convert_numpy_types(optimization_result.stability_metrics.parameter_sensitivity),
                'temporal_consistency': convert_numpy_types(optimization_result.stability_metrics.temporal_consistency),
                'robustness_score': convert_numpy_types(optimization_result.stability_metrics.robustness_score),
                'overall_stability': convert_numpy_types(optimization_result.stability_metrics.overall_stability)
            },
            'parameter_importance': convert_numpy_types(optimization_result.parameter_importance),
            'validation_results': convert_numpy_types(optimization_result.validation_results),
            'optimization_config': {
                'param_spaces': convert_numpy_types(self.param_spaces),
                'stability_weight': convert_numpy_types(self.stability_weight),
                'performance_metric': self.performance_metric,
                'max_workers': self.max_workers,
                'safe_mode': self.safe_mode
            }
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"稳定性优化报告已保存到: {filepath}")


# 辅助函数
def create_stability_optimizer(
    backtester: Callable,
    param_spaces: Dict[str, Tuple[float, float, float, type]],
    stability_weight: float = 0.4,
    safe_mode: bool = True,
    silent_backtest: bool = True,
    **kwargs
) -> EnhancedStabilityOptimizer:
    """创建增强稳定性优化器的便捷函数"""
    return EnhancedStabilityOptimizer(
        backtester=backtester,
        param_spaces=param_spaces,
        stability_weight=stability_weight,
        safe_mode=safe_mode,
        silent_backtest=silent_backtest,
        **kwargs
    )


# 示例用法
if __name__ == "__main__":
    # 这里可以添加示例代码
    pass 