# 智能期货交易系统使用说明

## 🚀 快速启动

### 方法1：修复版启动器（推荐）
```bash
cd intelligent_trading_system
python run_fixed.py
```

### 方法2：原版启动器
```bash
python launch.py
```

### 方法3：测试系统
```bash
python test_system.py
```

## 🎯 系统功能

### 1. 品种动态筛选引擎
- **多维度评估**：动量、波动、胜率、流动性、相关性
- **自动推荐**：从25+品种中筛选出最优5个
- **实时更新**：每日更新品种健康度

### 2. 参数自适应优化模块
- **Walk-Forward分析**：防止过拟合
- **Calmar比率优化**：平衡收益与风险
- **参数稳定性检验**：确保参数鲁棒性

### 3. 资金智能管理系统
- **动态头寸计算**：基于ATR和账户净值
- **多层风险控制**：单品种2%、组合15%、日内3%
- **实时监控**：风险预警和自动处置

### 4. vnpy策略集成
- **自动加载**：支持加载vnpy的CTA策略
- **策略适配**：自动适配vnpy策略到系统中
- **参数管理**：统一的参数配置界面

## 📊 GUI界面功能

### 系统控制
- 启动/停止系统
- 账户净值设置
- 风险模式选择
- 配置保存/加载

### 策略管理
- 加载vnpy策略
- 策略选择和应用
- 策略参数配置
- 策略详情查看

### 品种筛选
- 执行品种筛选
- 筛选结果展示
- 筛选报告生成

### 参数优化
- 执行参数优化
- 优化结果展示
- 参数稳定性分析

### 风险监控
- 实时风险指标
- 风险预警信息
- 组合风险分析

### 回测分析
- 策略回测执行
- 权益曲线展示
- 回测统计分析

### 实时监控
- 系统健康评分
- 性能指标监控
- 实时日志显示

## 💡 使用示例

### 500万账户配置示例
```python
# 品种筛选结果
selected_instruments = ['RB888.SHFE', 'SC888.INE', 'AU888.SHFE']

# 资金分配
positions = {
    'RB888.SHFE': 10,  # 手数，风险金额: 100,000元
    'SC888.INE': 4,    # 手数，风险金额: 100,000元  
    'AU888.SHFE': 9    # 手数，风险金额: 100,000元
}

# 风险控制
total_risk = 300000  # 总风险: 6%
portfolio_risk = 0.06  # 组合风险比例
```

## 🔧 环境要求

### 必需依赖
```bash
Python >= 3.7
pandas >= 1.3.0
numpy >= 1.21.0
```

### 可选依赖
```bash
matplotlib >= 3.0.0  # 用于GUI图表
tkinter              # 用于GUI界面
vnpy                 # 用于策略集成
```

## 📁 文件结构

```
intelligent_trading_system/
├── run_fixed.py                 # 🚀 修复版启动脚本（推荐）
├── launch.py                    # 🚀 原版启动脚本
├── test_system.py              # 🧪 系统测试脚本
├── gui_interface.py            # 🖥️ GUI图形界面
├── main_system.py              # 🎯 主系统集成
├── contract_manager.py         # 📋 合约信息管理
├── strategy_loader.py          # 📈 策略加载器
├── core/                       # 核心模块
│   ├── instrument_selector.py  # 🔍 品种筛选引擎
│   ├── parameter_optimizer.py  # ⚙️ 参数优化模块
│   ├── risk_manager.py        # 🛡️ 风险管理系统
│   ├── data_manager.py        # 📊 数据管理模块
│   ├── monitor.py             # 📈 监控系统
│   └── backtest_engine.py     # 🔄 回测引擎
├── strategies/                 # 策略模块
│   └── adaptive_trend_strategy.py # 📊 自适应趋势策略
├── config.json                # ⚙️ 配置文件
└── README.md                  # 📖 详细说明文档
```

## 🎉 开始使用

### 首次使用
1. 运行 `python run_fixed.py`
2. 选择"1. 简单功能演示"了解基本功能
3. 选择"3. 启动GUI界面"进行完整操作

### 策略使用
1. 在GUI的"策略管理"选项卡中
2. 点击"加载vnpy策略"加载更多策略
3. 双击策略查看详细信息
4. 选择策略并点击"应用策略"

### 系统配置
1. 在"系统控制"选项卡中设置账户净值
2. 选择合适的风险模式
3. 点击"保存配置"保存设置

### 品种筛选
1. 在"品种筛选"选项卡中
2. 点击"执行品种筛选"
3. 查看筛选结果和报告

### 参数优化
1. 在"参数优化"选项卡中
2. 点击"执行参数优化"
3. 查看优化结果和稳定性

### 回测分析
1. 在"回测分析"选项卡中
2. 设置回测日期范围
3. 点击"运行回测"查看结果

## ⚠️ 注意事项

1. **首次运行**：建议先运行测试脚本确保环境正常
2. **数据源**：系统会自动尝试连接vnpy数据库，如果失败会使用模拟数据
3. **策略加载**：vnpy策略需要vnpy环境支持，如果没有会只显示内置策略
4. **风险控制**：系统默认采用保守的风险控制参数
5. **资金管理**：建议从小资金开始测试，确认无误后再扩大规模

## 🆘 常见问题

### Q: GUI启动失败
A: 检查是否安装了tkinter和matplotlib：`pip install matplotlib`

### Q: 策略加载失败
A: 确保vnpy环境正确安装，或者只使用内置策略

### Q: 数据获取失败
A: 系统会自动使用模拟数据，不影响功能测试

### Q: 优化速度慢
A: 可以在配置中减少优化参数范围或降低并行度

## 📞 技术支持

- 详细文档：查看 README.md
- 系统测试：运行 test_system.py
- 问题排查：查看系统日志和错误信息
- 功能演示：运行简单演示了解各模块功能

---

**🎉 开始使用智能期货交易系统，告别传统策略痛点！**
