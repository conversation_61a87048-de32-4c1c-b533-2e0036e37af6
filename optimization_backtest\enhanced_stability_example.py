"""
增强稳定性优化器使用示例

本示例展示如何使用EnhancedStabilityOptimizer进行稳定性导向的参数优化

功能演示：
1. 模拟量化策略回测器
2. 参数重要性分析
3. 稳定性导向优化
4. 结果分析和报告生成

作者: AI Assistant
"""

import numpy as np
import pandas as pd
import time
import json
from typing import Dict, Any
import matplotlib.pyplot as plt
import seaborn as sns
from enhanced_stability_optimizer import EnhancedStabilityOptimizer

# 设置中文字体（可选）
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class MockQuantStrategy:
    """模拟量化策略回测器"""
    
    def __init__(self, data_length: int = 1000, random_seed: int = 42):
        """
        初始化模拟策略
        
        Args:
            data_length: 模拟数据长度
            random_seed: 随机种子
        """
        np.random.seed(random_seed)
        
        # 生成模拟价格数据
        self.prices = self._generate_mock_prices(data_length)
        self.returns = np.diff(np.log(self.prices))
        
        # 添加一些噪声来模拟真实市场的不确定性
        self.noise_factor = 0.1
    
    def _generate_mock_prices(self, length: int) -> np.ndarray:
        """生成模拟价格序列"""
        # 使用几何布朗运动生成价格
        dt = 1/252  # 日频数据
        mu = 0.08   # 年化收益率
        sigma = 0.2 # 年化波动率
        
        # 随机游走
        random_walk = np.random.normal(0, 1, length)
        prices = [100]  # 初始价格
        
        for i in range(1, length):
            drift = mu * dt
            diffusion = sigma * np.sqrt(dt) * random_walk[i]
            price_change = prices[-1] * (drift + diffusion)
            new_price = prices[-1] + price_change
            prices.append(max(new_price, 1))  # 确保价格为正
        
        return np.array(prices)
    
    def backtest(self, params: Dict[str, Any]) -> Dict[str, float]:
        """
        执行回测
        
        Args:
            params: 策略参数字典
                - atr_window: ATR计算窗口
                - sl_multiplier: 止损倍数
                - ping_zy: 持仓天数
                - entry_threshold: 入场阈值
        
        Returns:
            性能指标字典
        """
        try:
            # 提取参数
            atr_window = int(params.get('atr_window', 20))
            sl_multiplier = float(params.get('sl_multiplier', 2.0))
            ping_zy = int(params.get('ping_zy', 10))
            entry_threshold = float(params.get('entry_threshold', 1.5))
            
            # 添加参数有效性检查
            if atr_window < 5 or atr_window > 100:
                return {'sharpe_ratio': -10, 'total_return': -0.5, 'max_drawdown': 0.8}
            
            # 计算技术指标
            atr = self._calculate_atr(atr_window)
            signals = self._generate_signals(atr, entry_threshold)
            
            # 执行交易模拟
            trades = self._simulate_trades(signals, sl_multiplier, ping_zy, atr)
            
            # 计算性能指标
            performance = self._calculate_performance(trades)
            
            # 添加噪声模拟真实回测的不确定性
            noise = np.random.normal(0, self.noise_factor)
            performance['sharpe_ratio'] *= (1 + noise * 0.1)
            performance['total_return'] *= (1 + noise * 0.05)
            
            return performance
            
        except Exception as e:
            print(f"回测异常: {e}")
            return {'sharpe_ratio': -10, 'total_return': -0.5, 'max_drawdown': 0.8}
    
    def _calculate_atr(self, window: int) -> np.ndarray:
        """计算平均真实波幅(ATR)"""
        high_low = np.abs(np.roll(self.prices, -1) - self.prices)[:-1]
        high_close = np.abs(self.prices[1:] - self.prices[:-1])
        low_close = np.abs(self.prices[1:] - self.prices[:-1])
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        
        # 简单移动平均
        atr = np.convolve(true_range, np.ones(window)/window, mode='valid')
        
        # 补齐长度
        atr = np.concatenate([np.full(len(self.prices) - len(atr), atr[0]), atr])
        
        return atr
    
    def _generate_signals(self, atr: np.ndarray, threshold: float) -> np.ndarray:
        """生成交易信号"""
        # 基于价格变化和ATR的简单策略
        price_changes = np.diff(self.prices)
        normalized_changes = price_changes / (atr[1:] + 1e-8)
        
        signals = np.zeros(len(self.prices))
        
        # 买入信号：价格变化超过ATR的threshold倍
        buy_condition = normalized_changes > threshold
        signals[1:][buy_condition] = 1
        
        # 卖出信号：价格变化低于-threshold倍
        sell_condition = normalized_changes < -threshold
        signals[1:][sell_condition] = -1
        
        return signals
    
    def _simulate_trades(
        self,
        signals: np.ndarray,
        sl_multiplier: float,
        holding_period: int,
        atr: np.ndarray
    ) -> List[Dict[str, float]]:
        """模拟交易执行"""
        trades = []
        position = 0
        entry_price = 0
        entry_index = 0
        
        for i, signal in enumerate(signals):
            current_price = self.prices[i]
            
            # 开仓逻辑
            if position == 0 and signal != 0:
                position = signal
                entry_price = current_price
                entry_index = i
                
            # 平仓逻辑
            elif position != 0:
                should_close = False
                exit_reason = ""
                
                # 止损检查
                if position == 1:  # 多头
                    stop_loss = entry_price - sl_multiplier * atr[entry_index]
                    if current_price <= stop_loss:
                        should_close = True
                        exit_reason = "stop_loss"
                else:  # 空头
                    stop_loss = entry_price + sl_multiplier * atr[entry_index]
                    if current_price >= stop_loss:
                        should_close = True
                        exit_reason = "stop_loss"
                
                # 时间止损
                if i - entry_index >= holding_period:
                    should_close = True
                    exit_reason = "time_exit"
                
                # 反向信号
                if signal != 0 and signal != position:
                    should_close = True
                    exit_reason = "signal_reverse"
                
                # 执行平仓
                if should_close:
                    pnl = position * (current_price - entry_price) / entry_price
                    
                    trade = {
                        'entry_index': entry_index,
                        'exit_index': i,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'position': position,
                        'pnl': pnl,
                        'exit_reason': exit_reason,
                        'holding_days': i - entry_index
                    }
                    trades.append(trade)
                    
                    position = 0
                    entry_price = 0
                    entry_index = 0
        
        return trades
    
    def _calculate_performance(self, trades: List[Dict[str, float]]) -> Dict[str, float]:
        """计算策略性能指标"""
        if not trades:
            return {
                'sharpe_ratio': 0,
                'total_return': 0,
                'max_drawdown': 0,
                'win_rate': 0,
                'profit_factor': 1,
                'avg_trade_return': 0,
                'trade_count': 0
            }
        
        # 提取收益序列
        returns = [trade['pnl'] for trade in trades]
        cumulative_returns = np.cumsum(returns)
        
        # 基本统计
        total_return = cumulative_returns[-1] if cumulative_returns.size > 0 else 0
        avg_return = np.mean(returns)
        return_std = np.std(returns) if len(returns) > 1 else 0
        
        # Sharpe比率
        sharpe_ratio = avg_return / (return_std + 1e-8) * np.sqrt(252)
        
        # 最大回撤
        peak = np.maximum.accumulate(cumulative_returns)
        drawdowns = peak - cumulative_returns
        max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0
        
        # 胜率
        winning_trades = [r for r in returns if r > 0]
        win_rate = len(winning_trades) / len(returns) if returns else 0
        
        # 盈亏比
        avg_win = np.mean(winning_trades) if winning_trades else 0
        losing_trades = [r for r in returns if r < 0]
        avg_loss = np.mean(losing_trades) if losing_trades else 0
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 1
        
        return {
            'sharpe_ratio': sharpe_ratio,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_trade_return': avg_return,
            'trade_count': len(trades)
        }


def run_enhanced_stability_optimization_example():
    """运行增强稳定性优化示例"""
    print("=" * 60)
    print("增强稳定性导向参数优化示例")
    print("=" * 60)
    
    # 1. 创建模拟策略
    print("\n1. 初始化模拟量化策略...")
    strategy = MockQuantStrategy(data_length=2000, random_seed=42)
    
    # 2. 定义参数空间 (新格式：min, max, step, type)
    print("\n2. 定义参数优化空间...")
    param_spaces = {
        'atr_window': (10, 50, 2, int),      # ATR计算窗口：10-50，步长2
        'sl_multiplier': (1.5, 4.0, 0.2, float),  # 止损倍数：1.5-4.0，步长0.2
        'ping_zy': (5, 25, 2, int),          # 持仓天数：5-25，步长2
        'entry_threshold': (1.0, 3.0, 0.2, float) # 入场阈值：1.0-3.0，步长0.2
    }
    
    print("参数空间:")
    for param_name, (min_val, max_val, step, param_type) in param_spaces.items():
        print(f"  {param_name}: [{min_val}, {max_val}], 步长={step}, 类型={param_type.__name__}")
    
    # 3. 创建增强稳定性优化器
    print("\n3. 创建增强稳定性优化器...")
    optimizer = EnhancedStabilityOptimizer(
        backtester=strategy.backtest,
        param_spaces=param_spaces,
        stability_weight=0.4,  # 40%权重给稳定性，60%给性能
        performance_metric='sharpe_ratio',
        max_workers=4,
        random_seed=42
    )
    
    # 4. 参数重要性分析
    print("\n4. 执行参数重要性分析...")
    start_time = time.time()
    
    importance_scores = optimizer.analyze_parameter_importance(
        n_samples=300,  # 使用300个样本进行重要性分析
        method='xgboost'  # 使用XGBoost方法
    )
    
    analysis_time = time.time() - start_time
    print(f"参数重要性分析完成 (用时: {analysis_time:.2f}秒)")
    print("参数重要性排序:")
    
    sorted_importance = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)
    for param_name, importance in sorted_importance:
        print(f"  {param_name}: {importance:.4f}")
    
    # 5. 执行稳定性导向优化
    print("\n5. 执行稳定性导向优化...")
    start_time = time.time()
    
    # 执行优化（使用较少的评估次数用于演示）
    optimization_result = optimizer.stability_optimization(
        max_evaluations=600,    # 总评估次数
        n_initial_samples=200,  # 初始采样数
        convergence_threshold=1e-6,
        max_stagnant_iterations=30
    )
    
    optimization_time = time.time() - start_time
    print(f"稳定性优化完成 (用时: {optimization_time:.2f}秒)")
    
    # 6. 展示优化结果
    print("\n6. 优化结果分析")
    print("=" * 40)
    
    print(f"最优参数组合:")
    for param_name, value in optimization_result.best_params.items():
        print(f"  {param_name}: {value}")
    
    print(f"\n性能指标:")
    print(f"  Sharpe比率: {optimization_result.best_performance:.4f}")
    print(f"  稳定性评分: {optimization_result.stability_score:.4f}")
    
    print(f"\n稳定性指标详情:")
    metrics = optimization_result.stability_metrics
    print(f"  性能稳定性: {metrics.performance_stability:.4f}")
    print(f"  参数敏感性: {metrics.parameter_sensitivity:.4f}")
    print(f"  时间一致性: {metrics.temporal_consistency:.4f}")
    print(f"  鲁棒性得分: {metrics.robustness_score:.4f}")
    print(f"  总体稳定性: {metrics.overall_stability:.4f}")
    
    # 7. 保存详细报告
    print("\n7. 保存优化报告...")
    report_filename = f"stability_optimization_report_{int(time.time())}.json"
    optimizer.save_stability_report(optimization_result, report_filename)
    print(f"详细报告已保存到: {report_filename}")
    
    # 8. 对比分析（可选）
    print("\n8. 执行对比分析...")
    comparison_result = compare_with_traditional_optimization(
        strategy, param_spaces, optimization_result
    )
    
    print("传统优化 vs 稳定性导向优化:")
    print(f"  传统最优Sharpe: {comparison_result['traditional_sharpe']:.4f}")
    print(f"  稳定性最优Sharpe: {comparison_result['stability_sharpe']:.4f}")
    print(f"  稳定性提升: {comparison_result['stability_improvement']:.2%}")
    
    return optimization_result


def compare_with_traditional_optimization(
    strategy: MockQuantStrategy,
    param_spaces: Dict[str, tuple],
    stability_result
) -> Dict[str, float]:
    """对比传统优化方法"""
    print("执行传统参数优化进行对比...")
    
    # 传统方法：只关注性能，不考虑稳定性
    best_sharpe = -np.inf
    best_traditional_params = None
    
    # 简单网格搜索（采样200个组合）
    np.random.seed(42)
    for _ in range(200):
        params = {}
        for param_name, (min_val, max_val, step, param_type) in param_spaces.items():
            if param_type == int:
                value = np.random.randint(min_val, max_val + 1)
            else:
                value = np.random.uniform(min_val, max_val)
                value = round(value / step) * step
            params[param_name] = value
        
        try:
            result = strategy.backtest(params)
            sharpe = result.get('sharpe_ratio', -np.inf)
            
            if sharpe > best_sharpe:
                best_sharpe = sharpe
                best_traditional_params = params
                
        except:
            continue
    
    # 对最优传统参数进行稳定性测试
    traditional_optimizer = EnhancedStabilityOptimizer(
        backtester=strategy.backtest,
        param_spaces=param_spaces,
        stability_weight=0.4
    )
    
    traditional_stability = traditional_optimizer.calculate_stability_metrics(
        best_traditional_params
    )
    
    traditional_stability_score = traditional_optimizer.calculate_stability_score(
        best_traditional_params,
        best_sharpe,
        traditional_stability
    )
    
    print(f"传统优化最佳参数: {best_traditional_params}")
    print(f"传统优化稳定性评分: {traditional_stability_score:.4f}")
    
    # 计算改进
    stability_improvement = (
        (stability_result.stability_score - traditional_stability_score) /
        abs(traditional_stability_score + 1e-8)
    )
    
    return {
        'traditional_sharpe': best_sharpe,
        'traditional_stability_score': traditional_stability_score,
        'stability_sharpe': stability_result.best_performance,
        'stability_stability_score': stability_result.stability_score,
        'stability_improvement': stability_improvement
    }


def visualize_optimization_results(optimization_result):
    """可视化优化结果"""
    try:
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('增强稳定性优化结果分析', fontsize=16, fontweight='bold')
        
        # 1. 参数重要性
        importance_scores = optimization_result.parameter_importance
        param_names = list(importance_scores.keys())
        importance_values = list(importance_scores.values())
        
        axes[0, 0].bar(param_names, importance_values, color='skyblue')
        axes[0, 0].set_title('参数重要性分析')
        axes[0, 0].set_ylabel('重要性得分')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. 稳定性指标雷达图
        metrics = optimization_result.stability_metrics
        categories = ['性能稳定性', '参数敏感性', '时间一致性', '鲁棒性得分']
        values = [
            metrics.performance_stability,
            metrics.parameter_sensitivity,
            metrics.temporal_consistency,
            metrics.robustness_score
        ]
        
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        values += values[:1]  # 闭合雷达图
        angles += angles[:1]
        
        axes[0, 1].plot(angles, values, 'o-', linewidth=2, color='red')
        axes[0, 1].fill(angles, values, alpha=0.25, color='red')
        axes[0, 1].set_xticks(angles[:-1])
        axes[0, 1].set_xticklabels(categories)
        axes[0, 1].set_ylim(0, 1)
        axes[0, 1].set_title('稳定性指标雷达图')
        axes[0, 1].grid(True)
        
        # 3. 最优参数分布
        best_params = optimization_result.best_params
        param_names = list(best_params.keys())
        param_values = list(best_params.values())
        
        bars = axes[1, 0].bar(param_names, param_values, color='lightgreen')
        axes[1, 0].set_title('最优参数值')
        axes[1, 0].set_ylabel('参数值')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 在柱状图上添加数值标签
        for bar, value in zip(bars, param_values):
            height = bar.get_height()
            axes[1, 0].text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.2f}', ha='center', va='bottom')
        
        # 4. 综合评分对比
        scores = [
            optimization_result.best_performance,
            optimization_result.stability_score
        ]
        score_labels = ['性能得分\n(Sharpe比率)', '综合稳定性得分']
        
        colors = ['orange', 'purple']
        bars = axes[1, 1].bar(score_labels, scores, color=colors)
        axes[1, 1].set_title('性能与稳定性综合评分')
        axes[1, 1].set_ylabel('得分')
        
        # 添加数值标签
        for bar, score in zip(bars, scores):
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height,
                           f'{score:.4f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # 保存图表
        plt.savefig('optimization_results_analysis.png', dpi=300, bbox_inches='tight')
        print("优化结果可视化图表已保存到: optimization_results_analysis.png")
        
        # 显示图表（如果在支持的环境中）
        plt.show()
        
    except Exception as e:
        print(f"可视化过程中出现错误: {e}")
        print("请确保已安装matplotlib和seaborn库")


def main():
    """主函数"""
    try:
        # 运行完整的增强稳定性优化示例
        optimization_result = run_enhanced_stability_optimization_example()
        
        # 可视化结果（可选）
        print("\n9. 生成可视化分析图表...")
        try:
            visualize_optimization_results(optimization_result)
        except ImportError:
            print("matplotlib未安装，跳过可视化")
        except Exception as e:
            print(f"可视化失败: {e}")
        
        print("\n" + "=" * 60)
        print("增强稳定性优化示例运行完成！")
        print("=" * 60)
        
        # 输出快速使用提示
        print("\n快速使用提示:")
        print("1. 查看生成的JSON报告了解详细优化结果")
        print("2. 可以调整stability_weight参数平衡性能与稳定性")
        print("3. 增加max_evaluations获得更精确的优化结果")
        print("4. 根据参数重要性分析调整参数空间范围")
        
        return optimization_result
        
    except Exception as e:
        print(f"示例运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    # 运行示例
    result = main() 