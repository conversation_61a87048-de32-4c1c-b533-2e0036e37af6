{"success_count": 5, "failure_count": 0, "success_rate": 1.0, "batch_config": {}, "performance_stats": {"mean": 3.71511582067908, "std": 0.49325963136950035, "min": 3.117901069717541, "max": 4.557581072271267, "median": 3.7233382744316406}, "stability_stats": {"mean": 0.961830074499821, "std": 0.014091787609689465, "min": 0.9428342877434216, "max": 0.9840413815401398, "median": 0.9629900716224886}, "time_stats": {"mean": 27.839354753494263, "std": 1.8882060204900493, "total": 139.1967737674713}, "parameter_statistics": {"k_5": {"mean": 20.0, "std": 10.0, "min": 10, "max": 30, "median": 20.0}, "sl_multiplier": {"mean": 6.5, "std": 2.0, "min": 5.0, "max": 10.0, "median": 5.0}, "macd_boll_count_fz": {"mean": 0.21999999999999997, "std": 0.09879271228182775, "min": 0.08, "max": 0.3, "median": 0.3}, "dk_fz": {"mean": 0.9100000000000001, "std": 0.066332495807108, "min": 0.8, "max": 1.0, "median": 0.9}, "zy": {"mean": 100.0, "std": 0.0, "min": 100, "max": 100, "median": 100.0}, "AF": {"mean": 0.00196, "std": 7.999999999999994e-05, "min": 0.0018000000000000002, "max": 0.002, "median": 0.002}, "AF_max": {"mean": 0.124, "std": 0.057131427428342804, "min": 0.04, "max": 0.2, "median": 0.14}, "trailing_start_ratio": {"mean": 0.6400000000000001, "std": 0.14966629547095767, "min": 0.4, "max": 0.8, "median": 0.6000000000000001}, "daily_loss_limit": {"mean": 2000.0, "std": 1000.0, "min": 1000, "max": 3000, "median": 2000.0}, "k_15": {"mean": 15.0, "std": 0.0, "min": 15, "max": 15, "median": 15.0}, "k_30": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "atr_window": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "donchian_period": {"mean": 20.0, "std": 0.0, "min": 20, "max": 20, "median": 20.0}, "lots": {"mean": 1.0, "std": 0.0, "min": 1, "max": 1, "median": 1.0}, "use_trailing_stop": {"mean": 1.0, "std": 0.0, "min": 1, "max": 1, "median": 1.0}, "contract_multiplier": {"mean": 12.0, "std": 9.273618495495704, "min": 5.0, "max": 30.0, "median": 10.0}}, "top_performers": [{"symbol": "rb888.SHFE", "best_performance": 4.557581072271267, "stability_score": 0.951648813884449}, {"symbol": "SH888.CZCE", "best_performance": 3.8311964379639045, "stability_score": 0.9840413815401398}, {"symbol": "bu888.SHFE", "best_performance": 3.7233382744316406, "stability_score": 0.9428342877434216}, {"symbol": "PX888.CZCE", "best_performance": 3.34556224901105, "stability_score": 0.967635817708606}, {"symbol": "TA888.CZCE", "best_performance": 3.117901069717541, "stability_score": 0.9629900716224886}], "most_stable": [{"symbol": "SH888.CZCE", "best_performance": 3.8311964379639045, "stability_score": 0.9840413815401398}, {"symbol": "PX888.CZCE", "best_performance": 3.34556224901105, "stability_score": 0.967635817708606}, {"symbol": "TA888.CZCE", "best_performance": 3.117901069717541, "stability_score": 0.9629900716224886}, {"symbol": "rb888.SHFE", "best_performance": 4.557581072271267, "stability_score": 0.951648813884449}, {"symbol": "bu888.SHFE", "best_performance": 3.7233382744316406, "stability_score": 0.9428342877434216}], "failed_symbols": []}