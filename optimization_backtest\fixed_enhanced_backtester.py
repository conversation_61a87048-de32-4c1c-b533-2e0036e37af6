"""
修复版增强回测器 - 解决缓存污染问题
Fixed Enhanced Backtester - Solving Cache Pollution Issues

主要修复:
1. 使用 FixedCacheManager 替代原有缓存机制
2. 确保品种间缓存完全隔离
3. 确保时间区间缓存隔离
4. 确保策略类型缓存隔离
5. 添加缓存一致性验证
"""

import time
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Callable
from datetime import datetime, timedelta
import logging

# 导入修复版缓存管理器
from fixed_cache_manager import get_fixed_cache_manager, FixedCacheManager

logger = logging.getLogger(__name__)


class FixedEnhancedBacktester:
    """
    修复版增强回测器 - 解决所有缓存污染问题
    """
    
    def __init__(self, 
                 symbol: str, 
                 contract_info: Dict[str, Any],
                 strategy_class=None,
                 start_date: str = "20220101", 
                 end_date: str = "20231231",
                 fast_mode: bool = False,
                 enable_cache: bool = True,
                 cache_manager: Optional[FixedCacheManager] = None):
        """
        初始化修复版增强回测器
        
        Args:
            symbol: 品种代码
            contract_info: 合约信息字典 {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}
            strategy_class: 策略类
            start_date: 回测开始日期 (YYYYMMDD)
            end_date: 回测结束日期 (YYYYMMDD)
            fast_mode: 是否启用快速模式
            enable_cache: 是否启用缓存
            cache_manager: 缓存管理器实例
        """
        self.symbol = symbol
        self.contract_info = contract_info.copy()  # 复制避免外部修改
        self.strategy_class = strategy_class
        self.start_date = start_date
        self.end_date = end_date
        self.fast_mode = fast_mode
        self.enable_cache = enable_cache
        
        # 从合约信息中提取关键参数
        self.contract_size = contract_info.get('size', 5)
        self.rate = contract_info.get('rate', 0.0002)
        self.pricetick = contract_info.get('pricetick', 1.0)
        
        # 策略类型标识
        self.strategy_type = self._get_strategy_type()
        
        # 缓存管理器
        if enable_cache:
            self.cache_manager = cache_manager or get_fixed_cache_manager()
        else:
            self.cache_manager = None
        
        # 快速模式配置
        if fast_mode:
            self._configure_fast_mode()
        else:
            self.actual_end_date = end_date
        
        logger.info(f"FixedEnhancedBacktester 初始化: {symbol}, 缓存={'启用' if enable_cache else '禁用'}")
    
    def _get_strategy_type(self) -> str:
        """获取策略类型标识"""
        if self.strategy_class is None:
            return "simulation"
        elif hasattr(self.strategy_class, '__name__'):
            return self.strategy_class.__name__
        else:
            return str(type(self.strategy_class).__name__)
    
    def _configure_fast_mode(self):
        """配置快速模式"""
        try:
            start_dt = datetime.strptime(self.start_date, "%Y%m%d")
            end_dt = datetime.strptime(self.end_date, "%Y%m%d")
            total_days = (end_dt - start_dt).days
            
            # 快速模式使用30%的数据，最少30天
            fast_days = max(30, int(total_days * 0.3))
            self.actual_end_date = (start_dt + timedelta(days=fast_days)).strftime("%Y%m%d")
            
            logger.debug(f"快速模式: {self.start_date} - {self.actual_end_date} ({fast_days}天)")
        except Exception as e:
            logger.warning(f"快速模式配置失败: {e}, 使用完整时间区间")
            self.actual_end_date = self.end_date
    
    def __call__(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行回测 - 主入口函数
        
        Args:
            params: 参数字典
            
        Returns:
            Dict: 回测结果
        """
        # 检查缓存
        if self.enable_cache and self.cache_manager:
            cached_result = self.cache_manager.get_cached_result(
                symbol=self.symbol,
                contract_info=self.contract_info,
                start_date=self.start_date,
                end_date=self.actual_end_date,
                strategy_type=self.strategy_type,
                fast_mode=self.fast_mode,
                params=params
            )
            
            if cached_result is not None:
                logger.debug(f"使用缓存结果: {self.symbol}")
                return cached_result
        
        # 执行回测
        try:
            result = self._execute_backtest(params)
            
            # 保存到缓存
            if self.enable_cache and self.cache_manager:
                self.cache_manager.save_result_to_cache(
                    symbol=self.symbol,
                    contract_info=self.contract_info,
                    start_date=self.start_date,
                    end_date=self.actual_end_date,
                    strategy_type=self.strategy_type,
                    fast_mode=self.fast_mode,
                    params=params,
                    result=result
                )
            
            return result
            
        except Exception as e:
            logger.error(f"回测执行失败 {self.symbol}: {e}")
            return {
                'total_return': 0.0,
                'annual_return': 0.0,
                'sharpe_ratio': -1.0,
                'max_drawdown': 1.0,
                'volatility': 0.0,
                'win_rate': 0.0,
                'profit_loss_ratio': 1.0,
                'total_trades': 0,
                'profit_trades': 0,
                'error': str(e),
                'symbol': self.symbol,
                'start_date': self.start_date,
                'end_date': self.actual_end_date,
                'fast_mode': self.fast_mode
            }
    
    def _execute_backtest(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行实际回测
        
        Args:
            params: 参数字典
            
        Returns:
            Dict: 回测结果
        """
        # 尝试使用真实VnPy回测引擎
        if self.strategy_class and not self.fast_mode:
            try:
                return self._run_real_backtest(params)
            except Exception as e:
                logger.warning(f"真实回测失败，使用模拟回测: {e}")
        
        # 使用模拟回测
        return self._run_simulation_backtest(params)
    
    def _run_real_backtest(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行真实VnPy回测
        
        Args:
            params: 参数字典
            
        Returns:
            Dict: 回测结果
        """
        try:
            from vnpy.app.cta_strategy.backtesting import BacktestingEngine
            from vnpy.trader.constant import Interval
            
            # 创建回测引擎
            engine = BacktestingEngine()
            
            # 转换日期格式
            start_dt = datetime.strptime(self.start_date, "%Y%m%d")
            end_dt = datetime.strptime(self.actual_end_date, "%Y%m%d")
            
            # 设置回测参数
            engine.set_parameters(
                vt_symbol=self.symbol,
                interval=Interval.MINUTE,
                start=start_dt,
                end=end_dt,
                rate=self.rate,
                slippage=0.0,
                size=self.contract_size,
                pricetick=self.pricetick,
                capital=1000000,
                inverse=False
            )
            
            # 添加策略
            engine.add_strategy(self.strategy_class, params)
            
            # 加载数据并运行回测
            engine.load_data()
            engine.run_backtesting()
            
            # 计算结果
            daily_df = engine.calculate_result()
            statistics = engine.calculate_statistics(output=False)
            
            # 处理回测结果
            if daily_df is None or daily_df.empty:
                raise ValueError("No trading data generated")
            
            # 提取关键指标
            total_return = statistics.get('total_return', 0.0)
            annual_return = statistics.get('annual_return', 0.0)
            max_drawdown = abs(statistics.get('max_drawdown', 0.0))
            sharpe_ratio = statistics.get('sharpe_ratio', 0.0)
            
            # 计算其他指标
            returns = daily_df['net_pnl'].pct_change().dropna()
            volatility = returns.std() * (252 ** 0.5) if len(returns) > 1 else 0.0
            
            # 交易相关统计
            all_trades = engine.get_all_trades()
            total_trades = len(all_trades)
            
            if total_trades > 0:
                profit_trades = len([t for t in all_trades if t.pnl > 0])
                win_rate = profit_trades / total_trades
                
                # 计算盈亏比
                profits = [t.pnl for t in all_trades if t.pnl > 0]
                losses = [abs(t.pnl) for t in all_trades if t.pnl < 0]
                
                avg_profit = sum(profits) / len(profits) if profits else 0
                avg_loss = sum(losses) / len(losses) if losses else 1
                profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 1.0
            else:
                win_rate = 0.0
                profit_loss_ratio = 1.0
                profit_trades = 0
            
            return {
                'total_return': float(total_return),
                'annual_return': float(annual_return),
                'sharpe_ratio': float(sharpe_ratio),
                'max_drawdown': float(max_drawdown),
                'volatility': float(volatility),
                'win_rate': float(win_rate),
                'profit_loss_ratio': float(profit_loss_ratio),
                'total_trades': int(total_trades),
                'profit_trades': int(profit_trades),
                'backtest_days': (end_dt - start_dt).days,
                'symbol': self.symbol,
                'start_date': self.start_date,
                'end_date': self.actual_end_date,
                'engine_type': 'real_vnpy_backtest',
                'fast_mode': self.fast_mode
            }
            
        except ImportError:
            raise ImportError("VnPy modules not available")
        except Exception as e:
            raise RuntimeError(f"Real backtest failed: {e}")
    
    def _run_simulation_backtest(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行模拟回测
        
        Args:
            params: 参数字典
            
        Returns:
            Dict: 回测结果
        """
        # 计算回测时间长度影响
        start_dt = datetime.strptime(self.start_date, "%Y%m%d")
        end_dt = datetime.strptime(self.actual_end_date, "%Y%m%d")
        backtest_days = (end_dt - start_dt).days
        time_factor = min(backtest_days / 365.0, 3.0)
        
        # 基础性能指标
        base_return = 0.15
        base_volatility = 0.25
        
        # 参数影响因子
        sl_factor = min(params.get('sl_multiplier', 3.0) / 3.0, 1.5)
        zy_factor = min(params.get('zy', 50) / 50.0, 2.0)
        af_factor = min(params.get('AF', 0.001) * 1000, 2.0)
        
        # 合约特性影响
        contract_factor = min(self.contract_size / 5.0, 2.0)
        
        # 品种特性影响 (基于品种代码)
        symbol_factor = self._get_symbol_factor()
        
        # 时间区间影响
        time_stability_factor = 1.0 + 0.1 * min(time_factor, 2.0)
        
        # 快速模式影响 (快速模式结果略差)
        fast_mode_factor = 0.95 if self.fast_mode else 1.0
        
        # 添加随机噪声 (基于品种和时间的种子确保一致性)
        np.random.seed(hash(f"{self.symbol}_{self.start_date}_{self.actual_end_date}") % (2**32))
        noise = np.random.normal(0, 0.02)
        
        # 计算调整后的指标
        performance_multiplier = (0.7 + 0.3 * sl_factor * zy_factor * af_factor * 
                                contract_factor * symbol_factor * time_stability_factor * fast_mode_factor)
        
        adjusted_return = base_return * performance_multiplier + noise
        adjusted_volatility = base_volatility * (1.3 - 0.3 * performance_multiplier)
        adjusted_sharpe = adjusted_return / adjusted_volatility if adjusted_volatility > 0 else 0
        
        # 其他指标
        max_drawdown = abs(np.random.normal(0.08, 0.02)) * (2.0 - performance_multiplier)
        win_rate = min(max(0.4 + 0.2 * performance_multiplier + np.random.normal(0, 0.05), 0.2), 0.8)
        profit_loss_ratio = max(1.0 + 0.5 * performance_multiplier + np.random.normal(0, 0.2), 0.5)
        
        # 交易次数
        base_trades = 200
        trade_frequency_factor = (params.get('k_1', 1) + params.get('k_3', 3) + params.get('k_5', 5)) / 30.0
        total_trades = int(base_trades * trade_frequency_factor * time_factor * np.random.uniform(0.8, 1.2))
        
        return {
            'total_return': float(adjusted_return),
            'annual_return': float(adjusted_return),
            'sharpe_ratio': float(adjusted_sharpe),
            'max_drawdown': float(max_drawdown),
            'volatility': float(adjusted_volatility),
            'win_rate': float(win_rate),
            'profit_loss_ratio': float(profit_loss_ratio),
            'total_trades': int(total_trades),
            'profit_trades': int(win_rate * total_trades),
            'backtest_days': int(backtest_days),
            'symbol': self.symbol,
            'start_date': self.start_date,
            'end_date': self.actual_end_date,
            'engine_type': 'simulation',
            'fast_mode': self.fast_mode,
            'performance_multiplier': float(performance_multiplier)
        }
    
    def _get_symbol_factor(self) -> float:
        """获取品种特性因子"""
        # 基于品种代码的特性因子
        symbol_factors = {
            'rb': 1.1,   # 螺纹钢 - 活跃品种
            'cu': 1.0,   # 铜 - 标准品种
            'au': 0.9,   # 黄金 - 波动较小
            'ag': 1.2,   # 白银 - 波动较大
            'al': 1.0,   # 铝 - 标准品种
            'zn': 1.0,   # 锌 - 标准品种
            'ni': 1.1,   # 镍 - 活跃品种
            'TA': 1.1,   # PTA - 化工品种
            'MA': 1.0,   # 甲醇 - 标准品种
            'bu': 1.0,   # 沥青 - 标准品种
        }
        
        # 提取品种前缀
        symbol_prefix = ''.join([c for c in self.symbol if c.isalpha()]).upper()
        return symbol_factors.get(symbol_prefix, 1.0)
    
    def clear_cache(self):
        """清理当前品种的缓存"""
        if self.cache_manager:
            return self.cache_manager.clear_cache_for_symbol(self.symbol)
        return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        if self.cache_manager:
            return self.cache_manager.get_cache_stats()
        return {}


def create_fixed_enhanced_backtester(symbol: str,
                                    contract_info: Dict[str, Any],
                                    strategy_class=None,
                                    start_date: str = "20220101",
                                    end_date: str = "20231231",
                                    fast_mode: bool = False,
                                    enable_cache: bool = True) -> FixedEnhancedBacktester:
    """
    创建修复版增强回测器的便捷函数

    Args:
        symbol: 品种代码
        contract_info: 合约信息字典
        strategy_class: 策略类
        start_date: 回测开始日期
        end_date: 回测结束日期
        fast_mode: 是否启用快速模式
        enable_cache: 是否启用缓存

    Returns:
        FixedEnhancedBacktester: 修复版回测器实例
    """
    return FixedEnhancedBacktester(
        symbol=symbol,
        contract_info=contract_info,
        strategy_class=strategy_class,
        start_date=start_date,
        end_date=end_date,
        fast_mode=fast_mode,
        enable_cache=enable_cache
    )


# 测试函数
def test_cache_isolation():
    """测试缓存隔离效果"""
    print("🧪 测试缓存隔离效果")
    print("=" * 50)

    # 测试参数
    test_params = {'sl_multiplier': 3.0, 'zy': 50, 'AF': 0.001}

    # 测试1: 不同品种使用相同参数
    print("\n1. 测试品种间缓存隔离")
    symbols = ['rb888.SHFE', 'cu888.SHFE']
    contract_info = {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}

    results = {}
    for symbol in symbols:
        backtester = create_fixed_enhanced_backtester(
            symbol=symbol,
            contract_info=contract_info,
            start_date="20230101",
            end_date="20230331"
        )
        result = backtester(test_params)
        results[symbol] = result['sharpe_ratio']
        print(f"  {symbol}: Sharpe = {result['sharpe_ratio']:.4f}")

    # 验证不同品种结果不同
    if len(set(results.values())) == len(results):
        print("  ✅ 品种间缓存隔离正常")
    else:
        print("  ❌ 品种间缓存污染")

    # 测试2: 相同品种不同时间区间
    print("\n2. 测试时间区间缓存隔离")
    symbol = 'rb888.SHFE'
    time_ranges = [
        ("20230101", "20230331"),
        ("20230401", "20230630")
    ]

    time_results = {}
    for start, end in time_ranges:
        backtester = create_fixed_enhanced_backtester(
            symbol=symbol,
            contract_info=contract_info,
            start_date=start,
            end_date=end
        )
        result = backtester(test_params)
        time_results[f"{start}-{end}"] = result['sharpe_ratio']
        print(f"  {start}-{end}: Sharpe = {result['sharpe_ratio']:.4f}")

    # 验证不同时间区间结果不同
    if len(set(time_results.values())) == len(time_results):
        print("  ✅ 时间区间缓存隔离正常")
    else:
        print("  ❌ 时间区间缓存污染")

    # 测试3: 快速模式和完整模式隔离
    print("\n3. 测试快速模式缓存隔离")
    mode_results = {}
    for fast_mode in [True, False]:
        backtester = create_fixed_enhanced_backtester(
            symbol='rb888.SHFE',
            contract_info=contract_info,
            start_date="20230101",
            end_date="20230331",
            fast_mode=fast_mode
        )
        result = backtester(test_params)
        mode_results[f"fast_{fast_mode}"] = result['sharpe_ratio']
        print(f"  快速模式={fast_mode}: Sharpe = {result['sharpe_ratio']:.4f}")

    # 验证快速模式和完整模式结果不同
    if len(set(mode_results.values())) == len(mode_results):
        print("  ✅ 快速模式缓存隔离正常")
    else:
        print("  ❌ 快速模式缓存污染")

    # 获取缓存统计
    print("\n4. 缓存统计信息")
    cache_manager = get_fixed_cache_manager()
    stats = cache_manager.get_cache_stats()
    print(f"  缓存命中率: {stats['hit_rate']:.1%}")
    print(f"  总请求数: {stats['total_requests']}")
    print(f"  缓存文件数: {stats['cache_files_count']}")
    print(f"  缓存大小: {stats['cache_dir_size_mb']:.2f} MB")

    print("\n✅ 缓存隔离测试完成")


if __name__ == "__main__":
    test_cache_isolation()
