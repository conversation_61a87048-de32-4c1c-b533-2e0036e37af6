"""
策略加载器
Strategy Loader

自动加载vnpy的CTA策略，并适配到智能交易系统中
"""

import sys
import os
import importlib
import inspect
from typing import Dict, List, Type, Any
import logging

# 添加vnpy路径
vnpy_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "vnpy")
if vnpy_path not in sys.path:
    sys.path.append(vnpy_path)

logger = logging.getLogger(__name__)

class StrategyLoader:
    """策略加载器"""
    
    def __init__(self):
        self.loaded_strategies = {}
        self.strategy_classes = {}
        self.vnpy_available = self._check_vnpy_availability()
        
    def _check_vnpy_availability(self) -> bool:
        """检查vnpy是否可用"""
        try:
            from vnpy.app.cta_strategy import CtaTemplate
            return True
        except ImportError:
            logger.warning("vnpy不可用，将使用内置策略")
            return False
    
    def load_vnpy_strategies(self) -> Dict[str, Type]:
        """加载vnpy策略"""
        if not self.vnpy_available:
            return {}
        
        strategies = {}
        strategy_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                   "vnpy", "app", "cta_strategy", "strategies")
        
        if not os.path.exists(strategy_dir):
            logger.warning(f"策略目录不存在: {strategy_dir}")
            return {}
        
        # 获取所有策略文件
        strategy_files = [f for f in os.listdir(strategy_dir) 
                         if f.endswith('.py') and not f.startswith('__')]
        
        logger.info(f"发现 {len(strategy_files)} 个策略文件")
        
        for strategy_file in strategy_files:
            try:
                module_name = strategy_file[:-3]  # 去掉.py后缀
                
                # 跳过一些特殊文件
                if module_name in ['__init__', 'chan_model']:
                    continue
                
                # 动态导入模块
                spec = importlib.util.spec_from_file_location(
                    module_name, 
                    os.path.join(strategy_dir, strategy_file)
                )
                
                if spec and spec.loader:
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    
                    # 查找策略类
                    strategy_classes = self._find_strategy_classes(module)
                    
                    for class_name, strategy_class in strategy_classes.items():
                        strategies[class_name] = strategy_class
                        logger.debug(f"加载策略: {class_name} from {strategy_file}")
                        
            except Exception as e:
                logger.warning(f"加载策略文件 {strategy_file} 失败: {e}")
                continue
        
        logger.info(f"成功加载 {len(strategies)} 个vnpy策略")
        self.strategy_classes.update(strategies)
        return strategies
    
    def _find_strategy_classes(self, module) -> Dict[str, Type]:
        """在模块中查找策略类"""
        strategy_classes = {}
        
        try:
            from vnpy.app.cta_strategy import CtaTemplate
            
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, CtaTemplate) and 
                    obj != CtaTemplate and
                    not name.startswith('_')):
                    strategy_classes[name] = obj
                    
        except ImportError:
            # 如果vnpy不可用，尝试通过类名识别策略
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    ('Strategy' in name or 'strategy' in name.lower()) and
                    not name.startswith('_')):
                    strategy_classes[name] = obj
        
        return strategy_classes
    
    def get_strategy_list(self) -> List[Dict[str, Any]]:
        """获取策略列表"""
        strategy_list = []
        
        # 添加内置策略
        strategy_list.append({
            'name': 'AdaptiveTrendStrategy',
            'display_name': '自适应趋势策略',
            'description': '基于移动平均线的自适应趋势跟踪策略',
            'type': 'builtin',
            'parameters': {
                'ma_period': {'type': 'int', 'default': 15, 'range': [5, 30]},
                'stop_atr_mult': {'type': 'float', 'default': 2.5, 'range': [1.5, 3.0]},
                'profit_atr_mult': {'type': 'float', 'default': 3.0, 'range': [2.0, 5.0]}
            }
        })
        
        # 添加vnpy策略
        for name, strategy_class in self.strategy_classes.items():
            try:
                # 获取策略参数
                parameters = self._extract_strategy_parameters(strategy_class)
                
                strategy_info = {
                    'name': name,
                    'display_name': getattr(strategy_class, 'class_name', name),
                    'description': getattr(strategy_class, '__doc__', '').strip() or f'vnpy策略: {name}',
                    'type': 'vnpy',
                    'parameters': parameters,
                    'class': strategy_class
                }
                
                strategy_list.append(strategy_info)
                
            except Exception as e:
                logger.warning(f"解析策略 {name} 信息失败: {e}")
        
        return strategy_list
    
    def _extract_strategy_parameters(self, strategy_class) -> Dict[str, Any]:
        """提取策略参数"""
        parameters = {}
        
        try:
            # 获取策略的参数定义
            if hasattr(strategy_class, 'parameters'):
                for param_name in strategy_class.parameters:
                    param_value = getattr(strategy_class, param_name, None)
                    
                    if param_value is not None:
                        param_type = type(param_value).__name__
                        
                        parameters[param_name] = {
                            'type': param_type,
                            'default': param_value,
                            'description': f'{param_name} 参数'
                        }
            
            # 如果没有parameters属性，尝试从类属性中提取
            elif hasattr(strategy_class, '__init__'):
                init_signature = inspect.signature(strategy_class.__init__)
                for param_name, param in init_signature.parameters.items():
                    if param_name not in ['self', 'cta_engine', 'strategy_name', 'vt_symbol', 'setting']:
                        param_type = 'float' if param.default and isinstance(param.default, (int, float)) else 'str'
                        
                        parameters[param_name] = {
                            'type': param_type,
                            'default': param.default if param.default != inspect.Parameter.empty else 0,
                            'description': f'{param_name} 参数'
                        }
        
        except Exception as e:
            logger.warning(f"提取策略参数失败: {e}")
        
        return parameters
    
    def create_strategy_adapter(self, strategy_name: str, parameters: Dict = None):
        """创建策略适配器"""
        if strategy_name == 'AdaptiveTrendStrategy':
            from strategies.adaptive_trend_strategy import AdaptiveTrendStrategy
            return AdaptiveTrendStrategy(parameters)
        
        elif strategy_name in self.strategy_classes:
            # 创建vnpy策略适配器
            return VnpyStrategyAdapter(self.strategy_classes[strategy_name], parameters)
        
        else:
            raise ValueError(f"未知策略: {strategy_name}")
    
    def get_strategy_info(self, strategy_name: str) -> Dict[str, Any]:
        """获取策略详细信息"""
        strategy_list = self.get_strategy_list()
        
        for strategy in strategy_list:
            if strategy['name'] == strategy_name:
                return strategy
        
        return {}


class VnpyStrategyAdapter:
    """vnpy策略适配器"""
    
    def __init__(self, strategy_class: Type, parameters: Dict = None):
        """
        初始化vnpy策略适配器
        
        Args:
            strategy_class: vnpy策略类
            parameters: 策略参数
        """
        self.strategy_class = strategy_class
        self.parameters = parameters or {}
        self.strategy_name = strategy_class.__name__
        
        # 模拟vnpy环境
        self.positions = {}
        self.indicators = {}
        self.signals_history = []
        
        logger.info(f"创建vnpy策略适配器: {self.strategy_name}")
    
    def calculate_indicators(self, data, symbol: str) -> Dict:
        """计算技术指标（适配vnpy策略）"""
        # 这里需要根据具体的vnpy策略来适配
        # 由于vnpy策略结构复杂，这里提供基础适配
        
        indicators = {}
        
        try:
            # 基础技术指标计算
            if len(data) >= 20:
                indicators['ma20'] = data['close'].rolling(20).mean()
                indicators['ma5'] = data['close'].rolling(5).mean()
                
                # ATR计算
                high_low = data['high'] - data['low']
                high_close = abs(data['high'] - data['close'].shift())
                low_close = abs(data['low'] - data['close'].shift())
                true_range = high_low.combine(high_close, max).combine(low_close, max)
                indicators['atr'] = true_range.rolling(14).mean()
            
            self.indicators[symbol] = indicators
            
        except Exception as e:
            logger.warning(f"计算 {symbol} 指标失败: {e}")
        
        return indicators
    
    def generate_signals(self, daily_data: Dict, current_date) -> Dict[str, int]:
        """生成交易信号（适配vnpy策略）"""
        signals = {}
        
        try:
            # 简化的信号生成逻辑
            # 实际使用时需要根据具体的vnpy策略来适配
            
            for symbol, current_bar in daily_data.items():
                if symbol in self.indicators:
                    indicators = self.indicators[symbol]
                    
                    # 简单的均线策略信号
                    if 'ma5' in indicators and 'ma20' in indicators:
                        ma5_current = indicators['ma5'].iloc[-1] if len(indicators['ma5']) > 0 else 0
                        ma20_current = indicators['ma20'].iloc[-1] if len(indicators['ma20']) > 0 else 0
                        
                        if ma5_current > ma20_current:
                            signals[symbol] = 1  # 买入信号
                        elif ma5_current < ma20_current:
                            signals[symbol] = -1  # 卖出信号
                        else:
                            signals[symbol] = 0  # 无信号
            
            # 记录信号历史
            self.signals_history.append({
                'date': current_date,
                'signals': signals.copy()
            })
            
        except Exception as e:
            logger.warning(f"生成信号失败: {e}")
        
        return signals
    
    def get_strategy_status(self) -> Dict:
        """获取策略状态"""
        return {
            'strategy_name': self.strategy_name,
            'strategy_type': 'vnpy_adapter',
            'parameters': self.parameters,
            'current_positions': len(self.positions),
            'recent_signals': self.signals_history[-5:] if self.signals_history else []
        }
    
    def update_parameters(self, new_params: Dict):
        """更新策略参数"""
        self.parameters.update(new_params)
        logger.info(f"更新 {self.strategy_name} 参数: {new_params}")


# 创建全局策略加载器实例
strategy_loader = StrategyLoader()

def get_strategy_loader() -> StrategyLoader:
    """获取策略加载器实例"""
    return strategy_loader

def load_all_strategies():
    """加载所有策略"""
    loader = get_strategy_loader()
    vnpy_strategies = loader.load_vnpy_strategies()
    
    logger.info(f"策略加载完成:")
    logger.info(f"  - vnpy策略: {len(vnpy_strategies)} 个")
    logger.info(f"  - 内置策略: 1 个")
    
    return loader.get_strategy_list()

if __name__ == "__main__":
    # 测试策略加载
    strategies = load_all_strategies()
    
    print(f"加载的策略列表:")
    for strategy in strategies:
        print(f"  - {strategy['name']}: {strategy['display_name']}")
        print(f"    类型: {strategy['type']}")
        print(f"    参数数量: {len(strategy['parameters'])}")
        print()
