"""
合约信息管理器
Contract Information Manager

集成vnpy的合约信息，提供：
1. 合约基础信息（乘数、最小变动价位等）
2. 手续费和保证金信息
3. 交易所映射关系
4. 主力合约识别
"""

import sys
import os
import pandas as pd
from typing import Dict, List, Optional
import logging

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

try:
    from contract_info import ContractInfo
    CONTRACT_INFO_AVAILABLE = True
except ImportError:
    CONTRACT_INFO_AVAILABLE = False
    print("警告: contract_info.py 未找到，将使用默认合约信息")

logger = logging.getLogger(__name__)

class ContractManager:
    """合约信息管理器"""
    
    def __init__(self, excel_path: str = None):
        """
        初始化合约管理器
        
        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path or os.path.join(project_root, "optimization_backtest", "期货全品种手续费保证金.xls")
        
        # 初始化合约信息
        if CONTRACT_INFO_AVAILABLE and os.path.exists(self.excel_path):
            try:
                self.contract_info = ContractInfo(self.excel_path)
                self.contract_params = self.contract_info.get_contract_params()
                self.available_contracts = self.contract_info.get_888_contracts()
                logger.info(f"成功加载合约信息，共 {len(self.available_contracts)} 个品种")
            except Exception as e:
                logger.warning(f"加载合约信息失败: {e}，使用默认信息")
                self._init_default_contracts()
        else:
            logger.info("使用默认合约信息")
            self._init_default_contracts()
    
    def _init_default_contracts(self):
        """初始化默认合约信息"""
        self.available_contracts = [
            'rb888.SHFE', 'hc888.SHFE', 'i888.DCE', 'j888.DCE', 'jm888.DCE',
            'cu888.SHFE', 'al888.SHFE', 'zn888.SHFE', 'ni888.SHFE',
            'au888.SHFE', 'ag888.SHFE', 'sc888.INE', 'bu888.SHFE',
            'TA888.CZCE', 'MA888.CZCE', 'PX888.CZCE', 'SH888.CZCE',
            'm888.DCE', 'y888.DCE', 'p888.DCE', 'c888.DCE', 'cs888.DCE', 'a888.DCE'
        ]
        
        # 默认合约参数
        self.contract_params = {
            'rate': {},
            'size': {},
            'pricetick': {}
        }
        
        # 设置默认参数
        default_contracts = {
            'rb888.SHFE': {'size': 10, 'pricetick': 1, 'rate': 0.0001, 'name': '螺纹钢'},
            'hc888.SHFE': {'size': 10, 'pricetick': 1, 'rate': 0.0001, 'name': '热卷'},
            'i888.DCE': {'size': 100, 'pricetick': 0.5, 'rate': 0.0001, 'name': '铁矿石'},
            'j888.DCE': {'size': 100, 'pricetick': 0.5, 'rate': 0.0001, 'name': '焦炭'},
            'jm888.DCE': {'size': 60, 'pricetick': 0.5, 'rate': 0.0001, 'name': '焦煤'},
            'cu888.SHFE': {'size': 5, 'pricetick': 10, 'rate': 0.0001, 'name': '沪铜'},
            'al888.SHFE': {'size': 5, 'pricetick': 5, 'rate': 0.0001, 'name': '沪铝'},
            'zn888.SHFE': {'size': 5, 'pricetick': 5, 'rate': 0.0001, 'name': '沪锌'},
            'ni888.SHFE': {'size': 1, 'pricetick': 10, 'rate': 0.0001, 'name': '沪镍'},
            'au888.SHFE': {'size': 1000, 'pricetick': 0.02, 'rate': 0.0001, 'name': '黄金'},
            'ag888.SHFE': {'size': 15, 'pricetick': 1, 'rate': 0.0001, 'name': '白银'},
            'sc888.INE': {'size': 1000, 'pricetick': 0.1, 'rate': 0.0001, 'name': '原油'},
            'bu888.SHFE': {'size': 10, 'pricetick': 2, 'rate': 0.0001, 'name': '沥青'},
            'TA888.CZCE': {'size': 5, 'pricetick': 2, 'rate': 0.0001, 'name': 'PTA'},
            'MA888.CZCE': {'size': 10, 'pricetick': 1, 'rate': 0.0001, 'name': '甲醇'},
            'PX888.CZCE': {'size': 5, 'pricetick': 2, 'rate': 0.0001, 'name': 'PX'},
            'SH888.CZCE': {'size': 5, 'pricetick': 2, 'rate': 0.0001, 'name': '纯碱'},
            'm888.DCE': {'size': 10, 'pricetick': 1, 'rate': 0.0001, 'name': '豆粕'},
            'y888.DCE': {'size': 10, 'pricetick': 2, 'rate': 0.0001, 'name': '豆油'},
            'p888.DCE': {'size': 10, 'pricetick': 2, 'rate': 0.0001, 'name': '棕榈油'},
            'c888.DCE': {'size': 10, 'pricetick': 1, 'rate': 0.0001, 'name': '玉米'},
            'cs888.DCE': {'size': 10, 'pricetick': 1, 'rate': 0.0001, 'name': '玉米淀粉'},
            'a888.DCE': {'size': 10, 'pricetick': 1, 'rate': 0.0001, 'name': '豆一'}
        }
        
        for symbol, info in default_contracts.items():
            self.contract_params['size'][symbol] = info['size']
            self.contract_params['pricetick'][symbol] = info['pricetick']
            self.contract_params['rate'][symbol] = info['rate']
    
    def get_contract_info(self, symbol: str) -> Dict:
        """
        获取合约信息
        
        Args:
            symbol: 合约代码
            
        Returns:
            合约信息字典
        """
        info = {
            'symbol': symbol,
            'size': self.contract_params['size'].get(symbol, 1),
            'pricetick': self.contract_params['pricetick'].get(symbol, 1),
            'rate': self.contract_params['rate'].get(symbol, 0.0002),
            'name': self._get_contract_name(symbol)
        }
        
        return info
    
    def _get_contract_name(self, symbol: str) -> str:
        """获取合约中文名称"""
        name_mapping = {
            'rb888.SHFE': '螺纹钢', 'hc888.SHFE': '热卷', 'i888.DCE': '铁矿石',
            'j888.DCE': '焦炭', 'jm888.DCE': '焦煤', 'cu888.SHFE': '沪铜',
            'al888.SHFE': '沪铝', 'zn888.SHFE': '沪锌', 'ni888.SHFE': '沪镍',
            'au888.SHFE': '黄金', 'ag888.SHFE': '白银', 'sc888.INE': '原油',
            'bu888.SHFE': '沥青', 'TA888.CZCE': 'PTA', 'MA888.CZCE': '甲醇',
            'PX888.CZCE': 'PX', 'SH888.CZCE': '纯碱', 'm888.DCE': '豆粕',
            'y888.DCE': '豆油', 'p888.DCE': '棕榈油', 'c888.DCE': '玉米',
            'cs888.DCE': '玉米淀粉', 'a888.DCE': '豆一'
        }
        
        return name_mapping.get(symbol, symbol.split('.')[0])
    
    def get_all_contracts(self) -> List[str]:
        """获取所有可用合约列表"""
        return self.available_contracts.copy()
    
    def get_contracts_by_exchange(self, exchange: str) -> List[str]:
        """
        按交易所获取合约列表
        
        Args:
            exchange: 交易所代码 (SHFE, DCE, CZCE, INE)
            
        Returns:
            合约列表
        """
        return [symbol for symbol in self.available_contracts if symbol.endswith(f'.{exchange}')]
    
    def get_contract_multiplier(self, symbol: str) -> int:
        """获取合约乘数"""
        return self.contract_params['size'].get(symbol, 1)
    
    def get_contract_pricetick(self, symbol: str) -> float:
        """获取最小变动价位"""
        return self.contract_params['pricetick'].get(symbol, 1)
    
    def get_contract_commission_rate(self, symbol: str) -> float:
        """获取手续费率"""
        return self.contract_params['rate'].get(symbol, 0.0002)
    
    def calculate_contract_value(self, symbol: str, price: float, lots: int = 1) -> float:
        """
        计算合约价值
        
        Args:
            symbol: 合约代码
            price: 价格
            lots: 手数
            
        Returns:
            合约价值
        """
        multiplier = self.get_contract_multiplier(symbol)
        return price * multiplier * lots
    
    def calculate_commission(self, symbol: str, price: float, lots: int = 1) -> float:
        """
        计算手续费
        
        Args:
            symbol: 合约代码
            price: 价格
            lots: 手数
            
        Returns:
            手续费金额
        """
        contract_value = self.calculate_contract_value(symbol, price, lots)
        commission_rate = self.get_contract_commission_rate(symbol)
        return contract_value * commission_rate
    
    def get_risk_per_lot(self, symbol: str, atr: float, stop_multiplier: float = 2.5) -> float:
        """
        计算单手风险金额
        
        Args:
            symbol: 合约代码
            atr: ATR值
            stop_multiplier: 止损倍数
            
        Returns:
            单手风险金额
        """
        multiplier = self.get_contract_multiplier(symbol)
        return atr * multiplier * stop_multiplier
    
    def filter_liquid_contracts(self, min_multiplier: int = 5) -> List[str]:
        """
        筛选流动性较好的合约
        
        Args:
            min_multiplier: 最小合约乘数
            
        Returns:
            筛选后的合约列表
        """
        liquid_contracts = []
        
        for symbol in self.available_contracts:
            multiplier = self.get_contract_multiplier(symbol)
            if multiplier >= min_multiplier:
                liquid_contracts.append(symbol)
        
        return liquid_contracts
    
    def get_contract_summary(self) -> pd.DataFrame:
        """
        获取合约信息汇总表
        
        Returns:
            包含所有合约信息的DataFrame
        """
        data = []
        
        for symbol in self.available_contracts:
            info = self.get_contract_info(symbol)
            data.append({
                '合约代码': symbol,
                '品种名称': info['name'],
                '交易所': symbol.split('.')[1],
                '合约乘数': info['size'],
                '最小变动价位': info['pricetick'],
                '手续费率': f"{info['rate']:.4f}",
                '手续费率(万分之)': f"{info['rate'] * 10000:.2f}"
            })
        
        return pd.DataFrame(data)
    
    def export_contract_info(self, filename: str = "contract_summary.xlsx"):
        """
        导出合约信息到Excel
        
        Args:
            filename: 文件名
        """
        try:
            df = self.get_contract_summary()
            df.to_excel(filename, index=False)
            logger.info(f"合约信息已导出到: {filename}")
        except Exception as e:
            logger.error(f"导出合约信息失败: {e}")
    
    def validate_symbol(self, symbol: str) -> bool:
        """
        验证合约代码是否有效
        
        Args:
            symbol: 合约代码
            
        Returns:
            是否有效
        """
        return symbol in self.available_contracts
    
    def get_similar_contracts(self, symbol: str) -> List[str]:
        """
        获取相似品种（同一板块）
        
        Args:
            symbol: 合约代码
            
        Returns:
            相似合约列表
        """
        # 定义品种板块
        sectors = {
            'black': ['rb888.SHFE', 'hc888.SHFE', 'i888.DCE', 'j888.DCE', 'jm888.DCE'],
            'metal': ['cu888.SHFE', 'al888.SHFE', 'zn888.SHFE', 'ni888.SHFE'],
            'precious': ['au888.SHFE', 'ag888.SHFE'],
            'energy': ['sc888.INE', 'bu888.SHFE'],
            'chemical': ['TA888.CZCE', 'MA888.CZCE', 'PX888.CZCE', 'SH888.CZCE'],
            'agriculture': ['m888.DCE', 'y888.DCE', 'p888.DCE', 'c888.DCE', 'cs888.DCE', 'a888.DCE']
        }
        
        for sector, contracts in sectors.items():
            if symbol in contracts:
                return [s for s in contracts if s != symbol]
        
        return []


# 创建全局合约管理器实例
contract_manager = ContractManager()

def get_contract_manager() -> ContractManager:
    """获取合约管理器实例"""
    return contract_manager
