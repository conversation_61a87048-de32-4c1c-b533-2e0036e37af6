"""
品种动态筛选引擎
Instrument Dynamic Selection Engine

评估维度：
1. 动量强度 - 20日标准化动量
2. 波动适配 - 10日ATR/合约乘数
3. 策略相关性 - 品种间收益率相关系数
4. 流动性 - 主力合约日均成交量
5. 策略胜率 - 滚动60日策略胜率
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class InstrumentMetrics:
    """品种评估指标"""
    symbol: str
    momentum_score: float
    volatility_risk: float  # 单手风险金额
    strategy_winrate: float
    liquidity_score: float
    correlation_risk: float
    overall_score: float
    is_selected: bool = False

@dataclass
class SelectionCriteria:
    """筛选标准"""
    momentum_threshold: float = 0.7  # 动量分位数阈值
    max_risk_per_lot: float = 10000  # 单手最大风险金额
    min_winrate: float = 0.45  # 最低胜率
    min_volume: int = 10000  # 最低日均成交量
    max_correlation: float = 0.7  # 最大相关系数
    max_instruments: int = 5  # 最大选择品种数

class InstrumentSelector:
    """品种动态筛选引擎"""
    
    def __init__(self, account_value: float = 1000000):
        """
        初始化品种筛选引擎
        
        Args:
            account_value: 账户净值
        """
        self.account_value = account_value
        self.criteria = SelectionCriteria()
        self.contract_info = self._load_contract_info()
        self.selected_instruments = []
        self.metrics_history = []
        
    def _load_contract_info(self) -> Dict:
        """加载合约信息"""
        # 主要期货品种合约信息
        return {
            'rb888.SHFE': {'multiplier': 10, 'tick_size': 1, 'name': '螺纹钢'},
            'hc888.SHFE': {'multiplier': 10, 'tick_size': 1, 'name': '热卷'},
            'i888.DCE': {'multiplier': 100, 'tick_size': 0.5, 'name': '铁矿石'},
            'j888.DCE': {'multiplier': 100, 'tick_size': 0.5, 'name': '焦炭'},
            'jm888.DCE': {'multiplier': 60, 'tick_size': 0.5, 'name': '焦煤'},
            'cu888.SHFE': {'multiplier': 5, 'tick_size': 10, 'name': '沪铜'},
            'al888.SHFE': {'multiplier': 5, 'tick_size': 5, 'name': '沪铝'},
            'zn888.SHFE': {'multiplier': 5, 'tick_size': 5, 'name': '沪锌'},
            'ni888.SHFE': {'multiplier': 1, 'tick_size': 10, 'name': '沪镍'},
            'au888.SHFE': {'multiplier': 1000, 'tick_size': 0.02, 'name': '黄金'},
            'ag888.SHFE': {'multiplier': 15, 'tick_size': 1, 'name': '白银'},
            'sc888.INE': {'multiplier': 1000, 'tick_size': 0.1, 'name': '原油'},
            'bu888.SHFE': {'multiplier': 10, 'tick_size': 2, 'name': '沥青'},
            'br888.SHFE': {'multiplier': 5, 'tick_size': 1, 'name': '20号胶'},
            'lu888.INE': {'multiplier': 10, 'tick_size': 1, 'name': '低硫燃料油'},
            'TA888.CZCE': {'multiplier': 5, 'tick_size': 2, 'name': 'PTA'},
            'MA888.CZCE': {'multiplier': 10, 'tick_size': 1, 'name': '甲醇'},
            'PX888.CZCE': {'multiplier': 5, 'tick_size': 2, 'name': 'PX'},
            'SH888.CZCE': {'multiplier': 5, 'tick_size': 2, 'name': '纯碱'},
            'm888.DCE': {'multiplier': 10, 'tick_size': 1, 'name': '豆粕'},
            'y888.DCE': {'multiplier': 10, 'tick_size': 2, 'name': '豆油'},
            'p888.DCE': {'multiplier': 10, 'tick_size': 2, 'name': '棕榈油'},
            'c888.DCE': {'multiplier': 10, 'tick_size': 1, 'name': '玉米'},
            'cs888.DCE': {'multiplier': 10, 'tick_size': 1, 'name': '玉米淀粉'},
            'a888.DCE': {'multiplier': 10, 'tick_size': 1, 'name': '豆一'},
        }
    
    def calculate_momentum_score(self, data: pd.DataFrame) -> float:
        """
        计算动量强度评分
        
        Args:
            data: 包含OHLCV的价格数据
            
        Returns:
            标准化动量评分
        """
        if len(data) < 20:
            return 0.0
            
        # 计算20日标准化动量
        current_price = data['close'].iloc[-1]
        price_20d_ago = data['close'].iloc[-20]
        atr_20 = self._calculate_atr(data, 20)
        
        if atr_20 == 0:
            return 0.0
            
        momentum = (current_price - price_20d_ago) / atr_20
        return momentum
    
    def calculate_volatility_risk(self, data: pd.DataFrame, symbol: str) -> float:
        """
        计算波动风险（单手合约的绝对波动金额）
        
        Args:
            data: 价格数据
            symbol: 品种代码
            
        Returns:
            单手风险金额
        """
        if len(data) < 10 or symbol not in self.contract_info:
            return float('inf')
            
        atr_10 = self._calculate_atr(data, 10)
        multiplier = self.contract_info[symbol]['multiplier']
        
        return atr_10 * multiplier
    
    def calculate_strategy_winrate(self, data: pd.DataFrame, lookback: int = 60) -> float:
        """
        计算策略胜率（简化版趋势跟踪策略）
        
        Args:
            data: 价格数据
            lookback: 回看天数
            
        Returns:
            策略胜率
        """
        if len(data) < lookback + 20:
            return 0.0
            
        # 简化的趋势策略：10日均线突破
        data = data.copy()
        data['ma10'] = data['close'].rolling(10).mean()
        data['signal'] = np.where(data['close'] > data['ma10'], 1, -1)
        data['signal_change'] = data['signal'].diff()
        
        # 计算交易信号
        entry_points = data[data['signal_change'] != 0].copy()
        if len(entry_points) < 2:
            return 0.0
            
        # 计算每笔交易的盈亏
        trades = []
        for i in range(len(entry_points) - 1):
            entry_price = entry_points.iloc[i]['close']
            exit_price = entry_points.iloc[i + 1]['close']
            signal = entry_points.iloc[i]['signal']
            
            pnl = (exit_price - entry_price) * signal
            trades.append(pnl > 0)
        
        if not trades:
            return 0.0
            
        return sum(trades) / len(trades)
    
    def calculate_liquidity_score(self, data: pd.DataFrame) -> float:
        """
        计算流动性评分
        
        Args:
            data: 包含成交量的数据
            
        Returns:
            流动性评分
        """
        if len(data) < 20:
            return 0.0
            
        avg_volume = data['volume'].rolling(20).mean().iloc[-1]
        return min(avg_volume / self.criteria.min_volume, 2.0)  # 最高2倍评分
    
    def calculate_correlation_matrix(self, price_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        计算品种间相关系数矩阵
        
        Args:
            price_data: 各品种价格数据字典
            
        Returns:
            相关系数矩阵
        """
        returns_data = {}
        
        for symbol, data in price_data.items():
            if len(data) >= 20:
                returns = data['close'].pct_change().dropna()
                if len(returns) > 0:
                    returns_data[symbol] = returns
        
        if len(returns_data) < 2:
            return pd.DataFrame()
            
        # 对齐数据长度
        min_length = min(len(returns) for returns in returns_data.values())
        aligned_data = {symbol: returns.iloc[-min_length:] 
                       for symbol, returns in returns_data.items()}
        
        returns_df = pd.DataFrame(aligned_data)
        return returns_df.corr()
    
    def select_instruments(self, price_data: Dict[str, pd.DataFrame]) -> List[InstrumentMetrics]:
        """
        执行品种筛选
        
        Args:
            price_data: 各品种价格数据
            
        Returns:
            筛选结果列表
        """
        logger.info(f"开始品种筛选，账户净值: {self.account_value:,.0f}")
        
        # 计算各品种指标
        metrics_list = []
        correlation_matrix = self.calculate_correlation_matrix(price_data)
        
        for symbol, data in price_data.items():
            if symbol not in self.contract_info:
                continue
                
            try:
                # 计算各项指标
                momentum = self.calculate_momentum_score(data)
                volatility_risk = self.calculate_volatility_risk(data, symbol)
                winrate = self.calculate_strategy_winrate(data)
                liquidity = self.calculate_liquidity_score(data)
                
                # 计算综合评分
                overall_score = self._calculate_overall_score(
                    momentum, volatility_risk, winrate, liquidity
                )
                
                metrics = InstrumentMetrics(
                    symbol=symbol,
                    momentum_score=momentum,
                    volatility_risk=volatility_risk,
                    strategy_winrate=winrate,
                    liquidity_score=liquidity,
                    correlation_risk=0.0,  # 后续计算
                    overall_score=overall_score
                )
                
                metrics_list.append(metrics)
                
            except Exception as e:
                logger.warning(f"计算 {symbol} 指标失败: {e}")
                continue
        
        # 应用筛选标准
        filtered_metrics = self._apply_selection_criteria(metrics_list, correlation_matrix)
        
        # 记录筛选结果
        self.selected_instruments = [m.symbol for m in filtered_metrics if m.is_selected]
        self.metrics_history.append({
            'timestamp': datetime.now(),
            'metrics': filtered_metrics,
            'selected_count': len(self.selected_instruments)
        })
        
        logger.info(f"筛选完成，选中品种: {self.selected_instruments}")
        
        return filtered_metrics
    
    def _calculate_atr(self, data: pd.DataFrame, period: int) -> float:
        """计算ATR"""
        if len(data) < period:
            return 0.0
            
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = true_range.rolling(period).mean().iloc[-1]
        
        return atr if not np.isnan(atr) else 0.0
    
    def _calculate_overall_score(self, momentum: float, volatility_risk: float, 
                               winrate: float, liquidity: float) -> float:
        """计算综合评分"""
        # 标准化各项指标
        momentum_norm = max(0, min(momentum / 2.0, 1.0))  # 动量标准化到0-1
        risk_norm = max(0, 1 - volatility_risk / (self.account_value * 0.02))  # 风险越小越好
        winrate_norm = max(0, winrate)  # 胜率直接使用
        liquidity_norm = min(liquidity, 1.0)  # 流动性标准化
        
        # 加权计算综合评分
        weights = [0.3, 0.25, 0.25, 0.2]  # 动量、风险、胜率、流动性权重
        score = (momentum_norm * weights[0] + 
                risk_norm * weights[1] + 
                winrate_norm * weights[2] + 
                liquidity_norm * weights[3])
        
        return score
    
    def _apply_selection_criteria(self, metrics_list: List[InstrumentMetrics], 
                                correlation_matrix: pd.DataFrame) -> List[InstrumentMetrics]:
        """应用筛选标准"""
        # 1. 基础筛选
        qualified = []
        for metrics in metrics_list:
            if (metrics.volatility_risk <= self.criteria.max_risk_per_lot and
                metrics.strategy_winrate >= self.criteria.min_winrate and
                metrics.liquidity_score >= 1.0):
                qualified.append(metrics)
        
        # 2. 按综合评分排序
        qualified.sort(key=lambda x: x.overall_score, reverse=True)
        
        # 3. 相关性过滤
        selected = []
        for metrics in qualified:
            if len(selected) >= self.criteria.max_instruments:
                break
                
            # 检查与已选品种的相关性
            is_correlated = False
            if not correlation_matrix.empty and metrics.symbol in correlation_matrix.index:
                for selected_symbol in [m.symbol for m in selected]:
                    if selected_symbol in correlation_matrix.columns:
                        corr = abs(correlation_matrix.loc[metrics.symbol, selected_symbol])
                        if corr > self.criteria.max_correlation:
                            is_correlated = True
                            break
            
            if not is_correlated:
                metrics.is_selected = True
                selected.append(metrics)
        
        return metrics_list
    
    def get_selection_report(self) -> Dict:
        """生成筛选报告"""
        if not self.metrics_history:
            return {}
            
        latest_metrics = self.metrics_history[-1]['metrics']
        selected_metrics = [m for m in latest_metrics if m.is_selected]
        
        report = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'account_value': self.account_value,
            'total_evaluated': len(latest_metrics),
            'selected_count': len(selected_metrics),
            'selected_instruments': [
                {
                    'symbol': m.symbol,
                    'name': self.contract_info.get(m.symbol, {}).get('name', ''),
                    'momentum_score': round(m.momentum_score, 3),
                    'volatility_risk': round(m.volatility_risk, 0),
                    'strategy_winrate': round(m.strategy_winrate, 3),
                    'liquidity_score': round(m.liquidity_score, 2),
                    'overall_score': round(m.overall_score, 3)
                }
                for m in selected_metrics
            ],
            'criteria': {
                'momentum_threshold': self.criteria.momentum_threshold,
                'max_risk_per_lot': self.criteria.max_risk_per_lot,
                'min_winrate': self.criteria.min_winrate,
                'max_correlation': self.criteria.max_correlation,
                'max_instruments': self.criteria.max_instruments
            }
        }
        
        return report

    def update_account_value(self, new_value: float):
        """更新账户净值"""
        self.account_value = new_value
        # 重新计算风险阈值
        self.criteria.max_risk_per_lot = new_value * 0.02

    def get_recommended_positions(self, selected_metrics: List[InstrumentMetrics]) -> Dict[str, int]:
        """
        根据筛选结果计算推荐仓位

        Args:
            selected_metrics: 已筛选的品种指标

        Returns:
            品种代码到推荐手数的映射
        """
        positions = {}
        single_instrument_risk = self.account_value * 0.02  # 单品种2%风险

        for metrics in selected_metrics:
            if metrics.is_selected and metrics.volatility_risk > 0:
                # 计算建议手数
                max_lots = int(single_instrument_risk / metrics.volatility_risk)
                positions[metrics.symbol] = max(1, max_lots)  # 至少1手

        return positions
