"""
智能期货交易系统GUI界面
Intelligent Futures Trading System GUI Interface

提供用户友好的图形界面，包括：
1. 系统控制面板
2. 品种筛选界面
3. 参数优化界面
4. 风险监控界面
5. 回测分析界面
6. 实时监控界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import json
import os
from datetime import datetime, timedelta
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates

# 导入系统模块
from main_system import IntelligentTradingSystem
from core.instrument_selector import InstrumentSelector
from core.parameter_optimizer import ParameterOptimizer
from core.risk_manager import RiskManager
from core.data_manager import DataManager

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class TradingSystemGUI:
    """智能期货交易系统GUI主界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("智能期货交易系统 v1.0")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')

        # 系统实例
        self.trading_system = None
        self.system_running = False

        # 创建界面
        self.create_widgets()

        # 状态变量
        self.status_var = tk.StringVar(value="系统未启动")
        self.account_var = tk.StringVar(value="5,000,000")

    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建标题
        title_label = tk.Label(main_frame, text="智能期货交易系统",
                              font=("Arial", 20, "bold"), bg='#f0f0f0')
        title_label.pack(pady=(0, 20))

        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # 创建各个选项卡
        self.create_control_tab()
        self.create_selection_tab()
        self.create_optimization_tab()
        self.create_risk_tab()
        self.create_backtest_tab()
        self.create_monitor_tab()

        # 创建状态栏
        self.create_status_bar()

    def create_control_tab(self):
        """创建系统控制选项卡"""
        control_frame = ttk.Frame(self.notebook)
        self.notebook.add(control_frame, text="系统控制")

        # 系统配置区域
        config_frame = ttk.LabelFrame(control_frame, text="系统配置", padding=10)
        config_frame.pack(fill=tk.X, padx=10, pady=5)

        # 账户净值设置
        ttk.Label(config_frame, text="账户净值:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.account_entry = ttk.Entry(config_frame, textvariable=self.account_var, width=15)
        self.account_entry.grid(row=0, column=1, padx=5)
        ttk.Label(config_frame, text="元").grid(row=0, column=2, sticky=tk.W)

        # 风险模式设置
        ttk.Label(config_frame, text="风险模式:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.risk_mode_var = tk.StringVar(value="conservative")
        risk_combo = ttk.Combobox(config_frame, textvariable=self.risk_mode_var,
                                 values=["conservative", "balanced", "aggressive"], width=12)
        risk_combo.grid(row=1, column=1, padx=5, pady=5)

        # 系统控制按钮
        button_frame = ttk.LabelFrame(control_frame, text="系统控制", padding=10)
        button_frame.pack(fill=tk.X, pady=10)

        self.start_button = ttk.Button(button_frame, text="启动系统",
                                      command=self.start_system, style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(button_frame, text="停止系统",
                                     command=self.stop_system, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="保存配置",
                  command=self.save_config).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="加载配置",
                  command=self.load_config).pack(side=tk.LEFT, padx=5)

        # 系统状态显示
        status_frame = ttk.LabelFrame(control_frame, text="系统状态", padding=10)
        status_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        self.status_text = tk.Text(status_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 添加初始状态信息
        self.log_message("系统已初始化，等待启动...")

    def create_selection_tab(self):
        """创建品种筛选选项卡"""
        selection_frame = ttk.Frame(self.notebook)
        self.notebook.add(selection_frame, text="品种筛选")

        # 筛选控制区域
        control_frame = ttk.LabelFrame(selection_frame, text="筛选控制", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="执行品种筛选",
                  command=self.run_instrument_selection).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="查看筛选报告",
                  command=self.show_selection_report).pack(side=tk.LEFT, padx=5)

        # 筛选结果显示
        result_frame = ttk.LabelFrame(selection_frame, text="筛选结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建表格
        columns = ("品种代码", "品种名称", "动量评分", "波动风险", "策略胜率", "综合评分", "是否选中")
        self.selection_tree = ttk.Treeview(result_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.selection_tree.heading(col, text=col)
            self.selection_tree.column(col, width=100, anchor=tk.CENTER)

        # 添加滚动条
        selection_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL,
                                          command=self.selection_tree.yview)
        self.selection_tree.configure(yscrollcommand=selection_scrollbar.set)

        self.selection_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        selection_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_optimization_tab(self):
        """创建参数优化选项卡"""
        optimization_frame = ttk.Frame(self.notebook)
        self.notebook.add(optimization_frame, text="参数优化")

        # 优化控制区域
        control_frame = ttk.LabelFrame(optimization_frame, text="优化控制", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="执行参数优化",
                  command=self.run_parameter_optimization).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="查看优化报告",
                  command=self.show_optimization_report).pack(side=tk.LEFT, padx=5)

        # 优化结果显示
        result_frame = ttk.LabelFrame(optimization_frame, text="优化结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建表格
        opt_columns = ("品种代码", "MA周期", "止损倍数", "止盈倍数", "Calmar比率", "稳定性", "状态")
        self.optimization_tree = ttk.Treeview(result_frame, columns=opt_columns, show="headings", height=15)

        for col in opt_columns:
            self.optimization_tree.heading(col, text=col)
            self.optimization_tree.column(col, width=100, anchor=tk.CENTER)

        # 添加滚动条
        opt_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL,
                                    command=self.optimization_tree.yview)
        self.optimization_tree.configure(yscrollcommand=opt_scrollbar.set)

        self.optimization_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        opt_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_risk_tab(self):
        """创建风险监控选项卡"""
        risk_frame = ttk.Frame(self.notebook)
        self.notebook.add(risk_frame, text="风险监控")

        # 风险指标显示
        metrics_frame = ttk.LabelFrame(risk_frame, text="风险指标", padding=10)
        metrics_frame.pack(fill=tk.X, padx=10, pady=5)

        # 创建风险指标标签
        self.risk_labels = {}
        risk_metrics = ["组合风险暴露", "最大单品种风险", "相关性风险", "波动率风险", "尾部风险评分"]

        for i, metric in enumerate(risk_metrics):
            ttk.Label(metrics_frame, text=f"{metric}:").grid(row=i//3, column=(i%3)*2, sticky=tk.W, padx=5, pady=2)
            self.risk_labels[metric] = ttk.Label(metrics_frame, text="0.00%", foreground="blue")
            self.risk_labels[metric].grid(row=i//3, column=(i%3)*2+1, sticky=tk.W, padx=5, pady=2)

        # 风险预警区域
        alert_frame = ttk.LabelFrame(risk_frame, text="风险预警", padding=10)
        alert_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.risk_text = tk.Text(alert_frame, height=20, wrap=tk.WORD)
        risk_scrollbar = ttk.Scrollbar(alert_frame, orient=tk.VERTICAL, command=self.risk_text.yview)
        self.risk_text.configure(yscrollcommand=risk_scrollbar.set)

        self.risk_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        risk_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_backtest_tab(self):
        """创建回测分析选项卡"""
        backtest_frame = ttk.Frame(self.notebook)
        self.notebook.add(backtest_frame, text="回测分析")

        # 回测控制区域
        control_frame = ttk.LabelFrame(backtest_frame, text="回测控制", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # 日期选择
        ttk.Label(control_frame, text="开始日期:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.start_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d'))
        ttk.Entry(control_frame, textvariable=self.start_date_var, width=12).grid(row=0, column=1, padx=5)

        ttk.Label(control_frame, text="结束日期:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.end_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        ttk.Entry(control_frame, textvariable=self.end_date_var, width=12).grid(row=0, column=3, padx=5)

        ttk.Button(control_frame, text="运行回测",
                  command=self.run_backtest).grid(row=0, column=4, padx=10)

        # 回测结果显示
        result_frame = ttk.LabelFrame(backtest_frame, text="回测结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建图表区域
        self.backtest_figure, self.backtest_ax = plt.subplots(figsize=(12, 6))
        self.backtest_canvas = FigureCanvasTkAgg(self.backtest_figure, result_frame)
        self.backtest_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_monitor_tab(self):
        """创建实时监控选项卡"""
        monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitor_frame, text="实时监控")

        # 性能指标显示
        performance_frame = ttk.LabelFrame(monitor_frame, text="性能指标", padding=10)
        performance_frame.pack(fill=tk.X, padx=10, pady=5)

        # 创建性能指标标签
        self.performance_labels = {}
        performance_metrics = ["总收益率", "日收益率", "夏普比率", "最大回撤", "胜率", "当前持仓"]

        for i, metric in enumerate(performance_metrics):
            ttk.Label(performance_frame, text=f"{metric}:").grid(row=i//3, column=(i%3)*2, sticky=tk.W, padx=5, pady=2)
            self.performance_labels[metric] = ttk.Label(performance_frame, text="0.00%", foreground="green")
            self.performance_labels[metric].grid(row=i//3, column=(i%3)*2+1, sticky=tk.W, padx=5, pady=2)

        # 系统健康评分
        health_frame = ttk.LabelFrame(monitor_frame, text="系统健康评分", padding=10)
        health_frame.pack(fill=tk.X, padx=10, pady=5)

        self.health_var = tk.StringVar(value="100")
        health_label = ttk.Label(health_frame, text="健康评分:", font=("Arial", 12))
        health_label.pack(side=tk.LEFT, padx=5)

        self.health_score_label = ttk.Label(health_frame, textvariable=self.health_var,
                                          font=("Arial", 16, "bold"), foreground="green")
        self.health_score_label.pack(side=tk.LEFT, padx=5)

        ttk.Label(health_frame, text="/100", font=("Arial", 12)).pack(side=tk.LEFT)

        # 实时日志
        log_frame = ttk.LabelFrame(monitor_frame, text="实时日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.monitor_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        monitor_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.monitor_text.yview)
        self.monitor_text.configure(yscrollcommand=monitor_scrollbar.set)

        self.monitor_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        monitor_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 时间显示
        self.time_label = ttk.Label(status_frame, text="", relief=tk.SUNKEN)
        self.time_label.pack(side=tk.RIGHT)

        # 更新时间
        self.update_time()

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.status_text.insert(tk.END, log_entry)
        self.status_text.see(tk.END)

        # 同时记录到监控日志
        self.monitor_text.insert(tk.END, log_entry)
        self.monitor_text.see(tk.END)

    def start_system(self):
        """启动系统"""
        try:
            account_value = float(self.account_var.get().replace(',', ''))

            # 创建系统实例
            config = {
                'account_value': account_value,
                'risk_mode': self.risk_mode_var.get(),
                'monitor_interval': 60
            }

            self.trading_system = IntelligentTradingSystem(account_value, config)

            # 在后台线程启动系统
            def start_thread():
                self.trading_system.start_system()
                self.system_running = True

            threading.Thread(target=start_thread, daemon=True).start()

            # 更新界面状态
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.status_var.set("系统运行中")

            self.log_message("系统启动成功")

            # 开始定期更新界面
            self.update_interface()

        except Exception as e:
            messagebox.showerror("错误", f"系统启动失败: {e}")
            self.log_message(f"系统启动失败: {e}", "ERROR")

    def stop_system(self):
        """停止系统"""
        try:
            if self.trading_system:
                self.trading_system.stop_system()
                self.system_running = False

            # 更新界面状态
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.status_var.set("系统已停止")

            self.log_message("系统停止成功")

        except Exception as e:
            messagebox.showerror("错误", f"系统停止失败: {e}")
            self.log_message(f"系统停止失败: {e}", "ERROR")

    def save_config(self):
        """保存配置"""
        try:
            config = {
                'account_value': self.account_var.get(),
                'risk_mode': self.risk_mode_var.get(),
                'start_date': self.start_date_var.get(),
                'end_date': self.end_date_var.get()
            }

            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                self.log_message(f"配置已保存到: {filename}")
                messagebox.showinfo("成功", "配置保存成功")

        except Exception as e:
            messagebox.showerror("错误", f"配置保存失败: {e}")

    def load_config(self):
        """加载配置"""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 更新界面
                self.account_var.set(config.get('account_value', '5,000,000'))
                self.risk_mode_var.set(config.get('risk_mode', 'conservative'))
                self.start_date_var.set(config.get('start_date', ''))
                self.end_date_var.set(config.get('end_date', ''))

                self.log_message(f"配置已从 {filename} 加载")
                messagebox.showinfo("成功", "配置加载成功")

        except Exception as e:
            messagebox.showerror("错误", f"配置加载失败: {e}")

    def run_instrument_selection(self):
        """运行品种筛选"""
        def selection_thread():
            try:
                self.log_message("开始执行品种筛选...")

                # 创建临时筛选器
                account_value = float(self.account_var.get().replace(',', ''))
                selector = InstrumentSelector(account_value)
                data_manager = DataManager()

                # 获取测试数据
                symbols = ['rb888.SHFE', 'cu888.SHFE', 'au888.SHFE', 'TA888.CZCE', 'm888.DCE',
                          'i888.DCE', 'j888.DCE', 'al888.SHFE', 'zn888.SHFE', 'ag888.SHFE']

                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')

                market_data = {}
                for symbol in symbols:
                    try:
                        data = data_manager.get_historical_data(symbol, start_date, end_date)
                        if not data.empty:
                            market_data[symbol] = data
                    except Exception as e:
                        self.log_message(f"获取 {symbol} 数据失败: {e}", "WARNING")

                if market_data:
                    # 执行筛选
                    metrics_list = selector.select_instruments(market_data)

                    # 更新界面
                    self.root.after(0, lambda: self.update_selection_results(metrics_list))
                    self.log_message(f"品种筛选完成，评估了 {len(metrics_list)} 个品种")
                else:
                    self.log_message("没有获取到有效的市场数据", "WARNING")

            except Exception as e:
                self.log_message(f"品种筛选失败: {e}", "ERROR")

        threading.Thread(target=selection_thread, daemon=True).start()

    def update_selection_results(self, metrics_list):
        """更新品种筛选结果"""
        # 清空现有数据
        for item in self.selection_tree.get_children():
            self.selection_tree.delete(item)

        # 添加新数据
        for metrics in metrics_list:
            status = "✓ 选中" if metrics.is_selected else "✗ 未选中"
            values = (
                metrics.symbol,
                "期货品种",  # 简化的品种名称
                f"{metrics.momentum_score:.3f}",
                f"{metrics.volatility_risk:.0f}",
                f"{metrics.strategy_winrate:.1%}",
                f"{metrics.overall_score:.3f}",
                status
            )

            # 根据是否选中设置不同的标签
            tag = "selected" if metrics.is_selected else "unselected"
            self.selection_tree.insert("", tk.END, values=values, tags=(tag,))

        # 设置标签样式
        self.selection_tree.tag_configure("selected", background="#e8f5e8")
        self.selection_tree.tag_configure("unselected", background="#f5f5f5")

    def show_selection_report(self):
        """显示筛选报告"""
        if self.trading_system:
            try:
                report = self.trading_system.instrument_selector.get_selection_report()
                if report:
                    report_window = tk.Toplevel(self.root)
                    report_window.title("品种筛选报告")
                    report_window.geometry("600x400")

                    report_text = tk.Text(report_window, wrap=tk.WORD)
                    report_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

                    # 格式化报告内容
                    content = f"""品种筛选报告
生成时间: {report.get('timestamp', 'N/A')}
账户净值: {report.get('account_value', 0):,.0f} 元
评估品种数: {report.get('total_evaluated', 0)}
选中品种数: {report.get('selected_count', 0)}

筛选标准:
- 动量阈值: {report.get('criteria', {}).get('momentum_threshold', 0):.1%}
- 最大风险: {report.get('criteria', {}).get('max_risk_per_lot', 0):,.0f} 元
- 最低胜率: {report.get('criteria', {}).get('min_winrate', 0):.1%}
- 最大相关性: {report.get('criteria', {}).get('max_correlation', 0):.1%}

选中品种详情:
"""

                    for instrument in report.get('selected_instruments', []):
                        content += f"""
品种: {instrument['symbol']} ({instrument['name']})
  动量评分: {instrument['momentum_score']:.3f}
  波动风险: {instrument['volatility_risk']:,.0f} 元
  策略胜率: {instrument['strategy_winrate']:.1%}
  综合评分: {instrument['overall_score']:.3f}
"""

                    report_text.insert(tk.END, content)
                    report_text.config(state=tk.DISABLED)
                else:
                    messagebox.showinfo("提示", "暂无筛选报告，请先执行品种筛选")
            except Exception as e:
                messagebox.showerror("错误", f"获取筛选报告失败: {e}")
        else:
            messagebox.showinfo("提示", "请先启动系统")

    def run_parameter_optimization(self):
        """运行参数优化"""
        def optimization_thread():
            try:
                self.log_message("开始执行参数优化...")

                if self.trading_system and self.trading_system.selected_instruments:
                    # 使用系统中已选择的品种
                    symbols = self.trading_system.selected_instruments[:3]  # 限制为前3个品种
                else:
                    # 使用默认品种
                    symbols = ['rb888.SHFE', 'cu888.SHFE', 'au888.SHFE']

                optimizer = ParameterOptimizer()
                data_manager = DataManager()

                # 获取历史数据
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')

                instruments_data = {}
                for symbol in symbols:
                    try:
                        data = data_manager.get_historical_data(symbol, start_date, end_date)
                        if not data.empty:
                            instruments_data[symbol] = data
                    except Exception as e:
                        self.log_message(f"获取 {symbol} 优化数据失败: {e}", "WARNING")

                if instruments_data:
                    # 执行优化
                    optimization_results = optimizer.batch_optimize(instruments_data, max_workers=2)

                    # 更新界面
                    self.root.after(0, lambda: self.update_optimization_results(optimization_results))
                    self.log_message(f"参数优化完成，优化了 {len(optimization_results)} 个品种")
                else:
                    self.log_message("没有获取到有效的优化数据", "WARNING")

            except Exception as e:
                self.log_message(f"参数优化失败: {e}", "ERROR")

        threading.Thread(target=optimization_thread, daemon=True).start()

    def update_optimization_results(self, optimization_results):
        """更新参数优化结果"""
        # 清空现有数据
        for item in self.optimization_tree.get_children():
            self.optimization_tree.delete(item)

        # 添加新数据
        for symbol, result in optimization_results.items():
            params = result.best_params
            status = "稳定" if result.is_stable else "不稳定"

            values = (
                symbol,
                params.get('ma_period', 'N/A'),
                f"{params.get('stop_atr_mult', 0):.1f}",
                f"{params.get('profit_atr_mult', 0):.1f}",
                f"{result.performance_metrics.get('calmar_ratio', 0):.3f}",
                f"{result.stability_score:.3f}",
                status
            )

            # 根据稳定性设置不同的标签
            tag = "stable" if result.is_stable else "unstable"
            self.optimization_tree.insert("", tk.END, values=values, tags=(tag,))

        # 设置标签样式
        self.optimization_tree.tag_configure("stable", background="#e8f5e8")
        self.optimization_tree.tag_configure("unstable", background="#ffe8e8")

    def show_optimization_report(self):
        """显示优化报告"""
        messagebox.showinfo("提示", "优化报告功能开发中...")

    def run_backtest(self):
        """运行回测"""
        def backtest_thread():
            try:
                self.log_message("开始运行回测...")

                start_date = self.start_date_var.get()
                end_date = self.end_date_var.get()

                if not start_date or not end_date:
                    self.log_message("请设置回测日期", "WARNING")
                    return

                # 创建临时系统进行回测
                account_value = float(self.account_var.get().replace(',', ''))
                temp_system = IntelligentTradingSystem(account_value)

                # 运行回测
                backtest_result = temp_system.run_backtest(start_date, end_date)

                if 'result' in backtest_result:
                    # 更新界面
                    self.root.after(0, lambda: self.update_backtest_results(backtest_result))
                    self.log_message("回测完成")
                else:
                    self.log_message("回测失败，请检查日期设置", "WARNING")

            except Exception as e:
                self.log_message(f"回测失败: {e}", "ERROR")

        threading.Thread(target=backtest_thread, daemon=True).start()

    def update_backtest_results(self, backtest_result):
        """更新回测结果"""
        try:
            result = backtest_result['result']

            # 清空图表
            self.backtest_ax.clear()

            # 绘制权益曲线
            if not result.equity_curve.empty:
                self.backtest_ax.plot(result.equity_curve.index, result.equity_curve.values,
                                    label='权益曲线', linewidth=2)
                self.backtest_ax.set_title('回测权益曲线')
                self.backtest_ax.set_xlabel('日期')
                self.backtest_ax.set_ylabel('权益')
                self.backtest_ax.legend()
                self.backtest_ax.grid(True, alpha=0.3)

                # 格式化x轴日期
                self.backtest_ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
                self.backtest_ax.xaxis.set_major_locator(mdates.MonthLocator())
                plt.setp(self.backtest_ax.xaxis.get_majorticklabels(), rotation=45)

            # 刷新图表
            self.backtest_figure.tight_layout()
            self.backtest_canvas.draw()

            # 显示回测统计
            stats_text = f"""回测统计结果:
总收益率: {result.total_return:.2%}
年化收益率: {result.annual_return:.2%}
最大回撤: {result.max_drawdown:.2%}
夏普比率: {result.sharpe_ratio:.3f}
Calmar比率: {result.calmar_ratio:.3f}
胜率: {result.win_rate:.1%}
盈亏比: {result.profit_factor:.2f}
总交易次数: {result.total_trades}
"""

            messagebox.showinfo("回测完成", stats_text)

        except Exception as e:
            self.log_message(f"更新回测结果失败: {e}", "ERROR")

    def update_interface(self):
        """定期更新界面"""
        if self.system_running and self.trading_system:
            try:
                # 更新系统健康评分
                health_score = self.trading_system.get_health_score()
                self.health_var.set(f"{health_score:.1f}")

                # 根据健康评分设置颜色
                if health_score >= 80:
                    self.health_score_label.config(foreground="green")
                elif health_score >= 60:
                    self.health_score_label.config(foreground="orange")
                else:
                    self.health_score_label.config(foreground="red")

                # 更新性能指标（模拟数据）
                self.performance_labels["总收益率"].config(text="5.2%")
                self.performance_labels["日收益率"].config(text="0.1%")
                self.performance_labels["夏普比率"].config(text="1.25")
                self.performance_labels["最大回撤"].config(text="3.8%")
                self.performance_labels["胜率"].config(text="58%")
                self.performance_labels["当前持仓"].config(text=f"{len(self.trading_system.selected_instruments)}")

                # 更新风险指标（模拟数据）
                self.risk_labels["组合风险暴露"].config(text="8.5%")
                self.risk_labels["最大单品种风险"].config(text="2.0%")
                self.risk_labels["相关性风险"].config(text="0.65")
                self.risk_labels["波动率风险"].config(text="1.2")
                self.risk_labels["尾部风险评分"].config(text="0.3")

            except Exception as e:
                self.log_message(f"界面更新失败: {e}", "ERROR")

        # 每5秒更新一次
        self.root.after(5000, self.update_interface)

    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = TradingSystemGUI()
        app.run()
    except Exception as e:
        print(f"GUI启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()