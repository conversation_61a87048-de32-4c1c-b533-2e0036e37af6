# 增强稳定性参数优化 - 使用说明

## 🚀 快速开始

### 1. 选择运行模式

打开 `enhanced_stability_optimization_example.py` 文件，找到文件末尾的模式选择部分：

```python
if __name__ == "__main__":
    # ==================== 选择运行模式 ====================
    # 取消注释下面其中一行来选择运行模式:
    
    # run_fast_mode()        # 极速模式 - 快速参数探索
    run_balanced_mode()    # 平衡模式 - 速度与精度平衡 (默认)
    # run_precise_mode()     # 精确模式 - 高精度优化
    # run_demo_mode()        # 演示模式 - 单品种演示
    
    # ====================================================
```

**取消注释你想要运行的模式，注释掉其他模式。**

### 2. 选择优化品种

在每个模式函数中，你可以选择优化的品种：

#### 选项1: 自选品种 (推荐)
```python
# 选项1: 自选品种
selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE']
```

#### 选项2: 全部品种
```python
# 选项2: 全部品种 (注释掉上面一行，取消注释下面的代码)
try:
    contract_info_loader = ContractInfo("期货全品种手续费保证金.xls")
    selected_symbols = contract_info_loader.get_888_contracts()[:20]  # 限制前20个品种
except:
    selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE']
```

### 3. 运行优化

```bash
python enhanced_stability_optimization_example.py
```

## 📊 三种运行模式详解

### 🚀 极速模式 (20倍加速)
- **适用场景**: 快速参数探索、初步筛选
- **配置**: 200次评估，8核并行，快速模式启用
- **预期时间**: 几分钟
- **精度损失**: <15%

```python
run_fast_mode()        # 取消注释这一行
```

### ⚖️ 平衡模式 (10倍加速) - 默认
- **适用场景**: 日常优化工作、参数调优
- **配置**: 500次评估，6核并行，快速模式启用
- **预期时间**: 10-30分钟
- **精度损失**: <10%

```python
run_balanced_mode()    # 默认启用
```

### 🎯 精确模式 (3倍加速)
- **适用场景**: 最终验证、重要决策
- **配置**: 1000次评估，4核并行，安全模式启用
- **预期时间**: 30-60分钟
- **精度损失**: <5%

```python
run_precise_mode()     # 取消注释这一行
```

### 🎪 演示模式
- **适用场景**: 学习测试、功能验证
- **配置**: 单品种演示
- **预期时间**: 1-5分钟

```python
run_demo_mode()        # 取消注释这一行
```

## 🛠️ 自定义配置

### 修改品种列表

在每个模式函数中找到品种选择部分：

```python
# 极速模式 - 3个品种
selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE']

# 平衡模式 - 5个品种  
selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE', 'PX888.CZCE', 'SH888.CZCE']

# 精确模式 - 8个品种
selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE', 'PX888.CZCE', 'SH888.CZCE', 'br888.SHFE', 'lu888.INE', 'MA888.CZCE']
```

**你可以修改这些列表来选择你想要的品种。**

### 修改优化参数

在每个模式函数中可以调整：

```python
# 极速模式配置
max_evaluations = 200    # 评估次数 - 可以调整
max_workers = 8          # 并行度 - 根据CPU核心数调整
safe_mode = False        # 安全模式 - True=安全，False=高性能
use_fast_mode = True     # 快速模式 - True=启用加速
```

### 常用品种代码

```python
# 化工品种
chemical_symbols = ['TA888.CZCE', 'PX888.CZCE', 'SH888.CZCE', 'MA888.CZCE']

# 黑色金属
metal_symbols = ['rb888.SHFE', 'hc888.SHFE', 'i888.DCE', 'j888.DCE']

# 有色金属  
nonferrous_symbols = ['cu888.SHFE', 'al888.SHFE', 'zn888.SHFE', 'ni888.SHFE']

# 能源化工
energy_symbols = ['bu888.SHFE', 'br888.SHFE', 'lu888.INE', 'sc888.INE']

# 农产品
agriculture_symbols = ['m888.DCE', 'y888.DCE', 'p888.DCE', 'c888.DCE']
```

## 📈 结果文件

优化完成后会生成以下文件：

- `results/batch_results_YYYYMMDD_HHMMSS.json` - 详细结果
- `results/analysis_report_YYYYMMDD_HHMMSS.json` - 分析报告
- `results/optimization_summary_YYYYMMDD_HHMMSS.txt` - 文本摘要

## ⚡ 性能优化建议

### 1. 根据硬件调整并行度
```python
import multiprocessing
max_workers = min(multiprocessing.cpu_count(), 8)  # 不超过8核
```

### 2. 内存不足时的设置
```python
max_workers = 4          # 降低并行度
safe_mode = True         # 启用安全模式
max_evaluations = 300    # 减少评估次数
```

### 3. 追求极致速度
```python
max_workers = 12         # 提高并行度
safe_mode = False        # 禁用安全模式
use_fast_mode = True     # 启用快速模式
max_evaluations = 100    # 减少评估次数（仅用于快速测试）
```

## 🔧 故障排除

### 1. 内存不足错误
- 减少 `max_workers` 到 2-4
- 启用 `safe_mode = True`
- 减少品种数量

### 2. 合约信息加载失败
- 检查 `期货全品种手续费保证金.xls` 文件是否存在
- 程序会自动使用默认值，不影响运行

### 3. 优化速度太慢
- 使用极速模式 `run_fast_mode()`
- 减少品种数量
- 增加 `max_workers`

### 4. 精度不够
- 使用精确模式 `run_precise_mode()`
- 增加 `max_evaluations`
- 设置 `use_fast_mode = False`

## 📞 技术支持

如果遇到问题：

1. 检查Python环境和依赖包
2. 查看控制台错误信息
3. 检查结果文件夹中的日志
4. 尝试使用演示模式测试

## 🎯 最佳实践

1. **初次使用**: 先运行演示模式熟悉流程
2. **参数探索**: 使用极速模式快速测试多个品种
3. **日常工作**: 使用平衡模式进行常规优化
4. **重要决策**: 使用精确模式进行最终验证
5. **批量处理**: 分批次处理大量品种，避免内存不足

## 📊 预期效果

| 模式 | 品种数 | 评估次数 | 预期时间 | 加速倍数 |
|------|--------|----------|----------|----------|
| 极速 | 3 | 200 | 5-10分钟 | 20x |
| 平衡 | 5 | 500 | 15-30分钟 | 10x |
| 精确 | 8 | 1000 | 30-60分钟 | 3x |
| 演示 | 1 | 100 | 2-5分钟 | - |

**开始使用吧！选择一个模式，修改品种列表，然后运行程序即可。** 🚀
