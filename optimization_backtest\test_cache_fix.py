#!/usr/bin/env python3
"""
缓存修复验证测试脚本
Cache Fix Validation Test Script

验证修复版缓存管理器是否正确解决了所有缓存污染问题
"""

import sys
import time
from pathlib import Path
import numpy as np
from typing import Dict, Any

# 添加路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from fixed_cache_manager import get_fixed_cache_manager, reset_cache_manager
from fixed_enhanced_backtester import create_fixed_enhanced_backtester


def test_symbol_isolation():
    """测试品种间缓存隔离"""
    print("🧪 测试1: 品种间缓存隔离")
    print("-" * 40)
    
    # 相同参数，不同品种
    test_params = {'sl_multiplier': 3.0, 'zy': 50, 'AF': 0.001}
    contract_info = {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}
    
    symbols = ['rb888.SHFE', 'cu888.SHFE', 'au888.SHFE']
    results = {}
    
    for symbol in symbols:
        backtester = create_fixed_enhanced_backtester(
            symbol=symbol,
            contract_info=contract_info,
            start_date="20230101",
            end_date="20230331"
        )
        result = backtester(test_params)
        results[symbol] = result['sharpe_ratio']
        print(f"  {symbol}: Sharpe = {result['sharpe_ratio']:.6f}")
    
    # 验证结果
    unique_results = len(set(results.values()))
    total_results = len(results)
    
    if unique_results == total_results:
        print(f"  ✅ 品种间缓存隔离正常 ({unique_results}/{total_results} 个不同结果)")
        return True
    else:
        print(f"  ❌ 品种间缓存污染 ({unique_results}/{total_results} 个不同结果)")
        return False


def test_time_range_isolation():
    """测试时间区间缓存隔离"""
    print("\n🧪 测试2: 时间区间缓存隔离")
    print("-" * 40)
    
    test_params = {'sl_multiplier': 3.0, 'zy': 50, 'AF': 0.001}
    contract_info = {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}
    symbol = 'rb888.SHFE'
    
    time_ranges = [
        ("20230101", "20230331"),
        ("20230401", "20230630"),
        ("20230701", "20230930")
    ]
    
    results = {}
    for start_date, end_date in time_ranges:
        backtester = create_fixed_enhanced_backtester(
            symbol=symbol,
            contract_info=contract_info,
            start_date=start_date,
            end_date=end_date
        )
        result = backtester(test_params)
        range_key = f"{start_date}-{end_date}"
        results[range_key] = result['sharpe_ratio']
        print(f"  {range_key}: Sharpe = {result['sharpe_ratio']:.6f}")
    
    # 验证结果
    unique_results = len(set(results.values()))
    total_results = len(results)
    
    if unique_results == total_results:
        print(f"  ✅ 时间区间缓存隔离正常 ({unique_results}/{total_results} 个不同结果)")
        return True
    else:
        print(f"  ❌ 时间区间缓存污染 ({unique_results}/{total_results} 个不同结果)")
        return False


def test_fast_mode_isolation():
    """测试快速模式缓存隔离"""
    print("\n🧪 测试3: 快速模式缓存隔离")
    print("-" * 40)
    
    test_params = {'sl_multiplier': 3.0, 'zy': 50, 'AF': 0.001}
    contract_info = {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}
    symbol = 'rb888.SHFE'
    
    results = {}
    for fast_mode in [True, False]:
        backtester = create_fixed_enhanced_backtester(
            symbol=symbol,
            contract_info=contract_info,
            start_date="20230101",
            end_date="20230331",
            fast_mode=fast_mode
        )
        result = backtester(test_params)
        mode_key = f"fast_{fast_mode}"
        results[mode_key] = result['sharpe_ratio']
        print(f"  快速模式={fast_mode}: Sharpe = {result['sharpe_ratio']:.6f}")
    
    # 验证结果
    unique_results = len(set(results.values()))
    total_results = len(results)
    
    if unique_results == total_results:
        print(f"  ✅ 快速模式缓存隔离正常 ({unique_results}/{total_results} 个不同结果)")
        return True
    else:
        print(f"  ❌ 快速模式缓存污染 ({unique_results}/{total_results} 个不同结果)")
        return False


def test_contract_info_isolation():
    """测试合约信息缓存隔离"""
    print("\n🧪 测试4: 合约信息缓存隔离")
    print("-" * 40)
    
    test_params = {'sl_multiplier': 3.0, 'zy': 50, 'AF': 0.001}
    symbol = 'rb888.SHFE'
    
    contract_configs = [
        {'size': 5, 'rate': 0.0002, 'pricetick': 1.0},
        {'size': 10, 'rate': 0.0002, 'pricetick': 1.0},
        {'size': 5, 'rate': 0.0003, 'pricetick': 1.0},
        {'size': 5, 'rate': 0.0002, 'pricetick': 5.0}
    ]
    
    results = {}
    for i, contract_info in enumerate(contract_configs):
        backtester = create_fixed_enhanced_backtester(
            symbol=symbol,
            contract_info=contract_info,
            start_date="20230101",
            end_date="20230331"
        )
        result = backtester(test_params)
        config_key = f"config_{i+1}"
        results[config_key] = result['sharpe_ratio']
        print(f"  配置{i+1} {contract_info}: Sharpe = {result['sharpe_ratio']:.6f}")
    
    # 验证结果
    unique_results = len(set(results.values()))
    total_results = len(results)
    
    if unique_results == total_results:
        print(f"  ✅ 合约信息缓存隔离正常 ({unique_results}/{total_results} 个不同结果)")
        return True
    else:
        print(f"  ❌ 合约信息缓存污染 ({unique_results}/{total_results} 个不同结果)")
        return False


def test_parameter_isolation():
    """测试参数缓存隔离"""
    print("\n🧪 测试5: 参数缓存隔离")
    print("-" * 40)
    
    contract_info = {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}
    symbol = 'rb888.SHFE'
    
    param_sets = [
        {'sl_multiplier': 3.0, 'zy': 50, 'AF': 0.001},
        {'sl_multiplier': 4.0, 'zy': 50, 'AF': 0.001},
        {'sl_multiplier': 3.0, 'zy': 60, 'AF': 0.001},
        {'sl_multiplier': 3.0, 'zy': 50, 'AF': 0.002}
    ]
    
    results = {}
    for i, params in enumerate(param_sets):
        backtester = create_fixed_enhanced_backtester(
            symbol=symbol,
            contract_info=contract_info,
            start_date="20230101",
            end_date="20230331"
        )
        result = backtester(params)
        param_key = f"params_{i+1}"
        results[param_key] = result['sharpe_ratio']
        print(f"  参数{i+1} {params}: Sharpe = {result['sharpe_ratio']:.6f}")
    
    # 验证结果
    unique_results = len(set(results.values()))
    total_results = len(results)
    
    if unique_results == total_results:
        print(f"  ✅ 参数缓存隔离正常 ({unique_results}/{total_results} 个不同结果)")
        return True
    else:
        print(f"  ❌ 参数缓存污染 ({unique_results}/{total_results} 个不同结果)")
        return False


def test_cache_consistency():
    """测试缓存一致性"""
    print("\n🧪 测试6: 缓存一致性验证")
    print("-" * 40)
    
    test_params = {'sl_multiplier': 3.0, 'zy': 50, 'AF': 0.001}
    contract_info = {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}
    symbol = 'rb888.SHFE'
    
    # 第一次调用
    backtester1 = create_fixed_enhanced_backtester(
        symbol=symbol,
        contract_info=contract_info,
        start_date="20230101",
        end_date="20230331"
    )
    result1 = backtester1(test_params)
    
    # 第二次调用（应该使用缓存）
    backtester2 = create_fixed_enhanced_backtester(
        symbol=symbol,
        contract_info=contract_info,
        start_date="20230101",
        end_date="20230331"
    )
    result2 = backtester2(test_params)
    
    print(f"  第一次调用: Sharpe = {result1['sharpe_ratio']:.6f}")
    print(f"  第二次调用: Sharpe = {result2['sharpe_ratio']:.6f}")
    
    # 验证结果一致性
    if abs(result1['sharpe_ratio'] - result2['sharpe_ratio']) < 1e-10:
        print("  ✅ 缓存一致性正常")
        return True
    else:
        print("  ❌ 缓存一致性异常")
        return False


def test_cache_performance():
    """测试缓存性能"""
    print("\n🧪 测试7: 缓存性能测试")
    print("-" * 40)
    
    test_params = {'sl_multiplier': 3.0, 'zy': 50, 'AF': 0.001}
    contract_info = {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}
    symbol = 'rb888.SHFE'
    
    # 第一次调用（无缓存）
    start_time = time.time()
    backtester1 = create_fixed_enhanced_backtester(
        symbol=symbol,
        contract_info=contract_info,
        start_date="20230101",
        end_date="20230331"
    )
    result1 = backtester1(test_params)
    first_call_time = time.time() - start_time
    
    # 第二次调用（使用缓存）
    start_time = time.time()
    backtester2 = create_fixed_enhanced_backtester(
        symbol=symbol,
        contract_info=contract_info,
        start_date="20230101",
        end_date="20230331"
    )
    result2 = backtester2(test_params)
    second_call_time = time.time() - start_time
    
    print(f"  第一次调用耗时: {first_call_time:.4f} 秒")
    print(f"  第二次调用耗时: {second_call_time:.4f} 秒")
    
    if second_call_time < first_call_time:
        speedup = first_call_time / second_call_time
        print(f"  ✅ 缓存加速效果: {speedup:.1f}x")
        return True
    else:
        print("  ⚠️  缓存未生效或性能无提升")
        return False


def show_cache_stats():
    """显示缓存统计信息"""
    print("\n📊 缓存统计信息")
    print("-" * 40)
    
    cache_manager = get_fixed_cache_manager()
    stats = cache_manager.get_cache_stats()
    
    print(f"  缓存命中率: {stats['hit_rate']:.1%}")
    print(f"  总命中数: {stats['total_hits']}")
    print(f"  总未命中数: {stats['total_misses']}")
    print(f"  总请求数: {stats['total_requests']}")
    print(f"  缓存失效数: {stats['invalidations']}")
    print(f"  缓存清理数: {stats['cleanups']}")
    print(f"  缓存文件数: {stats['cache_files_count']}")
    print(f"  配置文件数: {stats['config_files_count']}")
    print(f"  缓存目录大小: {stats['cache_dir_size_mb']:.2f} MB")


def main():
    """主测试函数"""
    print("🚀 缓存修复验证测试")
    print("=" * 60)
    print("验证修复版缓存管理器是否正确解决了所有缓存污染问题")
    print("=" * 60)
    
    # 重置缓存管理器
    reset_cache_manager()
    
    # 清理旧缓存
    cache_manager = get_fixed_cache_manager()
    cache_manager.clear_all_cache()
    
    # 执行所有测试
    tests = [
        ("品种间缓存隔离", test_symbol_isolation),
        ("时间区间缓存隔离", test_time_range_isolation),
        ("快速模式缓存隔离", test_fast_mode_isolation),
        ("合约信息缓存隔离", test_contract_info_isolation),
        ("参数缓存隔离", test_parameter_isolation),
        ("缓存一致性验证", test_cache_consistency),
        ("缓存性能测试", test_cache_performance)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
    
    # 显示缓存统计
    show_cache_stats()
    
    # 总结
    print(f"\n📋 测试总结")
    print("-" * 40)
    print(f"通过测试: {passed_tests}/{total_tests}")
    print(f"测试通过率: {passed_tests/total_tests:.1%}")
    
    if passed_tests == total_tests:
        print("🎉 所有缓存污染问题已修复！")
    else:
        print("⚠️  仍有缓存问题需要修复")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
