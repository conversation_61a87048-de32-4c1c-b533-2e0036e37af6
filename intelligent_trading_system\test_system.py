#!/usr/bin/env python3
"""
智能期货交易系统测试脚本
System Test Script

测试所有核心功能是否正常工作
"""

import sys
import os
import traceback
from datetime import datetime, timedelta

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """测试模块导入"""
    print("🔧 测试模块导入...")
    
    modules = [
        ('core.data_manager', 'DataManager'),
        ('core.instrument_selector', 'InstrumentSelector'),
        ('core.parameter_optimizer', 'ParameterOptimizer'),
        ('core.risk_manager', 'RiskManager'),
        ('core.monitor', 'SystemMonitor'),
        ('core.backtest_engine', 'BacktestEngine'),
        ('strategies.adaptive_trend_strategy', 'AdaptiveTrendStrategy'),
        ('contract_manager', 'get_contract_manager'),
        ('main_system', 'IntelligentTradingSystem')
    ]
    
    success_count = 0
    for module_name, class_name in modules:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"  ✅ {module_name}.{class_name}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {module_name}.{class_name}: {e}")
    
    print(f"导入测试: {success_count}/{len(modules)} 成功")
    return success_count == len(modules)

def test_contract_manager():
    """测试合约管理器"""
    print("\n📋 测试合约管理器...")
    
    try:
        from contract_manager import get_contract_manager
        
        contract_manager = get_contract_manager()
        
        # 测试基本功能
        contracts = contract_manager.get_all_contracts()
        print(f"  ✅ 加载合约数量: {len(contracts)}")
        
        # 测试合约信息获取
        test_symbol = 'rb888.SHFE'
        info = contract_manager.get_contract_info(test_symbol)
        print(f"  ✅ {test_symbol} 信息: 乘数={info['size']}, 名称={info['name']}")
        
        # 测试按交易所分类
        shfe_contracts = contract_manager.get_contracts_by_exchange('SHFE')
        print(f"  ✅ 上期所品种数量: {len(shfe_contracts)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 合约管理器测试失败: {e}")
        return False

def test_data_manager():
    """测试数据管理器"""
    print("\n📊 测试数据管理器...")
    
    try:
        from core.data_manager import DataManager
        
        data_manager = DataManager()
        
        # 测试数据获取
        symbol = 'rb888.SHFE'
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
        
        if not data.empty:
            print(f"  ✅ 获取 {symbol} 数据: {len(data)} 条")
            print(f"  ✅ 数据列: {list(data.columns)}")
            
            # 测试数据质量
            quality_report = data_manager.get_data_quality_report(symbol, start_date, end_date)
            print(f"  ✅ 数据质量评分: {quality_report.get('quality_score', 0):.3f}")
        else:
            print(f"  ⚠️  {symbol} 数据为空，使用模拟数据")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据管理器测试失败: {e}")
        traceback.print_exc()
        return False

def test_instrument_selector():
    """测试品种筛选器"""
    print("\n🔍 测试品种筛选器...")
    
    try:
        from core.instrument_selector import InstrumentSelector
        from core.data_manager import DataManager
        
        selector = InstrumentSelector(account_value=5000000)
        data_manager = DataManager()
        
        # 获取测试数据
        symbols = ['rb888.SHFE', 'cu888.SHFE', 'au888.SHFE']
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
        
        market_data = {}
        for symbol in symbols:
            data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
            if not data.empty:
                market_data[symbol] = data
        
        if market_data:
            # 执行筛选
            metrics_list = selector.select_instruments(market_data)
            selected = [m.symbol for m in metrics_list if m.is_selected]
            
            print(f"  ✅ 评估品种数: {len(metrics_list)}")
            print(f"  ✅ 选中品种数: {len(selected)}")
            print(f"  ✅ 选中品种: {selected}")
            
            # 测试报告生成
            report = selector.get_selection_report()
            if report:
                print(f"  ✅ 生成筛选报告成功")
            
            return True
        else:
            print(f"  ❌ 没有获取到有效的市场数据")
            return False
        
    except Exception as e:
        print(f"  ❌ 品种筛选器测试失败: {e}")
        traceback.print_exc()
        return False

def test_parameter_optimizer():
    """测试参数优化器"""
    print("\n⚙️ 测试参数优化器...")
    
    try:
        from core.parameter_optimizer import ParameterOptimizer
        from core.data_manager import DataManager
        
        optimizer = ParameterOptimizer()
        data_manager = DataManager()
        
        # 获取测试数据
        symbol = 'rb888.SHFE'
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
        
        data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
        
        if not data.empty and len(data) >= 60:
            print(f"  ✅ 获取 {symbol} 优化数据: {len(data)} 条")
            
            # 执行优化（使用较少的参数组合以加快速度）
            result = optimizer.optimize_parameters(symbol, data, max_workers=1)
            
            print(f"  ✅ 优化完成: Calmar比率={result.performance_metrics.get('calmar_ratio', 0):.3f}")
            print(f"  ✅ 参数稳定性: {result.stability_score:.3f}")
            print(f"  ✅ 最佳参数: {result.best_params}")
            
            return True
        else:
            print(f"  ⚠️  数据不足，跳过参数优化测试")
            return True
        
    except Exception as e:
        print(f"  ❌ 参数优化器测试失败: {e}")
        traceback.print_exc()
        return False

def test_risk_manager():
    """测试风险管理器"""
    print("\n🛡️ 测试风险管理器...")
    
    try:
        from core.risk_manager import RiskManager, PositionInfo
        from contract_manager import get_contract_manager
        
        contract_manager = get_contract_manager()
        risk_manager = RiskManager(account_value=5000000, contract_info=contract_manager.contract_params)
        
        # 测试头寸计算
        symbol = 'rb888.SHFE'
        atr_value = 80.0
        position_size = risk_manager.calculate_position_size(symbol, atr_value)
        print(f"  ✅ {symbol} 建议头寸: {position_size} 手")
        
        # 测试组合风险计算
        positions = {
            'rb888.SHFE': PositionInfo(
                symbol='rb888.SHFE',
                current_lots=5,
                suggested_lots=6,
                risk_amount=100000,
                risk_percentage=0.02,
                atr_value=80.0,
                contract_value=200000
            )
        }
        
        risk_metrics = risk_manager.calculate_portfolio_risk(positions)
        print(f"  ✅ 组合风险暴露: {risk_metrics.portfolio_risk_percentage:.1%}")
        
        # 测试风险检查
        warnings = risk_manager.check_risk_limits(positions)
        total_warnings = sum(len(w) for w in warnings.values())
        print(f"  ✅ 风险检查: {total_warnings} 条警告")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 风险管理器测试失败: {e}")
        traceback.print_exc()
        return False

def test_strategy():
    """测试策略"""
    print("\n📈 测试自适应趋势策略...")
    
    try:
        from strategies.adaptive_trend_strategy import AdaptiveTrendStrategy
        from core.data_manager import DataManager
        
        strategy = AdaptiveTrendStrategy()
        data_manager = DataManager()
        
        # 获取测试数据
        symbol = 'rb888.SHFE'
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
        
        data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
        
        if not data.empty:
            # 计算技术指标
            indicators = strategy.calculate_indicators(data, symbol)
            print(f"  ✅ 计算技术指标: {list(indicators.keys())}")
            
            # 测试信号生成
            if len(data) > 20:
                test_date = data.index[-1]
                daily_data = {symbol: data.iloc[-1]}
                signals = strategy.generate_signals(daily_data, test_date)
                print(f"  ✅ 生成交易信号: {signals}")
            
            # 测试策略状态
            status = strategy.get_strategy_status()
            print(f"  ✅ 策略状态: {status['strategy_name']}")
            
            return True
        else:
            print(f"  ❌ 没有获取到策略测试数据")
            return False
        
    except Exception as e:
        print(f"  ❌ 策略测试失败: {e}")
        traceback.print_exc()
        return False

def test_backtest_engine():
    """测试回测引擎"""
    print("\n🔄 测试回测引擎...")
    
    try:
        from core.backtest_engine import BacktestEngine
        from strategies.adaptive_trend_strategy import AdaptiveTrendStrategy
        from core.data_manager import DataManager
        
        backtest_engine = BacktestEngine(initial_capital=5000000)
        strategy = AdaptiveTrendStrategy()
        data_manager = DataManager()
        
        # 获取回测数据
        symbols = ['rb888.SHFE']
        start_date = '2024-01-01'
        end_date = '2024-03-01'
        
        backtest_data = {}
        for symbol in symbols:
            data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
            if not data.empty:
                backtest_data[symbol] = data
                strategy.calculate_indicators(data, symbol)
        
        if backtest_data:
            # 运行回测
            result = backtest_engine.run_backtest(strategy, backtest_data, start_date, end_date)
            
            print(f"  ✅ 回测完成")
            print(f"  ✅ 总收益率: {result.total_return:.2%}")
            print(f"  ✅ 最大回撤: {result.max_drawdown:.2%}")
            print(f"  ✅ 交易次数: {result.total_trades}")
            
            return True
        else:
            print(f"  ❌ 没有获取到回测数据")
            return False
        
    except Exception as e:
        print(f"  ❌ 回测引擎测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 智能期货交易系统测试")
    print("="*60)
    
    tests = [
        ("模块导入", test_imports),
        ("合约管理器", test_contract_manager),
        ("数据管理器", test_data_manager),
        ("品种筛选器", test_instrument_selector),
        ("参数优化器", test_parameter_optimizer),
        ("风险管理器", test_risk_manager),
        ("自适应策略", test_strategy),
        ("回测引擎", test_backtest_engine)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"🧪 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        print("\n🚀 可以使用以下命令启动系统:")
        print("   python launch.py")
    else:
        print("⚠️  部分测试失败，请检查系统配置")
    
    print("="*60)

if __name__ == "__main__":
    main()
