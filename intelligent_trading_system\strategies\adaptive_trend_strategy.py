"""
自适应趋势策略
Adaptive Trend Strategy

基于移动平均线的趋势跟踪策略，具有以下特点：
1. 自适应参数调整
2. ATR动态止损止盈
3. 成交量过滤
4. 趋势强度判断
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class AdaptiveTrendStrategy:
    """自适应趋势策略"""
    
    def __init__(self, parameters: Optional[Dict[str, Any]] = None):
        """
        初始化策略
        
        Args:
            parameters: 策略参数字典
        """
        # 默认参数
        self.default_params = {
            'ma_period': 15,           # 移动平均线周期
            'stop_atr_mult': 2.5,      # 止损ATR倍数
            'profit_atr_mult': 3.0,    # 止盈ATR倍数
            'atr_period': 14,          # ATR计算周期
            'volume_filter': 1.0,      # 成交量过滤倍数
            'trend_strength': 0.2      # 趋势强度阈值
        }
        
        # 更新参数
        self.params = self.default_params.copy()
        if parameters:
            self.params.update(parameters)
        
        # 策略状态
        self.positions = {}  # symbol -> position_info
        self.indicators = {}  # symbol -> indicators
        self.signals_history = []
        
        logger.info(f"自适应趋势策略初始化完成，参数: {self.params}")
    
    def update_parameters(self, new_params: Dict[str, Any]):
        """
        更新策略参数
        
        Args:
            new_params: 新参数字典
        """
        old_params = self.params.copy()
        self.params.update(new_params)
        
        logger.info(f"策略参数已更新: {old_params} -> {self.params}")
    
    def calculate_indicators(self, data: pd.DataFrame, symbol: str) -> Dict[str, pd.Series]:
        """
        计算技术指标
        
        Args:
            data: 价格数据
            symbol: 品种代码
            
        Returns:
            技术指标字典
        """
        if len(data) < max(self.params['ma_period'], self.params['atr_period']):
            return {}
        
        indicators = {}
        
        # 移动平均线
        indicators['ma'] = data['close'].rolling(self.params['ma_period']).mean()
        
        # ATR
        indicators['atr'] = self._calculate_atr(data, self.params['atr_period'])
        
        # 成交量移动平均
        indicators['volume_ma'] = data['volume'].rolling(20).mean()
        
        # 趋势强度指标
        indicators['trend_strength'] = self._calculate_trend_strength(data)
        
        # 价格相对位置
        indicators['price_position'] = (data['close'] - indicators['ma']) / indicators['atr']
        
        # 突破信号
        indicators['breakout'] = self._calculate_breakout_signal(data, indicators['ma'])
        
        # 缓存指标
        self.indicators[symbol] = indicators
        
        return indicators
    
    def _calculate_atr(self, data: pd.DataFrame, period: int) -> pd.Series:
        """计算ATR"""
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()
    
    def _calculate_trend_strength(self, data: pd.DataFrame) -> pd.Series:
        """计算趋势强度"""
        # 使用价格变化的标准差衡量趋势强度
        price_change = data['close'].pct_change()
        trend_strength = price_change.rolling(10).std()
        
        return trend_strength
    
    def _calculate_breakout_signal(self, data: pd.DataFrame, ma: pd.Series) -> pd.Series:
        """计算突破信号"""
        # 价格突破移动平均线的信号
        close_above_ma = data['close'] > ma
        close_below_ma = data['close'] < ma
        
        # 突破信号：从下方突破到上方为1，从上方突破到下方为-1
        breakout = pd.Series(0, index=data.index)
        
        # 上突破
        breakout[(close_above_ma) & (~close_above_ma.shift())] = 1
        
        # 下突破
        breakout[(close_below_ma) & (~close_below_ma.shift())] = -1
        
        return breakout
    
    def generate_signals(self, daily_data: Dict[str, pd.Series], 
                        current_date: datetime) -> Dict[str, int]:
        """
        生成交易信号
        
        Args:
            daily_data: 当日市场数据
            current_date: 当前日期
            
        Returns:
            交易信号字典 {symbol: signal}
            signal: 1=买入, -1=卖出, 0=平仓
        """
        signals = {}
        
        for symbol, current_bar in daily_data.items():
            try:
                signal = self._generate_symbol_signal(symbol, current_bar, current_date)
                if signal != 0:
                    signals[symbol] = signal
                    
            except Exception as e:
                logger.error(f"生成 {symbol} 信号失败: {e}")
                continue
        
        # 记录信号历史
        self.signals_history.append({
            'date': current_date,
            'signals': signals.copy()
        })
        
        return signals
    
    def _generate_symbol_signal(self, symbol: str, current_bar: pd.Series, 
                               current_date: datetime) -> int:
        """生成单个品种的交易信号"""
        # 检查是否有足够的指标数据
        if symbol not in self.indicators:
            return 0
        
        indicators = self.indicators[symbol]
        
        # 获取当前指标值
        try:
            current_price = current_bar['close']
            current_ma = indicators['ma'].loc[current_date] if current_date in indicators['ma'].index else np.nan
            current_atr = indicators['atr'].loc[current_date] if current_date in indicators['atr'].index else np.nan
            current_volume = current_bar['volume']
            volume_ma = indicators['volume_ma'].loc[current_date] if current_date in indicators['volume_ma'].index else np.nan
            trend_strength = indicators['trend_strength'].loc[current_date] if current_date in indicators['trend_strength'].index else np.nan
            breakout = indicators['breakout'].loc[current_date] if current_date in indicators['breakout'].index else 0
            
        except (KeyError, IndexError):
            return 0
        
        # 检查数据有效性
        if np.isnan(current_ma) or np.isnan(current_atr) or np.isnan(volume_ma):
            return 0
        
        # 检查当前持仓
        current_position = self.positions.get(symbol, {})
        has_position = bool(current_position)
        position_direction = current_position.get('direction', 0)
        
        # 成交量过滤
        volume_threshold = volume_ma * self.params['volume_filter']
        if current_volume < volume_threshold:
            return 0
        
        # 趋势强度过滤
        if not np.isnan(trend_strength) and trend_strength < self.params['trend_strength']:
            return 0
        
        # 生成信号
        signal = 0
        
        if not has_position:
            # 开仓信号
            if breakout == 1 and current_price > current_ma:
                # 多头开仓
                signal = 1
                self._update_position(symbol, 1, current_price, current_atr, current_date)
                
            elif breakout == -1 and current_price < current_ma:
                # 空头开仓
                signal = -1
                self._update_position(symbol, -1, current_price, current_atr, current_date)
        
        else:
            # 检查止损止盈
            entry_price = current_position['entry_price']
            entry_atr = current_position['entry_atr']
            
            if position_direction == 1:  # 多头持仓
                stop_loss = entry_price - entry_atr * self.params['stop_atr_mult']
                take_profit = entry_price + entry_atr * self.params['profit_atr_mult']
                
                if current_price <= stop_loss or current_price >= take_profit or current_price < current_ma:
                    # 平多仓
                    signal = 0
                    self._close_position(symbol)
                    
            elif position_direction == -1:  # 空头持仓
                stop_loss = entry_price + entry_atr * self.params['stop_atr_mult']
                take_profit = entry_price - entry_atr * self.params['profit_atr_mult']
                
                if current_price >= stop_loss or current_price <= take_profit or current_price > current_ma:
                    # 平空仓
                    signal = 0
                    self._close_position(symbol)
        
        return signal
    
    def _update_position(self, symbol: str, direction: int, price: float, 
                        atr: float, timestamp: datetime):
        """更新持仓信息"""
        self.positions[symbol] = {
            'direction': direction,
            'entry_price': price,
            'entry_atr': atr,
            'entry_time': timestamp
        }
        
        logger.debug(f"开仓: {symbol} {direction} @{price}")
    
    def _close_position(self, symbol: str):
        """平仓"""
        if symbol in self.positions:
            del self.positions[symbol]
            logger.debug(f"平仓: {symbol}")
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            'strategy_name': 'AdaptiveTrendStrategy',
            'parameters': self.params,
            'current_positions': len(self.positions),
            'position_details': {
                symbol: {
                    'direction': pos['direction'],
                    'entry_price': pos['entry_price'],
                    'entry_time': pos['entry_time'].strftime('%Y-%m-%d %H:%M:%S')
                }
                for symbol, pos in self.positions.items()
            },
            'recent_signals': self.signals_history[-10:] if self.signals_history else []
        }
    
    def reset_strategy(self):
        """重置策略状态"""
        self.positions.clear()
        self.indicators.clear()
        self.signals_history.clear()
        
        logger.info("策略状态已重置")
    
    def validate_parameters(self, params: Dict[str, Any]) -> List[str]:
        """
        验证参数有效性
        
        Args:
            params: 待验证的参数
            
        Returns:
            错误信息列表
        """
        errors = []
        
        # 检查必需参数
        required_params = ['ma_period', 'stop_atr_mult', 'profit_atr_mult', 'atr_period']
        for param in required_params:
            if param not in params:
                errors.append(f"缺少必需参数: {param}")
        
        # 检查参数范围
        if 'ma_period' in params and (params['ma_period'] < 5 or params['ma_period'] > 50):
            errors.append("ma_period 应在 5-50 之间")
        
        if 'stop_atr_mult' in params and (params['stop_atr_mult'] < 1.0 or params['stop_atr_mult'] > 5.0):
            errors.append("stop_atr_mult 应在 1.0-5.0 之间")
        
        if 'profit_atr_mult' in params and (params['profit_atr_mult'] < 1.5 or params['profit_atr_mult'] > 8.0):
            errors.append("profit_atr_mult 应在 1.5-8.0 之间")
        
        if 'atr_period' in params and (params['atr_period'] < 5 or params['atr_period'] > 30):
            errors.append("atr_period 应在 5-30 之间")
        
        # 检查逻辑关系
        if ('stop_atr_mult' in params and 'profit_atr_mult' in params and 
            params['profit_atr_mult'] <= params['stop_atr_mult']):
            errors.append("profit_atr_mult 应大于 stop_atr_mult")
        
        return errors
    
    def get_parameter_description(self) -> Dict[str, str]:
        """获取参数说明"""
        return {
            'ma_period': '移动平均线周期，用于判断趋势方向',
            'stop_atr_mult': '止损ATR倍数，控制单笔交易风险',
            'profit_atr_mult': '止盈ATR倍数，设定盈利目标',
            'atr_period': 'ATR计算周期，衡量市场波动性',
            'volume_filter': '成交量过滤倍数，过滤低流动性时段',
            'trend_strength': '趋势强度阈值，过滤震荡行情'
        }
    
    def calculate_expected_performance(self, historical_data: Dict[str, pd.DataFrame]) -> Dict[str, float]:
        """
        基于历史数据计算预期表现
        
        Args:
            historical_data: 历史数据
            
        Returns:
            预期表现指标
        """
        # 这里可以实现基于历史数据的策略表现预测
        # 简化版本：返回基本统计信息
        
        total_bars = sum(len(data) for data in historical_data.values())
        avg_volatility = np.mean([
            data['close'].pct_change().std() for data in historical_data.values()
            if len(data) > 1
        ])
        
        # 基于参数估算预期表现
        expected_win_rate = 0.45 + (self.params['profit_atr_mult'] - self.params['stop_atr_mult']) * 0.05
        expected_profit_factor = self.params['profit_atr_mult'] / self.params['stop_atr_mult']
        
        return {
            'expected_win_rate': min(0.7, max(0.3, expected_win_rate)),
            'expected_profit_factor': min(3.0, max(1.0, expected_profit_factor)),
            'expected_annual_trades': total_bars / (self.params['ma_period'] * 2),
            'risk_level': avg_volatility * self.params['stop_atr_mult']
        }
