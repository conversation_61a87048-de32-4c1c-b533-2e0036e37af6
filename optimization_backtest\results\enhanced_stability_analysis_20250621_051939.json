{"success_count": 8, "failure_count": 0, "success_rate": 1.0, "batch_config": {}, "performance_stats": {"mean": 3.669002563651829, "std": 0.4427417395325423, "min": 3.097425435691683, "max": 4.011948840427917, "median": 4.011948840427917}, "stability_stats": {"mean": 0.9444869896243083, "std": 0.0063120065182238555, "min": 0.9363382242488971, "max": 0.9493762488495551, "median": 0.9493762488495551}, "time_stats": {"mean": 8.361787110567093, "std": 0.3938430354105182, "total": 66.89429688453674}, "parameter_statistics": {"k_1": {"mean": 10.0, "std": 0.0, "min": 10, "max": 10, "median": 10.0}, "k_3": {"mean": 5.0, "std": 0.0, "min": 5, "max": 5, "median": 5.0}, "k_5": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "sl_multiplier": {"mean": 7.9375, "std": 2.6626760505175993, "min": 4.5, "max": 10.0, "median": 10.0}, "macd_boll_count_fz": {"mean": 0.22499999999999998, "std": 0.05809475019311125, "min": 0.18, "max": 0.3, "median": 0.18}, "dk_fz": {"mean": 0.9624999999999999, "std": 0.0484122918275927, "min": 0.9, "max": 1.0, "median": 1.0}, "ping_zy": {"mean": 0.0, "std": 0.0, "min": 0, "max": 0, "median": 0.0}, "AF": {"mean": 0.002, "std": 0.0, "min": 0.002, "max": 0.002, "median": 0.002}, "AF_max": {"mean": 0.05, "std": 0.03872983346207417, "min": 0.0, "max": 0.08, "median": 0.08}, "trailing_start_ratio": {"mean": 0.425, "std": 0.2904737509655563, "min": 0.2, "max": 0.8, "median": 0.2}, "daily_loss_limit": {"mean": 3000.0, "std": 0.0, "min": 3000, "max": 3000, "median": 3000.0}, "k_15": {"mean": 15.0, "std": 0.0, "min": 15, "max": 15, "median": 15.0}, "k_30": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "atr_window": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "donchian_period": {"mean": 20.0, "std": 0.0, "min": 20, "max": 20, "median": 20.0}, "lots": {"mean": 1.0, "std": 0.0, "min": 1, "max": 1, "median": 1.0}, "use_trailing_stop": {"mean": 1.0, "std": 0.0, "min": 1, "max": 1, "median": 1.0}, "contract_multiplier": {"mean": 10.625, "std": 7.680128579652817, "min": 5.0, "max": 30.0, "median": 10.0}}, "top_performers": [{"symbol": "bu888.SHFE", "best_performance": 4.011948840427917, "stability_score": 0.9493762488495551}, {"symbol": "rb888.SHFE", "best_performance": 4.011948840427917, "stability_score": 0.9493762488495551}, {"symbol": "SH888.CZCE", "best_performance": 4.011948840427917, "stability_score": 0.9493762488495551}, {"symbol": "lu888.INE", "best_performance": 4.011948840427917, "stability_score": 0.9493762488495551}, {"symbol": "MA888.CZCE", "best_performance": 4.011948840427917, "stability_score": 0.9493762488495551}], "most_stable": [{"symbol": "bu888.SHFE", "best_performance": 4.011948840427917, "stability_score": 0.9493762488495551}, {"symbol": "rb888.SHFE", "best_performance": 4.011948840427917, "stability_score": 0.9493762488495551}, {"symbol": "SH888.CZCE", "best_performance": 4.011948840427917, "stability_score": 0.9493762488495551}, {"symbol": "lu888.INE", "best_performance": 4.011948840427917, "stability_score": 0.9493762488495551}, {"symbol": "MA888.CZCE", "best_performance": 4.011948840427917, "stability_score": 0.9493762488495551}], "failed_symbols": []}