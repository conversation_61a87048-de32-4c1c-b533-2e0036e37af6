#!/usr/bin/env python3
"""
智能期货交易系统启动脚本
Simple System Startup Script

解决所有路径和依赖问题的简化启动脚本
"""

import sys
import os
import traceback
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("🚀 智能期货交易系统启动器")
print("="*60)
print(f"当前目录: {current_dir}")
print(f"Python版本: {sys.version}")
print("="*60)

def check_dependencies():
    """检查依赖"""
    print("📦 检查依赖包...")
    
    required_packages = {
        'pandas': 'pandas',
        'numpy': 'numpy', 
        'matplotlib': 'matplotlib',
        'tkinter': 'tkinter'
    }
    
    missing_packages = []
    
    for package, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install pandas numpy matplotlib")
        return False
    
    print("✅ 所有依赖包检查通过")
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")
    
    try:
        # 测试数据管理器
        from core.data_manager import DataManager
        data_manager = DataManager()
        print("  ✅ 数据管理器")
        
        # 测试品种筛选器
        from core.instrument_selector import InstrumentSelector
        selector = InstrumentSelector(5000000)
        print("  ✅ 品种筛选器")
        
        # 测试风险管理器
        from core.risk_manager import RiskManager
        from contract_manager import get_contract_manager
        contract_manager = get_contract_manager()
        risk_manager = RiskManager(5000000, contract_manager.contract_params)
        print("  ✅ 风险管理器")
        
        # 测试策略
        from strategies.adaptive_trend_strategy import AdaptiveTrendStrategy
        strategy = AdaptiveTrendStrategy()
        print("  ✅ 自适应趋势策略")
        
        print("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        traceback.print_exc()
        return False

def run_demo():
    """运行演示"""
    print("\n🎯 运行系统演示...")
    
    try:
        # 简化的演示
        from core.data_manager import DataManager
        from core.instrument_selector import InstrumentSelector
        from contract_manager import get_contract_manager
        
        # 获取合约管理器
        contract_manager = get_contract_manager()
        print(f"  📊 加载了 {len(contract_manager.get_all_contracts())} 个期货品种")
        
        # 创建数据管理器
        data_manager = DataManager()
        print("  📈 数据管理器初始化完成")
        
        # 创建品种筛选器
        selector = InstrumentSelector(5000000)
        print("  🔍 品种筛选器初始化完成")
        
        # 获取一些测试数据
        test_symbols = ['rb888.SHFE', 'cu888.SHFE', 'au888.SHFE']
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = '2024-01-01'
        
        print(f"  📊 获取测试数据: {start_date} 到 {end_date}")
        
        market_data = {}
        for symbol in test_symbols:
            try:
                data = data_manager.get_historical_data(symbol, start_date, end_date)
                if not data.empty:
                    market_data[symbol] = data
                    print(f"    ✅ {symbol}: {len(data)} 条数据")
                else:
                    print(f"    ⚠️  {symbol}: 使用模拟数据")
            except Exception as e:
                print(f"    ❌ {symbol}: {e}")
        
        if market_data:
            # 执行品种筛选
            print("  🔍 执行品种筛选...")
            metrics_list = selector.select_instruments(market_data)
            selected = [m.symbol for m in metrics_list if m.is_selected]
            print(f"    ✅ 筛选完成，选中品种: {selected}")
            
            # 显示筛选结果
            print("\n  📋 筛选结果详情:")
            for metrics in metrics_list:
                status = "✅ 选中" if metrics.is_selected else "❌ 未选中"
                print(f"    {metrics.symbol:12} {status} - "
                      f"动量:{metrics.momentum_score:6.2f} "
                      f"胜率:{metrics.strategy_winrate:5.1%} "
                      f"综合:{metrics.overall_score:5.3f}")
        
        print("✅ 演示运行完成")
        return True
        
    except Exception as e:
        print(f"❌ 演示运行失败: {e}")
        traceback.print_exc()
        return False

def start_gui():
    """启动GUI界面"""
    print("\n🖥️  启动GUI界面...")
    
    try:
        from gui_interface import TradingSystemGUI
        
        print("  🎨 创建GUI界面...")
        app = TradingSystemGUI()
        
        print("  🚀 启动GUI...")
        app.run()
        
    except ImportError as e:
        print(f"❌ GUI模块导入失败: {e}")
        print("可能缺少tkinter或matplotlib依赖")
        return False
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        traceback.print_exc()
        return False

def run_backtest_demo():
    """运行回测演示"""
    print("\n📈 运行回测演示...")
    
    try:
        from core.backtest_engine import BacktestEngine
        from strategies.adaptive_trend_strategy import AdaptiveTrendStrategy
        from core.data_manager import DataManager
        
        # 创建回测引擎
        backtest_engine = BacktestEngine(initial_capital=5000000)
        print("  🔧 回测引擎初始化完成")
        
        # 创建策略
        strategy = AdaptiveTrendStrategy()
        print("  📊 策略初始化完成")
        
        # 获取回测数据
        data_manager = DataManager()
        symbols = ['rb888.SHFE', 'cu888.SHFE']
        start_date = '2024-01-01'
        end_date = '2024-06-01'
        
        backtest_data = {}
        for symbol in symbols:
            data = data_manager.get_historical_data(symbol, start_date, end_date)
            if not data.empty:
                backtest_data[symbol] = data
                strategy.calculate_indicators(data, symbol)
                print(f"    ✅ {symbol}: {len(data)} 条数据")
        
        if backtest_data:
            print("  🚀 开始回测...")
            result = backtest_engine.run_backtest(strategy, backtest_data, start_date, end_date)
            
            print(f"\n  📊 回测结果:")
            print(f"    总收益率: {result.total_return:.2%}")
            print(f"    年化收益率: {result.annual_return:.2%}")
            print(f"    最大回撤: {result.max_drawdown:.2%}")
            print(f"    夏普比率: {result.sharpe_ratio:.3f}")
            print(f"    胜率: {result.win_rate:.1%}")
            print(f"    总交易次数: {result.total_trades}")
            
            print("✅ 回测演示完成")
        else:
            print("❌ 没有获取到有效的回测数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 回测演示失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("智能期货交易系统 v1.0")
    print("解决期货策略三大痛点的系统化解决方案\n")
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    # 测试基本功能
    if not test_basic_functionality():
        input("\n按回车键退出...")
        return
    
    # 显示菜单
    while True:
        print("\n" + "="*60)
        print("请选择运行模式:")
        print("1. 运行系统演示")
        print("2. 启动GUI界面")
        print("3. 运行回测演示")
        print("4. 查看合约信息")
        print("0. 退出")
        print("="*60)
        
        try:
            choice = input("请输入选择 (0-4): ").strip()
            
            if choice == "0":
                print("👋 感谢使用智能期货交易系统！")
                break
            elif choice == "1":
                run_demo()
            elif choice == "2":
                start_gui()
            elif choice == "3":
                run_backtest_demo()
            elif choice == "4":
                from contract_manager import get_contract_manager
                contract_manager = get_contract_manager()
                print(f"\n📋 合约信息汇总:")
                df = contract_manager.get_contract_summary()
                print(df.to_string(index=False))
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"❌ 运行出错: {e}")
            traceback.print_exc()

if __name__ == "__main__":
    main()
