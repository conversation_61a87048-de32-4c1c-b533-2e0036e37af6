#!/usr/bin/env python3
"""
智能期货交易系统启动脚本
System Startup Script

提供多种运行模式：
1. 演示模式 - 展示系统功能
2. 回测模式 - 历史数据回测
3. 实时模式 - 实时监控运行
4. 配置模式 - 系统配置管理
"""

import sys
import os
import json
import argparse
from datetime import datetime, timedelta
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_system import IntelligentTradingSystem
from example_usage import (
    demo_instrument_selection,
    demo_parameter_optimization, 
    demo_risk_management,
    demo_strategy_backtest,
    demo_complete_system
)

def load_config(config_file: str = "config.json") -> dict:
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"配置文件 {config_file} 不存在，使用默认配置")
        return {}
    except json.JSONDecodeError as e:
        print(f"配置文件格式错误: {e}")
        return {}

def run_demo_mode():
    """运行演示模式"""
    print("🎯 智能期货交易系统 - 演示模式")
    print("="*60)
    
    demos = [
        ("品种筛选演示", demo_instrument_selection),
        ("参数优化演示", demo_parameter_optimization),
        ("风险管理演示", demo_risk_management),
        ("策略回测演示", demo_strategy_backtest),
        ("完整系统演示", demo_complete_system)
    ]
    
    for i, (name, demo_func) in enumerate(demos, 1):
        print(f"\n{'='*20} {i}. {name} {'='*20}")
        try:
            demo_func()
            print(f"✅ {name} 完成")
        except Exception as e:
            print(f"❌ {name} 失败: {e}")
        
        if i < len(demos):
            time.sleep(2)  # 演示间隔
    
    print(f"\n{'='*60}")
    print("🎉 所有演示完成！")

def run_backtest_mode(config: dict, start_date: str, end_date: str):
    """运行回测模式"""
    print("📈 智能期货交易系统 - 回测模式")
    print("="*60)
    
    # 创建系统
    account_value = config.get('system_config', {}).get('account_value', 5000000)
    system = IntelligentTradingSystem(account_value=account_value, config=config.get('system_config', {}))
    
    print(f"账户净值: {account_value:,.0f}")
    print(f"回测期间: {start_date} 到 {end_date}")
    
    try:
        # 执行品种筛选
        print("\n🔍 执行品种筛选...")
        system._run_instrument_selection()
        print(f"选中品种: {system.selected_instruments}")
        
        # 执行参数优化
        if system.selected_instruments:
            print("\n⚙️ 执行参数优化...")
            system._run_parameter_optimization()
            print(f"优化完成: {len(system.optimized_parameters)} 个品种")
        
        # 运行回测
        print(f"\n📊 运行回测...")
        backtest_result = system.run_backtest(start_date, end_date)
        
        if 'result' in backtest_result:
            result = backtest_result['result']
            summary = backtest_result['summary']
            
            print(f"\n{'='*40}")
            print("回测结果摘要")
            print(f"{'='*40}")
            
            print(f"总收益率: {result.total_return:.2%}")
            print(f"年化收益率: {result.annual_return:.2%}")
            print(f"最大回撤: {result.max_drawdown:.2%}")
            print(f"夏普比率: {result.sharpe_ratio:.3f}")
            print(f"Calmar比率: {result.calmar_ratio:.3f}")
            print(f"胜率: {result.win_rate:.1%}")
            print(f"盈亏比: {result.profit_factor:.2f}")
            print(f"总交易次数: {result.total_trades}")
            
            # 保存结果
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            result_file = f"results/backtest_result_{timestamp}.json"
            
            os.makedirs("results", exist_ok=True)
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n📄 回测结果已保存到: {result_file}")
        else:
            print("❌ 回测失败")
            
    except Exception as e:
        print(f"❌ 回测过程中出现错误: {e}")
    
    finally:
        # 生成系统报告
        system.save_system_report()

def run_realtime_mode(config: dict, duration_hours: int = 24):
    """运行实时模式"""
    print("🚀 智能期货交易系统 - 实时模式")
    print("="*60)
    
    # 创建系统
    account_value = config.get('system_config', {}).get('account_value', 5000000)
    system = IntelligentTradingSystem(account_value=account_value, config=config.get('system_config', {}))
    
    print(f"账户净值: {account_value:,.0f}")
    print(f"运行时长: {duration_hours} 小时")
    print(f"监控间隔: {config.get('system_config', {}).get('monitor_interval', 60)} 秒")
    
    try:
        # 启动系统
        system.start_system()
        print("✅ 系统启动成功")
        
        # 运行指定时长
        start_time = time.time()
        end_time = start_time + duration_hours * 3600
        
        print(f"\n系统运行中... (按 Ctrl+C 提前停止)")
        
        while time.time() < end_time:
            try:
                # 显示系统状态
                health_score = system.get_health_score()
                uptime = (time.time() - start_time) / 3600
                
                print(f"\r运行时间: {uptime:.1f}h | 健康评分: {health_score:.1f}/100 | "
                      f"选中品种: {len(system.selected_instruments)}", end="", flush=True)
                
                time.sleep(30)  # 30秒更新一次显示
                
            except KeyboardInterrupt:
                print(f"\n\n⚠️ 用户中断，正在停止系统...")
                break
        
        print(f"\n\n⏰ 运行时间到达，正在停止系统...")
        
    except Exception as e:
        print(f"❌ 实时运行过程中出现错误: {e}")
    
    finally:
        # 停止系统
        system.stop_system()
        
        # 保存最终报告
        system.save_system_report()
        print("✅ 系统已安全停止")

def run_config_mode(config_file: str):
    """运行配置模式"""
    print("⚙️ 智能期货交易系统 - 配置模式")
    print("="*60)
    
    config = load_config(config_file)
    
    print("当前配置:")
    print(json.dumps(config, indent=2, ensure_ascii=False))
    
    print(f"\n配置文件位置: {config_file}")
    print("可以编辑配置文件来调整系统参数")
    
    # 配置验证
    required_sections = ['system_config', 'risk_management', 'strategy_config']
    missing_sections = [section for section in required_sections if section not in config]
    
    if missing_sections:
        print(f"\n⚠️ 缺少配置节: {missing_sections}")
    else:
        print(f"\n✅ 配置文件格式正确")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="智能期货交易系统")
    parser.add_argument('--mode', choices=['demo', 'backtest', 'realtime', 'config'], 
                       default='demo', help='运行模式')
    parser.add_argument('--config', default='config.json', help='配置文件路径')
    parser.add_argument('--start-date', help='回测开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='回测结束日期 (YYYY-MM-DD)')
    parser.add_argument('--duration', type=int, default=24, help='实时模式运行时长（小时）')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建必要目录
    for dir_name in ['data', 'results', 'logs']:
        os.makedirs(dir_name, exist_ok=True)
    
    try:
        if args.mode == 'demo':
            run_demo_mode()
            
        elif args.mode == 'backtest':
            # 设置默认日期
            if not args.start_date:
                args.start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
            if not args.end_date:
                args.end_date = datetime.now().strftime('%Y-%m-%d')
            
            run_backtest_mode(config, args.start_date, args.end_date)
            
        elif args.mode == 'realtime':
            run_realtime_mode(config, args.duration)
            
        elif args.mode == 'config':
            run_config_mode(args.config)
            
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
