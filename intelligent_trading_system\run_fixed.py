#!/usr/bin/env python3
"""
智能期货交易系统修复版启动器
Fixed Intelligent Futures Trading System Launcher

解决所有导入和路径问题的完整启动脚本
"""

import sys
import os
import traceback
from datetime import datetime, timedelta

# 确保路径正确
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("🚀 智能期货交易系统 v1.0 (修复版)")
print("="*80)
print("解决期货策略三大痛点的系统化解决方案")
print("1. 品种选择困难 → 动态品种筛选引擎")
print("2. 参数调整迷茫 → 参数自适应优化模块")  
print("3. 净值停滞问题 → 资金智能管理系统")
print("="*80)

def check_environment():
    """检查运行环境"""
    print("🔧 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    print(f"  ✅ Python {sys.version.split()[0]}")
    
    # 检查必要的包
    required_packages = ['pandas', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} (缺失)")
    
    if missing_packages:
        print(f"\n⚠️  请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    # 检查可选包
    optional_packages = {'matplotlib': 'GUI图表', 'tkinter': 'GUI界面'}
    for package, desc in optional_packages.items():
        try:
            __import__(package)
            print(f"  ✅ {package} ({desc})")
        except ImportError:
            print(f"  ⚠️  {package} (可选，用于{desc})")
    
    print("✅ 环境检查完成")
    return True

def py_core_modules():
    """测试核心模块"""
    print("\n🔧 测试核心模块...")
    
    modules_to_test = [
        ('contract_manager', 'get_contract_manager'),
        ('core.data_manager', 'DataManager'),
        ('core.instrument_selector', 'InstrumentSelector'),
        ('core.risk_manager', 'RiskManager'),
        ('strategies.adaptive_trend_strategy', 'AdaptiveTrendStrategy')
    ]
    
    success_count = 0
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"  ✅ {module_name}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {module_name}: {e}")
    
    print(f"模块测试: {success_count}/{len(modules_to_test)} 成功")
    return success_count >= len(modules_to_test) - 1  # 允许1个模块失败

def run_simple_demo():
    """运行简单演示"""
    print("\n🎯 简单功能演示")
    print("-" * 50)
    
    try:
        # 测试合约管理器
        print("📋 测试合约管理器...")
        from contract_manager import get_contract_manager
        
        contract_manager = get_contract_manager()
        contracts = contract_manager.get_all_contracts()
        print(f"  ✅ 加载了 {len(contracts)} 个期货品种")
        
        # 显示部分合约信息
        print("  📊 部分合约信息:")
        for symbol in contracts[:5]:
            info = contract_manager.get_contract_info(symbol)
            print(f"    {symbol:<15} {info['name']:<8} 乘数:{info['size']:>4}")
        
        # 测试数据管理器
        print("\n📊 测试数据管理器...")
        from core.data_manager import DataManager
        
        data_manager = DataManager()
        
        # 获取测试数据
        symbol = 'rb888.SHFE'
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
        print(f"  ✅ 获取 {symbol} 数据: {len(data)} 条")
        
        # 测试品种筛选器
        print("\n🔍 测试品种筛选器...")
        from core.instrument_selector import InstrumentSelector
        
        selector = InstrumentSelector(account_value=5000000)
        
        # 获取多个品种数据
        test_symbols = ['rb888.SHFE', 'cu888.SHFE', 'au888.SHFE']
        market_data = {}
        
        for test_symbol in test_symbols:
            test_data = data_manager.get_historical_data(test_symbol, start_date, end_date, source='simulation')
            if not test_data.empty:
                market_data[test_symbol] = test_data
        
        if market_data:
            metrics_list = selector.select_instruments(market_data)
            selected = [m.symbol for m in metrics_list if m.is_selected]
            
            print(f"  ✅ 筛选完成: 从 {len(metrics_list)} 个品种中选出 {len(selected)} 个")
            print(f"  📋 选中品种: {', '.join(selected)}")
        
        # 测试策略加载器
        print("\n📈 测试策略加载器...")
        try:
            from strategy_loader import get_strategy_loader
            
            strategy_loader = get_strategy_loader()
            strategies = strategy_loader.get_strategy_list()
            
            print(f"  ✅ 加载策略: {len(strategies)} 个")
            for strategy in strategies[:3]:  # 显示前3个
                print(f"    - {strategy['name']}: {strategy['display_name']} ({strategy['type']})")
                
        except Exception as e:
            print(f"  ⚠️  策略加载器测试失败: {e}")
        
        print("\n✅ 简单演示完成！")
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        traceback.print_exc()
        return False

def start_gui_safe():
    """安全启动GUI"""
    print("\n🖥️  启动GUI界面...")
    
    try:
        # 检查tkinter
        try:
            import tkinter
            print("  ✅ tkinter可用")
        except ImportError:
            print("  ❌ tkinter不可用，无法启动GUI")
            return False
        
        # 检查matplotlib
        try:
            import matplotlib
            print("  ✅ matplotlib可用")
        except ImportError:
            print("  ⚠️  matplotlib不可用，图表功能将受限")
        
        # 导入GUI
        from gui_interface import TradingSystemGUI
        
        print("  🎨 创建图形界面...")
        app = TradingSystemGUI()
        
        print("  🚀 启动GUI界面...")
        print("  💡 GUI界面已打开，请在新窗口中操作")
        app.run()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

def run_backtest_demo():
    """运行回测演示"""
    print("\n📈 回测演示")
    print("-" * 50)
    
    try:
        from core.backtest_engine import BacktestEngine
        from strategies.adaptive_trend_strategy import AdaptiveTrendStrategy
        from core.data_manager import DataManager
        
        print("🔧 初始化回测组件...")
        backtest_engine = BacktestEngine(initial_capital=5000000)
        strategy = AdaptiveTrendStrategy()
        data_manager = DataManager()
        
        # 获取回测数据
        symbols = ['rb888.SHFE']
        start_date = '2024-01-01'
        end_date = '2024-03-01'
        
        print(f"📊 获取回测数据: {start_date} 到 {end_date}")
        backtest_data = {}
        
        for symbol in symbols:
            data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
            if not data.empty:
                backtest_data[symbol] = data
                strategy.calculate_indicators(data, symbol)
                print(f"  ✅ {symbol}: {len(data)} 条数据")
        
        if backtest_data:
            print("\n🚀 开始回测...")
            result = backtest_engine.run_backtest(strategy, backtest_data, start_date, end_date)
            
            print(f"\n📊 回测结果:")
            print(f"  初始资金: {result.initial_capital:>12,.0f} 元")
            print(f"  最终资金: {result.final_capital:>12,.0f} 元")
            print(f"  总收益率: {result.total_return:>12.2%}")
            print(f"  最大回撤: {result.max_drawdown:>12.2%}")
            print(f"  夏普比率: {result.sharpe_ratio:>12.3f}")
            print(f"  交易次数: {result.total_trades:>12d}")
        
        print("\n✅ 回测演示完成！")
        return True
        
    except Exception as e:
        print(f"❌ 回测演示失败: {e}")
        traceback.print_exc()
        return False

def show_system_info():
    """显示系统信息"""
    print(f"\n📖 系统信息:")
    print("="*60)
    print("🎯 核心功能:")
    print("  1. 品种动态筛选 - 自动识别最适合的交易品种")
    print("  2. 参数自适应优化 - 根据市场变化调整策略参数")
    print("  3. 资金智能管理 - 实时监控和控制交易风险")
    print("  4. 完整监控体系 - 全方位系统状态监控")
    print("  5. vnpy策略集成 - 支持加载vnpy的CTA策略")
    print("\n💡 解决方案:")
    print("  ❌ 品种选择困难 → ✅ 系统自动推荐最优品种")
    print("  ❌ 参数调整迷茫 → ✅ 自动优化找到最佳参数")
    print("  ❌ 净值停滞问题 → ✅ 智能风控提升稳定性")
    print("\n🚀 使用建议:")
    print("  1. 先运行简单演示了解功能")
    print("  2. 使用GUI界面进行完整操作")
    print("  3. 根据实际需求调整参数")
    print("  4. 从小资金开始逐步扩大规模")

def main():
    """主函数"""
    # 检查环境
    if not check_environment():
        input("\n按回车键退出...")
        return
    
    # 测试核心模块
    if not py_core_modules():
        print("\n⚠️  部分模块测试失败，但系统仍可运行")
    
    # 主菜单循环
    while True:
        print(f"\n{'='*60}")
        print("🎯 请选择功能:")
        print("1. 简单功能演示")
        print("2. 回测演示")
        print("3. 启动GUI界面")
        print("4. 系统信息")
        print("0. 退出")
        print("="*60)
        
        try:
            choice = input("请输入选择 (0-4): ").strip()
            
            if choice == "0":
                print("\n👋 感谢使用智能期货交易系统！")
                break
                
            elif choice == "1":
                run_simple_demo()
                
            elif choice == "2":
                run_backtest_demo()
                
            elif choice == "3":
                start_gui_safe()
                
            elif choice == "4":
                show_system_info()
                
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"❌ 运行出错: {e}")
            traceback.print_exc()

if __name__ == "__main__":
    main()
