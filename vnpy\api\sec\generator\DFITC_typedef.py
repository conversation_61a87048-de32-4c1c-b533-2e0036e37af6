DFITCSECBusinessTypeType = "int"
DFITCSECRequestIDType = "int"
DFITCSECAccountIDType = "string"
DFITCSECPasswordType = "string"
DFITCSECAuthenticCodeType = "string"
DFITCSECAppID = "string"
DFITCSECErrorIDType = "int"
DFITCSECSessionIDType = "int"
DFITCSECLocalOrderIDType = "int"
DFITCSECSpdOrderIDType = "int"
DFITCSECMessageType = "string"
DFITCSECFrontIDType = "int"
DFITCSECTimeType = "string"
DFITCSECDateType = "int"
DFITCSECWeekType = "int"
DFITCSECMillisecondType = "int"
DFITCSECPasswordTypeType = "int"
DFITCSECPriceType = "double"
DFITCSECFundsType = "double"
DFITCSECPositionSourceType = "int"
DFITCSECEntrustDirectionType = "int"
DFITCSECQuantityType = "int"
DFITCSECLargeQuantityType = "int"
DFITCSECLargeQuantityType = "int"
DFITCSECOrderTypeType = "int"
DFITCSECAccountType = "int"
DFITCSECEntrustBatchIDType = "int"
DFITCSECSeatIDType = "string"
DFITCSECExchangeIDType = "string"
DFITCSECSecurityIDType = "string"
DFITCSECEntrustQryFlagType = "int"
DFITCSECWithdrawFlagType = "string"
DFITCSECIncQryIndexType = "string"
DFITCSECTradeQryFlagType = "int"
DFITCSECPositionQryFlagType = "int"
DFITCSECFundsQryFlagType = "int"
DFITCSECSystemQryFlagType = "int"
DFITCSECCurrencyType = "string"
DFITCSECFundsTransferFlagType = "int"
DFITCSECEntrustBatchOrderDetailType = "string"
DFITCSECOrderIDRangeType = "string"
DFITCSECFundsFreezeTypeType = "int"
DFITCSECTransFundsFreezeTypeType = "int"
DFITCSECSerialIDType = "int"
DFITCSECStockFreezeTypeType = "int"
DFITCSECShareholderIDType = "string"
DFITCSECEntrustTypeType = "int"
DFITCSECSecurityNameType = "string"
DFITCSECDeclareResultType = "int"
DFITCSECBatchDeclareResultType = "int"
DFITCSECDeclareOrderIDType = "string"
DFITCSECIPAddressType = "string"
DFITCSECMacAddressType = "string"
DFITCSECTradeIDType = "string"
DFITCSECSecurityTypeType = "string"
DFITCSECAccountStatusType = "int"
DFITCSECTradeStatusType = "int"
DFITCSECBranchIDType = "string"
DFITCSECPhoneIDType = "string"
DFITCSECAccountNameType = "string"
DFITCSECAccountIdentityIDType = "string"
DFITCSECAccountIdentityTypeType = "int"
DFITCSBranchTypeType = "int"
DFITCSECPasswdSyncFlagType = "int"
DFITCSECShareholderSpecPropType = "int"
DFITCSECTradePermissionsType = "int"
DFITCSECShareholderStatusType = "int"
DFITCSECMainAccountFlagType = "int"
DFITCSECShareholderCtlPropType = "int"
DFITCSECOrderRangeID = "string"
DFITCSECBidTradeFlagType = "int"
DFITCSECTradeUnitType = "int"
DFITCSECBusinessLimitType = "int"
DFITCSECSubAccountIDType = "string"
DFITCSECOpenCloseFlagType = "int"
DFITCSECCoveredFlagType = "int"
DFITCSECOrderExpiryDateType = "int"
DFITCSECOrderCategoryType = "int"
DFITCSECQuoteIDType = "int"
DFITCSECTDevIDType = "string"
DFITCSECTDevDecInfoType = "string"
DFITCSECOptionTypeType = "int"
DFITCSECContractObjectTypeType = "int"
DFITCSECContractUnitType = "int"
DFITCSECContractIDType = "string"
DFITCSECTradingPhaseCodeType = "string"
DFITCSECAccountTypeType = "int"
DFITCSECAccountPropType = "int"
DFITCSECAccountNodeIDType = "int"
DFITCSECCheckUpLimitFlagType = "int"
DFITCSECContractNameType = "string"
DFITCSECExecuteTypeType = "int"
DFITCSECDeliveryTypeType = "int"
DFITCSECOpenLimitsType = "int"
DFITCSECStockTradeFlagType = "int"
DFITCSECApproachExpireFlagType = "int"
DFITCSECTempAdjuestFlagType = "int"
DFITCSECStockListFlagType = "string"
DFITCSECContractObjectStatusType = "int"
DFITCSECExchangeNameType = "string"
DFITCSECNightTradingFlagType = "int"
DFITCSECTradingDayFlagType = "int"
DFITCSECLvelType = "int"
DFITCSECReferenceTypeType = "int"
DFITCSECCalcTypeType = "int"
DFITCSECContractNOType = "string"
DFITCSECCrdtContractQryFlagType = "int"
DFITCSECCrdtContractStatusType = "int"
DFITCSECPositionNOType = "int"
DFITCSECClearFlagType = "int"
DFITCSECShareholderTypeType = "int"
DFITCSECHKEntrustLimitType = "int"
DFITCSECHKOrderTypeType = "int"
DFITCSECOrderConfirmFlagType = "int"
DFITCSECExchangeGroupTypeType = "int"
DFITCSECGroupCodeType = "string"
DFITCSECContractAdjustRemindType = "int"
DFITCSECContraceExpireRemindType = "int"
DFITCSESecurityOptionIndexType = "string"
DFITCSECOrderTypeLimitType = "string"
DFITCSECMiniPriceChangeType = "double"
DFITCSECMDCompressFalgType = "int"
DFITCSECCollectInterfaceType = "int"
DFITCSECConnectType = "int"
DFITCSECSystemInfo = "string"
DFITCSECSystemInfoIntegrity = "string"
DFITCSECSecretkeyVer = "string"
DFITCSECAppID = "string"
DFITCSECExceptionFlag = "int"
DFITCSECClientPublicIP = "string"
DFITCSECClientPublicPort = "int"
DFITCSECClientLoginTime = "string"
