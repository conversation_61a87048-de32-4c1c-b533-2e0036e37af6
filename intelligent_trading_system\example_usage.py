"""
智能期货交易系统使用示例
Example Usage of Intelligent Futures Trading System

演示如何使用系统的各个功能模块
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from main_system import IntelligentTradingSystem
    from core.data_manager import DataManager
    from core.instrument_selector import InstrumentSelector
    from core.parameter_optimizer import ParameterOptimizer
    from core.risk_manager import RiskManager
    from strategies.adaptive_trend_strategy import AdaptiveTrendStrategy
    from contract_manager import get_contract_manager
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"模块导入失败: {e}")
    MODULES_AVAILABLE = False

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

def demo_instrument_selection():
    """演示品种筛选功能"""
    print("\n" + "="*60)
    print("📊 品种筛选演示")
    print("="*60)
    
    # 创建品种筛选器
    selector = InstrumentSelector(account_value=5000000)
    
    # 创建数据管理器
    data_manager = DataManager()
    
    # 获取测试数据
    symbols = ['rb888.SHFE', 'cu888.SHFE', 'au888.SHFE', 'TA888.CZCE', 'm888.DCE']
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
    
    market_data = {}
    for symbol in symbols:
        data = data_manager.get_historical_data(symbol, start_date, end_date)
        if not data.empty:
            market_data[symbol] = data
    
    # 执行筛选
    metrics_list = selector.select_instruments(market_data)
    
    # 显示结果
    print(f"评估品种数: {len(metrics_list)}")
    print(f"选中品种: {[m.symbol for m in metrics_list if m.is_selected]}")
    
    print("\n品种评估详情:")
    for metrics in metrics_list:
        status = "✅ 选中" if metrics.is_selected else "❌ 未选中"
        print(f"{metrics.symbol:12} {status} - 动量:{metrics.momentum_score:6.2f} "
              f"胜率:{metrics.strategy_winrate:5.1%} 综合:{metrics.overall_score:5.3f}")
    
    # 生成报告
    report = selector.get_selection_report()
    print(f"\n筛选报告已生成，选中 {report['selected_count']} 个品种")

def demo_parameter_optimization():
    """演示参数优化功能"""
    print("\n" + "="*60)
    print("⚙️ 参数优化演示")
    print("="*60)
    
    # 创建参数优化器
    optimizer = ParameterOptimizer()
    
    # 创建数据管理器
    data_manager = DataManager()
    
    # 获取测试数据
    symbol = 'rb888.SHFE'
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
    
    data = data_manager.get_historical_data(symbol, start_date, end_date)
    
    if not data.empty:
        print(f"正在优化 {symbol}，数据长度: {len(data)}")
        
        # 执行优化
        result = optimizer.optimize_parameters(symbol, data, max_workers=2)
        
        # 显示结果
        print(f"\n优化结果:")
        print(f"品种: {result.symbol}")
        print(f"稳定性评分: {result.stability_score:.3f}")
        print(f"是否稳定: {'是' if result.is_stable else '否'}")
        print(f"优化时间: {result.optimization_time:.2f}秒")
        
        print(f"\n最佳参数:")
        for param, value in result.best_params.items():
            print(f"  {param}: {value}")
        
        print(f"\n性能指标:")
        for metric, value in result.performance_metrics.items():
            print(f"  {metric}: {value:.4f}")
    else:
        print(f"无法获取 {symbol} 的数据")

def demo_risk_management():
    """演示风险管理功能"""
    print("\n" + "="*60)
    print("🛡️ 风险管理演示")
    print("="*60)
    
    # 创建风险管理器
    contract_info = {
        'rb888.SHFE': {'multiplier': 10, 'tick_size': 1, 'margin_rate': 0.08},
        'cu888.SHFE': {'multiplier': 5, 'tick_size': 10, 'margin_rate': 0.07},
        'au888.SHFE': {'multiplier': 1000, 'tick_size': 0.02, 'margin_rate': 0.06}
    }
    
    risk_manager = RiskManager(account_value=5000000, contract_info=contract_info)
    
    # 计算建议头寸
    symbols_atr = {
        'rb888.SHFE': 80.0,
        'cu888.SHFE': 1200.0,
        'au888.SHFE': 8.0
    }
    
    print("建议头寸计算:")
    for symbol, atr in symbols_atr.items():
        lots = risk_manager.calculate_position_size(symbol, atr)
        risk_amount = atr * contract_info[symbol]['multiplier'] * 2.5 * lots
        print(f"{symbol:12} ATR:{atr:6.1f} 建议手数:{lots:2d} 风险金额:{risk_amount:8.0f}")
    
    # 模拟持仓风险检查
    from core.risk_manager import PositionInfo
    
    positions = {
        'rb888.SHFE': PositionInfo(
            symbol='rb888.SHFE',
            current_lots=5,
            suggested_lots=6,
            risk_amount=100000,
            risk_percentage=0.02,
            atr_value=80.0,
            contract_value=200000
        ),
        'cu888.SHFE': PositionInfo(
            symbol='cu888.SHFE',
            current_lots=3,
            suggested_lots=3,
            risk_amount=90000,
            risk_percentage=0.018,
            atr_value=1200.0,
            contract_value=350000
        )
    }
    
    # 计算风险指标
    risk_metrics = risk_manager.calculate_portfolio_risk(positions)
    print(f"\n组合风险指标:")
    print(f"总风险暴露: {risk_metrics.total_risk_exposure:,.0f}")
    print(f"组合风险比例: {risk_metrics.portfolio_risk_percentage:.1%}")
    print(f"最大单品种风险: {risk_metrics.max_single_risk:.1%}")
    
    # 检查风险限制
    warnings = risk_manager.check_risk_limits(positions)
    print(f"\n风险检查结果:")
    for severity, warning_list in warnings.items():
        if warning_list:
            print(f"{severity.upper()}: {len(warning_list)} 条警告")
            for warning in warning_list:
                print(f"  - {warning}")

def demo_strategy_backtest():
    """演示策略回测功能"""
    print("\n" + "="*60)
    print("📈 策略回测演示")
    print("="*60)
    
    # 创建策略
    strategy = AdaptiveTrendStrategy()
    
    # 创建回测引擎
    from core.backtest_engine import BacktestEngine
    backtest_engine = BacktestEngine(initial_capital=5000000)
    
    # 创建数据管理器
    data_manager = DataManager()
    
    # 获取回测数据
    symbols = ['rb888.SHFE', 'cu888.SHFE']
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    
    backtest_data = {}
    for symbol in symbols:
        data = data_manager.get_historical_data(symbol, start_date, end_date)
        if not data.empty:
            backtest_data[symbol] = data
            # 计算技术指标
            strategy.calculate_indicators(data, symbol)
    
    if backtest_data:
        print(f"回测品种: {list(backtest_data.keys())}")
        print(f"回测期间: {start_date} 到 {end_date}")
        
        # 运行回测
        result = backtest_engine.run_backtest(strategy, backtest_data, start_date, end_date)
        
        # 显示结果
        print(f"\n回测结果:")
        print(f"总收益率: {result.total_return:.2%}")
        print(f"年化收益率: {result.annual_return:.2%}")
        print(f"最大回撤: {result.max_drawdown:.2%}")
        print(f"夏普比率: {result.sharpe_ratio:.3f}")
        print(f"Calmar比率: {result.calmar_ratio:.3f}")
        print(f"胜率: {result.win_rate:.1%}")
        print(f"盈亏比: {result.profit_factor:.2f}")
        print(f"总交易次数: {result.total_trades}")
        
        if result.trades:
            print(f"\n最近5笔交易:")
            for i, trade in enumerate(result.trades[-5:], 1):
                direction = "多" if trade.direction == 1 else "空"
                print(f"{i}. {trade.symbol} {direction} {trade.quantity}手 "
                      f"盈亏:{trade.pnl:8.2f} 时间:{trade.exit_time.strftime('%m-%d')}")
    else:
        print("无法获取回测数据")

def demo_complete_system():
    """演示完整系统功能"""
    print("\n" + "="*60)
    print("🚀 完整系统演示")
    print("="*60)
    
    # 创建系统
    system = IntelligentTradingSystem(account_value=5000000)
    
    print("系统初始化完成")
    print(f"账户净值: {system.account_value:,.0f}")
    print(f"系统配置: {system.config}")
    
    # 手动执行一次完整流程
    print("\n执行品种筛选...")
    system._run_instrument_selection()
    print(f"选中品种: {system.selected_instruments}")
    
    if system.selected_instruments:
        print("\n执行参数优化...")
        system._run_parameter_optimization()
        print(f"优化参数: {len(system.optimized_parameters)} 个品种")
        
        for symbol, params in system.optimized_parameters.items():
            print(f"  {symbol}: MA周期={params.get('ma_period', 'N/A')}")
    
    print("\n执行风险监控...")
    system._run_risk_monitoring()
    
    # 运行回测
    print("\n运行回测...")
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
    
    backtest_result = system.run_backtest(start_date, end_date)
    
    if 'result' in backtest_result:
        result = backtest_result['result']
        print(f"回测收益率: {result.total_return:.2%}")
        print(f"夏普比率: {result.sharpe_ratio:.3f}")
    
    # 生成系统报告
    print("\n生成系统报告...")
    report = system.get_system_report()
    
    print(f"系统健康评分: {system.get_health_score():.1f}/100")
    
    # 保存报告
    system.save_system_report()
    print("系统报告已保存")

def main():
    """主演示函数"""
    print("🎯 智能期货交易系统演示")
    print("解决期货策略三大痛点的系统化解决方案")
    
    demos = [
        ("1. 品种筛选演示", demo_instrument_selection),
        ("2. 参数优化演示", demo_parameter_optimization),
        ("3. 风险管理演示", demo_risk_management),
        ("4. 策略回测演示", demo_strategy_backtest),
        ("5. 完整系统演示", demo_complete_system)
    ]
    
    print("\n可用演示:")
    for name, _ in demos:
        print(f"  {name}")
    
    try:
        choice = input("\n请选择演示 (1-5, 或按回车运行所有演示): ").strip()
        
        if choice == "":
            # 运行所有演示
            for name, demo_func in demos:
                print(f"\n{'='*20} {name} {'='*20}")
                demo_func()
                time.sleep(1)  # 短暂暂停
        elif choice.isdigit() and 1 <= int(choice) <= 5:
            # 运行指定演示
            name, demo_func = demos[int(choice) - 1]
            print(f"\n{'='*20} {name} {'='*20}")
            demo_func()
        else:
            print("无效选择")
            return
        
        print("\n" + "="*60)
        print("🎉 演示完成！")
        print("="*60)
        
        print("\n💡 系统特点:")
        print("✅ 动态品种筛选 - 自动识别最适合的交易品种")
        print("✅ 参数自适应优化 - 根据市场变化调整策略参数")
        print("✅ 智能风险管理 - 实时监控和控制交易风险")
        print("✅ 完整监控体系 - 全方位系统状态监控")
        print("✅ 模块化设计 - 易于扩展和定制")
        
        print("\n📈 预期效果:")
        print("🎯 解决品种选择困难 - 系统自动推荐最优品种")
        print("🎯 解决参数调整迷茫 - 自动优化找到最佳参数")
        print("🎯 解决净值停滞问题 - 智能风控提升稳定性")
        
    except KeyboardInterrupt:
        print("\n\n用户中断演示")
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")

if __name__ == "__main__":
    main()
