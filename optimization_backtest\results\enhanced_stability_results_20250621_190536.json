{"TA888.CZCE": {"symbol": "TA888.CZCE", "best_performance": 3.041762817458628, "stability_score": 0.9629420066271293, "best_params": {"k_1": 9, "k_3": 14, "k_5": 16, "sl_multiplier": 7.0, "macd_boll_count_fz": 0.22, "dk_fz": 1.0, "ping_zy": 0, "zy": 100, "AF": 0.002, "AF_max": 0.2, "trailing_start_ratio": 0.6000000000000001, "daily_loss_limit": 2108, "k_15": 15, "k_30": 30, "atr_window": 30, "donchian_period": 20, "lots": 1, "use_trailing_stop": 1, "contract_multiplier": 5.0}, "stability_metrics": {"performance_stability": 0.9999999999999998, "parameter_sensitivity": 0.6911833885594106, "temporal_consistency": 1.0, "robustness_score": 0.8970611295198035, "overall_stability": 0.907355016567823}, "parameter_importance": {"k_1": 0.00696199107915163, "k_3": 0.01817573979496956, "k_5": 0.012704338878393173, "sl_multiplier": 0.07539016008377075, "macd_boll_count_fz": 0.027380146086215973, "dk_fz": 0.08858106285333633, "ping_zy": 0.010250742547214031, "zy": 0.32492345571517944, "AF": 0.4039309024810791, "AF_max": 0.0111367953941226, "trailing_start_ratio": 0.007273842580616474, "daily_loss_limit": 0.013290897011756897}, "optimization_time": 37.07829213142395, "constraint_violations": [], "contract_info": {"size": 5.0, "rate": 0.00012190166598943519, "pricetick": 2.0}}, "bu888.SHFE": {"symbol": "bu888.SHFE", "best_performance": 3.8918072906643912, "stability_score": 0.9620905953693035, "best_params": {"k_1": 7, "k_3": 19, "k_5": 24, "sl_multiplier": 7.0, "macd_boll_count_fz": 0.3, "dk_fz": 0.9, "ping_zy": 8, "zy": 53, "AF": 0.002, "AF_max": 0.12, "trailing_start_ratio": 0.6000000000000001, "daily_loss_limit": 2826, "k_15": 15, "k_30": 30, "atr_window": 30, "donchian_period": 20, "lots": 1, "use_trailing_stop": 1, "contract_multiplier": 10.0}, "stability_metrics": {"performance_stability": 0.9999999999999998, "parameter_sensitivity": 0.6840882947441971, "temporal_consistency": 0.9999999999999998, "robustness_score": 0.8946960982480655, "overall_stability": 0.9052264884232589}, "parameter_importance": {"k_1": 0.006554738152772188, "k_3": 0.17458170652389526, "k_5": 0.0027383840642869473, "sl_multiplier": 0.2674843668937683, "macd_boll_count_fz": 0.010824393481016159, "dk_fz": 0.0036643738858401775, "ping_zy": 0.0055122519843280315, "zy": 0.17899027466773987, "AF": 0.332004576921463, "AF_max": 0.012728936038911343, "trailing_start_ratio": 0.002930068876594305, "daily_loss_limit": 0.0019859361927956343}, "optimization_time": 41.259907245635986, "constraint_violations": [], "contract_info": {"size": 10.0, "rate": 5e-05, "pricetick": 1.0}}, "rb888.SHFE": {"symbol": "rb888.SHFE", "best_performance": 3.3493510269075033, "stability_score": 0.9606975459430145, "best_params": {"k_1": 4, "k_3": 18, "k_5": 30, "sl_multiplier": 8.0, "macd_boll_count_fz": 0.08, "dk_fz": 0.9, "ping_zy": 29, "zy": 88, "AF": 0.0012000000000000001, "AF_max": 0.0, "trailing_start_ratio": 0.2, "daily_loss_limit": 2843, "k_15": 15, "k_30": 30, "atr_window": 30, "donchian_period": 20, "lots": 1, "use_trailing_stop": 1, "contract_multiplier": 10.0}, "stability_metrics": {"performance_stability": 1.0, "parameter_sensitivity": 0.6724795495251213, "temporal_consistency": 0.9999999999999998, "robustness_score": 0.8908265165083739, "overall_stability": 0.9017438648575363}, "parameter_importance": {"k_1": 0.010086636990308762, "k_3": 0.19599555432796478, "k_5": 0.0038506072014570236, "sl_multiplier": 0.28693515062332153, "macd_boll_count_fz": 0.017153482884168625, "dk_fz": 0.003638075664639473, "ping_zy": 0.0018617678433656693, "zy": 0.14587542414665222, "AF": 0.3025611937046051, "AF_max": 0.028209535405039787, "trailing_start_ratio": 0.0, "daily_loss_limit": 0.0038326908834278584}, "optimization_time": 53.47920823097229, "constraint_violations": ["AF_less_than_AF_max"], "contract_info": {"size": 10.0, "rate": 0.0001, "pricetick": 1.0}}, "PX888.CZCE": {"symbol": "PX888.CZCE", "best_performance": 3.009698314156136, "stability_score": 0.9625923654723285, "best_params": {"k_1": 7, "k_3": 8, "k_5": 23, "sl_multiplier": 7.0, "macd_boll_count_fz": 0.24, "dk_fz": 0.8, "ping_zy": 18, "zy": 100, "AF": 0.002, "AF_max": 0.06, "trailing_start_ratio": 0.4, "daily_loss_limit": 1955, "k_15": 15, "k_30": 30, "atr_window": 30, "donchian_period": 20, "lots": 1, "use_trailing_stop": 1, "contract_multiplier": 5.0}, "stability_metrics": {"performance_stability": 0.9999999999999998, "parameter_sensitivity": 0.6882697122694047, "temporal_consistency": 1.0, "robustness_score": 0.8960899040898015, "overall_stability": 0.9064809136808214}, "parameter_importance": {"k_1": 0.031129395589232445, "k_3": 0.03481656685471535, "k_5": 0.04930967465043068, "sl_multiplier": 0.08208473026752472, "macd_boll_count_fz": 0.05351059511303902, "dk_fz": 0.02527167834341526, "ping_zy": 0.04643773287534714, "zy": 0.2417738437652588, "AF": 0.3950785994529724, "AF_max": 0.02322322502732277, "trailing_start_ratio": 0.010427677072584629, "daily_loss_limit": 0.006936359219253063}, "optimization_time": 20.98768162727356, "constraint_violations": [], "contract_info": {"size": 5.0, "rate": 0.0001, "pricetick": 2.0}}, "SH888.CZCE": {"symbol": "SH888.CZCE", "best_performance": 4.7681051596180986, "stability_score": 0.9648810377135976, "best_params": {"k_1": 4, "k_3": 20, "k_5": 16, "sl_multiplier": 10.0, "macd_boll_count_fz": 0.26, "dk_fz": 1.0, "ping_zy": 28, "zy": 58, "AF": 0.002, "AF_max": 0.2, "trailing_start_ratio": 0.8, "daily_loss_limit": 2145, "k_15": 15, "k_30": 30, "atr_window": 30, "donchian_period": 20, "lots": 1, "use_trailing_stop": 1, "contract_multiplier": 30.0}, "stability_metrics": {"performance_stability": 0.9999999999999998, "parameter_sensitivity": 0.707341980946647, "temporal_consistency": 1.0, "robustness_score": 0.9024473269822156, "overall_stability": 0.912202594283994}, "parameter_importance": {"k_1": 0.009237592108547688, "k_3": 0.3112020194530487, "k_5": 0.003111360827460885, "sl_multiplier": 0.1823054701089859, "macd_boll_count_fz": 0.011909032240509987, "dk_fz": 0.002538106869906187, "ping_zy": 0.006023249588906765, "zy": 0.14575281739234924, "AF": 0.3112940192222595, "AF_max": 0.015383850783109665, "trailing_start_ratio": 0.0, "daily_loss_limit": 0.0012424810556694865}, "optimization_time": 13.832096338272095, "constraint_violations": ["k_periods_logical"], "contract_info": {"size": 30.0, "rate": 0.0001, "pricetick": 1.0}}}