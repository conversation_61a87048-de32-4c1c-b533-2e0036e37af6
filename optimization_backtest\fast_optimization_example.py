"""
快速回测优化示例
演示如何使用各种加速策略提高回测效率

使用方法:
python fast_optimization_example.py --mode fast      # 快速模式 (10-20倍加速)
python fast_optimization_example.py --mode balanced  # 平衡模式 (5-10倍加速)  
python fast_optimization_example.py --mode precise   # 精确模式 (2-3倍加速)
"""

import time
import argparse
from typing import Dict, List, Any
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_stability_optimization_example import (
    optimize_single_symbol_enhanced,
    run_batch_enhanced_optimization,
    analyze_batch_results,
    save_enhanced_results
)

try:
    from contract_info import ContractInfo
except ImportError:
    print("⚠️ 无法导入 contract_info 模块，将使用模拟数据")
    ContractInfo = None


def get_test_symbols(mode: str = "fast") -> List[str]:
    """根据模式获取测试品种"""
    if mode == "fast":
        # 快速模式：少量品种
        return ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE']
    elif mode == "balanced":
        # 平衡模式：中等数量品种
        return ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE', 'PX888.CZCE', 'SH888.CZCE']
    else:
        # 精确模式：更多品种
        return ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE', 'PX888.CZCE', 'SH888.CZCE', 
                'br888.SHFE', 'lu888.INE', 'MA888.CZCE']


def get_optimization_config(mode: str) -> Dict[str, Any]:
    """根据模式获取优化配置"""
    configs = {
        "fast": {
            "max_evaluations": 200,
            "max_workers": 8,
            "safe_mode": False,
            "use_fast_mode": True,
            "description": "极速模式 - 快速参数探索"
        },
        "balanced": {
            "max_evaluations": 500,
            "max_workers": 6,
            "safe_mode": False,
            "use_fast_mode": True,
            "description": "平衡模式 - 速度与精度平衡"
        },
        "precise": {
            "max_evaluations": 1000,
            "max_workers": 4,
            "safe_mode": True,
            "use_fast_mode": False,
            "description": "精确模式 - 高精度优化"
        }
    }
    return configs.get(mode, configs["balanced"])


def load_contract_info(symbols: List[str]) -> Dict[str, Dict[str, Any]]:
    """加载合约信息"""
    contract_info = {}
    
    if ContractInfo:
        try:
            loader = ContractInfo("期货全品种手续费保证金.xls")
            all_contracts = loader.get_888_contracts()
            
            for symbol in symbols:
                if symbol in all_contracts:
                    contract_info[symbol] = loader.get_contract_params(symbol)
                else:
                    print(f"⚠️ 未找到 {symbol} 的合约信息，使用默认值")
                    contract_info[symbol] = {
                        'size': 5,
                        'rate': 0.0002,
                        'pricetick': 1.0
                    }
        except Exception as e:
            print(f"⚠️ 加载合约信息失败: {e}，使用默认值")
            for symbol in symbols:
                contract_info[symbol] = {
                    'size': 5,
                    'rate': 0.0002,
                    'pricetick': 1.0
                }
    else:
        # 使用默认合约信息
        for symbol in symbols:
            contract_info[symbol] = {
                'size': 5,
                'rate': 0.0002,
                'pricetick': 1.0
            }
    
    return contract_info


def run_single_optimization_demo(mode: str):
    """运行单品种优化演示"""
    print(f"\n{'='*60}")
    print(f"单品种快速优化演示 - {mode.upper()} 模式")
    print(f"{'='*60}")
    
    config = get_optimization_config(mode)
    symbol = "TA888.CZCE"
    
    # 模拟合约信息
    contract_info = {
        'size': 5,
        'rate': 0.0002,
        'pricetick': 1.0
    }
    
    print(f"优化品种: {symbol}")
    print(f"配置: {config['description']}")
    print(f"评估次数: {config['max_evaluations']}")
    print(f"并行度: {config['max_workers']}")
    print(f"快速模式: {'启用' if config['use_fast_mode'] else '禁用'}")
    
    start_time = time.time()
    
    result = optimize_single_symbol_enhanced(
        symbol=symbol,
        contract_info=contract_info,
        strategy_class=None,
        max_evaluations=config['max_evaluations'],
        max_workers=config['max_workers'],
        safe_mode=config['safe_mode'],
        start_date="20230101",
        end_date="20240531",
        use_fast_mode=config['use_fast_mode']
    )
    
    total_time = time.time() - start_time
    
    print(f"\n{'='*40}")
    print(f"优化结果:")
    print(f"{'='*40}")
    
    if 'error' not in result:
        print(f"✅ 优化成功!")
        print(f"📈 最佳性能: {result['best_performance']:.4f}")
        print(f"🎯 稳定性评分: {result['stability_score']:.4f}")
        print(f"⏱️  优化耗时: {result['optimization_time']:.2f}秒")
        print(f"🚀 总耗时: {total_time:.2f}秒")
        
        # 显示最佳参数
        print(f"\n📋 最佳参数:")
        for param, value in result['best_params'].items():
            if isinstance(value, float):
                print(f"   {param}: {value:.4f}")
            else:
                print(f"   {param}: {value}")
    else:
        print(f"❌ 优化失败: {result['error']}")
    
    return result


def run_batch_optimization_demo(mode: str):
    """运行批量优化演示"""
    print(f"\n{'='*60}")
    print(f"批量快速优化演示 - {mode.upper()} 模式")
    print(f"{'='*60}")
    
    config = get_optimization_config(mode)
    symbols = get_test_symbols(mode)
    contract_info = load_contract_info(symbols)
    
    print(f"优化品种: {symbols}")
    print(f"配置: {config['description']}")
    print(f"每品种评估次数: {config['max_evaluations']}")
    print(f"并行度: {config['max_workers']}")
    print(f"快速模式: {'启用' if config['use_fast_mode'] else '禁用'}")
    
    start_time = time.time()
    
    # 修改批量优化函数调用，添加use_fast_mode参数
    batch_results = {}
    for symbol in symbols:
        print(f"\n正在优化: {symbol}")
        result = optimize_single_symbol_enhanced(
            symbol=symbol,
            contract_info=contract_info[symbol],
            strategy_class=None,
            max_evaluations=config['max_evaluations'],
            max_workers=config['max_workers'],
            safe_mode=config['safe_mode'],
            start_date="20230101",
            end_date="20240531",
            use_fast_mode=config['use_fast_mode']
        )
        batch_results[symbol] = result
    
    total_time = time.time() - start_time
    
    # 分析结果
    analysis = analyze_batch_results(batch_results)
    
    print(f"\n{'='*60}")
    print(f"批量优化结果分析:")
    print(f"{'='*60}")
    
    print(f"✅ 成功优化: {analysis['success_count']}/{len(symbols)} 个品种")
    print(f"📊 成功率: {analysis['success_rate']:.1%}")
    print(f"⏱️  总耗时: {total_time:.2f}秒")
    print(f"⚡ 平均速度: {len(symbols)/total_time:.2f} 品种/秒")
    
    if analysis['success_count'] > 0:
        perf_stats = analysis['performance_stats']
        print(f"\n📈 性能统计:")
        print(f"   平均性能: {perf_stats['mean']:.4f}")
        print(f"   最佳性能: {perf_stats['max']:.4f}")
        print(f"   性能标准差: {perf_stats['std']:.4f}")
        
        stability_stats = analysis['stability_stats']
        print(f"\n🎯 稳定性统计:")
        print(f"   平均稳定性: {stability_stats['mean']:.4f}")
        print(f"   最佳稳定性: {stability_stats['max']:.4f}")
        
        print(f"\n🏆 表现最佳品种:")
        for i, result in enumerate(analysis['top_performers'][:3], 1):
            print(f"   {i}. {result['symbol']}: 性能={result['best_performance']:.4f}")
    
    if analysis['failure_count'] > 0:
        print(f"\n❌ 失败品种: {analysis['failed_symbols']}")
    
    return batch_results, analysis


def performance_comparison_demo():
    """性能对比演示"""
    print(f"\n{'='*60}")
    print(f"性能对比演示")
    print(f"{'='*60}")
    
    symbol = "TA888.CZCE"
    contract_info = {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}
    
    modes = ["fast", "balanced", "precise"]
    results = {}
    
    for mode in modes:
        print(f"\n测试 {mode.upper()} 模式...")
        config = get_optimization_config(mode)
        
        start_time = time.time()
        result = optimize_single_symbol_enhanced(
            symbol=symbol,
            contract_info=contract_info,
            strategy_class=None,
            max_evaluations=config['max_evaluations'],
            max_workers=config['max_workers'],
            safe_mode=config['safe_mode'],
            start_date="20230101",
            end_date="20240531",
            use_fast_mode=config['use_fast_mode']
        )
        total_time = time.time() - start_time
        
        results[mode] = {
            'result': result,
            'total_time': total_time,
            'config': config
        }
        
        if 'error' not in result:
            print(f"   ✅ 性能: {result['best_performance']:.4f}, 耗时: {total_time:.2f}秒")
        else:
            print(f"   ❌ 失败: {result['error']}")
    
    # 性能对比表
    print(f"\n{'='*80}")
    print(f"性能对比表:")
    print(f"{'='*80}")
    print(f"{'模式':<10} {'评估次数':<10} {'性能':<12} {'耗时(秒)':<10} {'速度倍数':<10}")
    print(f"{'-'*80}")
    
    baseline_time = results.get('precise', {}).get('total_time', 1)
    
    for mode in modes:
        data = results[mode]
        result = data['result']
        total_time = data['total_time']
        config = data['config']
        
        if 'error' not in result:
            performance = result['best_performance']
            speed_multiplier = baseline_time / total_time
            print(f"{mode.upper():<10} {config['max_evaluations']:<10} {performance:<12.4f} {total_time:<10.2f} {speed_multiplier:<10.1f}x")
        else:
            print(f"{mode.upper():<10} {config['max_evaluations']:<10} {'失败':<12} {total_time:<10.2f} {'N/A':<10}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="快速回测优化演示")
    parser.add_argument("--mode", choices=["fast", "balanced", "precise"], 
                       default="balanced", help="优化模式")
    parser.add_argument("--demo", choices=["single", "batch", "comparison"], 
                       default="single", help="演示类型")
    
    args = parser.parse_args()
    
    print("🚀 快速回测优化演示")
    print("=" * 60)
    
    if args.demo == "single":
        run_single_optimization_demo(args.mode)
    elif args.demo == "batch":
        run_batch_optimization_demo(args.mode)
    elif args.demo == "comparison":
        performance_comparison_demo()
    
    print(f"\n{'='*60}")
    print("演示完成! 🎉")
    print("=" * 60)
    
    print("\n💡 使用建议:")
    print("1. 参数探索阶段使用 --mode fast")
    print("2. 日常优化使用 --mode balanced") 
    print("3. 最终验证使用 --mode precise")
    print("4. 批量测试使用 --demo batch")
    print("5. 性能对比使用 --demo comparison")


if __name__ == "__main__":
    main()
