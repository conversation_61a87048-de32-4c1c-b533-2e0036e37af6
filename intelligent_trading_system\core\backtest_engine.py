"""
回测引擎
Backtest Engine

功能：
1. 策略回测 - 支持多种策略的历史回测
2. 性能分析 - 计算详细的绩效指标
3. 风险分析 - 分析回测期间的风险指标
4. 结果可视化 - 生成回测报告和图表
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class Trade:
    """交易记录"""
    symbol: str
    entry_time: datetime
    exit_time: datetime
    direction: int  # 1: 多头, -1: 空头
    entry_price: float
    exit_price: float
    quantity: int
    pnl: float
    commission: float
    slippage: float

@dataclass
class Position:
    """持仓信息"""
    symbol: str
    direction: int
    quantity: int
    entry_price: float
    entry_time: datetime
    unrealized_pnl: float = 0.0

@dataclass
class BacktestResult:
    """回测结果"""
    start_date: str
    end_date: str
    initial_capital: float
    final_capital: float
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    calmar_ratio: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_pnl: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    trades: List[Trade]
    equity_curve: pd.Series
    daily_returns: pd.Series

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_capital: float = 1000000, 
                 commission_rate: float = 0.0002,
                 slippage_rate: float = 0.0001):
        """
        初始化回测引擎
        
        Args:
            initial_capital: 初始资金
            commission_rate: 手续费率
            slippage_rate: 滑点率
        """
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        
        # 回测状态
        self.current_capital = initial_capital
        self.positions = {}  # symbol -> Position
        self.trades = []
        self.equity_curve = []
        self.daily_pnl = []
        
        # 合约信息
        self.contract_info = self._load_contract_info()
        
    def _load_contract_info(self) -> Dict:
        """加载合约信息"""
        return {
            'rb888.SHFE': {'multiplier': 10, 'tick_size': 1, 'margin_rate': 0.08},
            'hc888.SHFE': {'multiplier': 10, 'tick_size': 1, 'margin_rate': 0.08},
            'i888.DCE': {'multiplier': 100, 'tick_size': 0.5, 'margin_rate': 0.08},
            'j888.DCE': {'multiplier': 100, 'tick_size': 0.5, 'margin_rate': 0.08},
            'cu888.SHFE': {'multiplier': 5, 'tick_size': 10, 'margin_rate': 0.07},
            'al888.SHFE': {'multiplier': 5, 'tick_size': 5, 'margin_rate': 0.07},
            'zn888.SHFE': {'multiplier': 5, 'tick_size': 5, 'margin_rate': 0.07},
            'au888.SHFE': {'multiplier': 1000, 'tick_size': 0.02, 'margin_rate': 0.06},
            'ag888.SHFE': {'multiplier': 15, 'tick_size': 1, 'margin_rate': 0.08},
            'sc888.INE': {'multiplier': 1000, 'tick_size': 0.1, 'margin_rate': 0.10},
            'TA888.CZCE': {'multiplier': 5, 'tick_size': 2, 'margin_rate': 0.06},
            'MA888.CZCE': {'multiplier': 10, 'tick_size': 1, 'margin_rate': 0.07},
            'm888.DCE': {'multiplier': 10, 'tick_size': 1, 'margin_rate': 0.05},
            'y888.DCE': {'multiplier': 10, 'tick_size': 2, 'margin_rate': 0.05}
        }
    
    def run_backtest(self, strategy, data: Dict[str, pd.DataFrame], 
                    start_date: str, end_date: str) -> BacktestResult:
        """
        运行回测
        
        Args:
            strategy: 策略实例
            data: 市场数据字典 {symbol: DataFrame}
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            回测结果
        """
        logger.info(f"开始回测: {start_date} 到 {end_date}")
        
        # 重置回测状态
        self._reset_backtest_state()
        
        # 获取所有交易日期
        all_dates = self._get_trading_dates(data, start_date, end_date)
        
        if not all_dates:
            logger.warning("没有找到有效的交易日期")
            return self._create_empty_result(start_date, end_date)
        
        # 逐日回测
        for current_date in all_dates:
            try:
                # 获取当日数据
                daily_data = self._get_daily_data(data, current_date)
                
                if not daily_data:
                    continue
                
                # 更新持仓盈亏
                self._update_positions_pnl(daily_data)
                
                # 执行策略
                signals = strategy.generate_signals(daily_data, current_date)
                
                # 处理交易信号
                self._process_signals(signals, daily_data, current_date)
                
                # 记录当日权益
                self._record_daily_equity(current_date)
                
            except Exception as e:
                logger.error(f"回测日期 {current_date} 处理失败: {e}")
                continue
        
        # 平仓所有持仓
        self._close_all_positions(all_dates[-1], data)
        
        # 生成回测结果
        result = self._generate_backtest_result(start_date, end_date)
        
        logger.info(f"回测完成: 总收益={result.total_return:.2%}, 夏普比率={result.sharpe_ratio:.3f}")
        
        return result
    
    def _reset_backtest_state(self):
        """重置回测状态"""
        self.current_capital = self.initial_capital
        self.positions.clear()
        self.trades.clear()
        self.equity_curve.clear()
        self.daily_pnl.clear()
    
    def _get_trading_dates(self, data: Dict[str, pd.DataFrame], 
                          start_date: str, end_date: str) -> List[datetime]:
        """获取交易日期列表"""
        all_dates = set()
        
        for symbol, df in data.items():
            if df.empty:
                continue
            
            # 筛选日期范围
            mask = (df.index >= start_date) & (df.index <= end_date)
            symbol_dates = df[mask].index
            all_dates.update(symbol_dates)
        
        return sorted(list(all_dates))
    
    def _get_daily_data(self, data: Dict[str, pd.DataFrame], 
                       current_date: datetime) -> Dict[str, pd.Series]:
        """获取当日数据"""
        daily_data = {}
        
        for symbol, df in data.items():
            if current_date in df.index:
                daily_data[symbol] = df.loc[current_date]
        
        return daily_data
    
    def _update_positions_pnl(self, daily_data: Dict[str, pd.Series]):
        """更新持仓盈亏"""
        for symbol, position in self.positions.items():
            if symbol in daily_data:
                current_price = daily_data[symbol]['close']
                
                # 计算未实现盈亏
                price_diff = current_price - position.entry_price
                unrealized_pnl = price_diff * position.direction * position.quantity
                
                if symbol in self.contract_info:
                    unrealized_pnl *= self.contract_info[symbol]['multiplier']
                
                position.unrealized_pnl = unrealized_pnl
    
    def _process_signals(self, signals: Dict[str, int], 
                        daily_data: Dict[str, pd.Series], 
                        current_date: datetime):
        """处理交易信号"""
        for symbol, signal in signals.items():
            if symbol not in daily_data:
                continue
            
            current_price = daily_data[symbol]['close']
            current_position = self.positions.get(symbol)
            
            # 处理平仓信号
            if current_position and signal == 0:
                self._close_position(symbol, current_price, current_date)
            
            # 处理开仓信号
            elif signal != 0:
                # 如果已有持仓且方向相反，先平仓
                if current_position and current_position.direction != signal:
                    self._close_position(symbol, current_price, current_date)
                
                # 开新仓
                if symbol not in self.positions:
                    quantity = self._calculate_position_size(symbol, current_price)
                    if quantity > 0:
                        self._open_position(symbol, signal, quantity, current_price, current_date)
    
    def _calculate_position_size(self, symbol: str, price: float) -> int:
        """计算持仓大小"""
        if symbol not in self.contract_info:
            return 1
        
        # 基于2%风险计算持仓
        risk_amount = self.current_capital * 0.02
        contract_value = price * self.contract_info[symbol]['multiplier']
        
        # 简化计算：假设止损为2%
        stop_loss_amount = contract_value * 0.02
        
        if stop_loss_amount > 0:
            quantity = int(risk_amount / stop_loss_amount)
            return max(1, min(quantity, 10))  # 限制在1-10手之间
        
        return 1
    
    def _open_position(self, symbol: str, direction: int, quantity: int,
                      price: float, timestamp: datetime):
        """开仓"""
        # 计算手续费和滑点
        if symbol in self.contract_info:
            contract_value = price * quantity * self.contract_info[symbol]['multiplier']
            commission = contract_value * self.commission_rate
            slippage = contract_value * self.slippage_rate * abs(direction)
        else:
            commission = 0
            slippage = 0
        
        # 创建持仓
        position = Position(
            symbol=symbol,
            direction=direction,
            quantity=quantity,
            entry_price=price,
            entry_time=timestamp
        )
        
        self.positions[symbol] = position
        
        # 扣除手续费和滑点
        self.current_capital -= (commission + slippage)
        
        logger.debug(f"开仓: {symbol} {direction} {quantity}手 @{price}")
    
    def _close_position(self, symbol: str, price: float, timestamp: datetime):
        """平仓"""
        if symbol not in self.positions:
            return
        
        position = self.positions[symbol]
        
        # 计算盈亏
        price_diff = price - position.entry_price
        pnl = price_diff * position.direction * position.quantity
        
        # 计算手续费和滑点
        if symbol in self.contract_info:
            multiplier = self.contract_info[symbol]['multiplier']
            pnl *= multiplier
            
            contract_value = price * position.quantity * multiplier
            commission = contract_value * self.commission_rate
            slippage = contract_value * self.slippage_rate * abs(position.direction)
        else:
            commission = 0
            slippage = 0
        
        # 净盈亏
        net_pnl = pnl - commission - slippage
        
        # 创建交易记录
        trade = Trade(
            symbol=symbol,
            entry_time=position.entry_time,
            exit_time=timestamp,
            direction=position.direction,
            entry_price=position.entry_price,
            exit_price=price,
            quantity=position.quantity,
            pnl=net_pnl,
            commission=commission,
            slippage=slippage
        )
        
        self.trades.append(trade)
        
        # 更新资金
        self.current_capital += net_pnl
        
        # 删除持仓
        del self.positions[symbol]
        
        logger.debug(f"平仓: {symbol} 盈亏={net_pnl:.2f}")
    
    def _close_all_positions(self, final_date: datetime, data: Dict[str, pd.DataFrame]):
        """平仓所有持仓"""
        for symbol in list(self.positions.keys()):
            if symbol in data and final_date in data[symbol].index:
                final_price = data[symbol].loc[final_date, 'close']
                self._close_position(symbol, final_price, final_date)
    
    def _record_daily_equity(self, current_date: datetime):
        """记录当日权益"""
        # 计算总权益（现金 + 持仓市值）
        total_equity = self.current_capital
        
        for position in self.positions.values():
            total_equity += position.unrealized_pnl
        
        self.equity_curve.append({
            'date': current_date,
            'equity': total_equity,
            'cash': self.current_capital,
            'unrealized_pnl': sum(p.unrealized_pnl for p in self.positions.values())
        })
    
    def _generate_backtest_result(self, start_date: str, end_date: str) -> BacktestResult:
        """生成回测结果"""
        if not self.equity_curve:
            return self._create_empty_result(start_date, end_date)
        
        # 转换为DataFrame
        equity_df = pd.DataFrame(self.equity_curve)
        equity_df.set_index('date', inplace=True)
        
        # 计算收益率
        equity_series = equity_df['equity']
        daily_returns = equity_series.pct_change().dropna()
        
        # 基础指标
        final_capital = equity_series.iloc[-1]
        total_return = (final_capital - self.initial_capital) / self.initial_capital
        
        # 年化收益率
        trading_days = len(equity_series)
        annual_return = (1 + total_return) ** (252 / trading_days) - 1 if trading_days > 0 else 0
        
        # 最大回撤
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak
        max_drawdown = abs(drawdown.min())
        
        # 夏普比率
        if len(daily_returns) > 1 and daily_returns.std() > 0:
            sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(252)
        else:
            sharpe_ratio = 0
        
        # Calmar比率
        calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
        
        # 交易统计
        if self.trades:
            winning_trades = [t for t in self.trades if t.pnl > 0]
            losing_trades = [t for t in self.trades if t.pnl < 0]
            
            win_rate = len(winning_trades) / len(self.trades)
            
            avg_win = np.mean([t.pnl for t in winning_trades]) if winning_trades else 0
            avg_loss = abs(np.mean([t.pnl for t in losing_trades])) if losing_trades else 1
            profit_factor = avg_win / avg_loss if avg_loss > 0 else 0
            
            avg_trade_pnl = np.mean([t.pnl for t in self.trades])
            
            # 连续盈亏统计
            consecutive_wins = 0
            consecutive_losses = 0
            max_consecutive_wins = 0
            max_consecutive_losses = 0
            
            for trade in self.trades:
                if trade.pnl > 0:
                    consecutive_wins += 1
                    consecutive_losses = 0
                    max_consecutive_wins = max(max_consecutive_wins, consecutive_wins)
                else:
                    consecutive_losses += 1
                    consecutive_wins = 0
                    max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
        else:
            win_rate = 0
            profit_factor = 0
            avg_trade_pnl = 0
            max_consecutive_wins = 0
            max_consecutive_losses = 0
        
        return BacktestResult(
            start_date=start_date,
            end_date=end_date,
            initial_capital=self.initial_capital,
            final_capital=final_capital,
            total_return=total_return,
            annual_return=annual_return,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            calmar_ratio=calmar_ratio,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=len(self.trades),
            avg_trade_pnl=avg_trade_pnl,
            max_consecutive_wins=max_consecutive_wins,
            max_consecutive_losses=max_consecutive_losses,
            trades=self.trades.copy(),
            equity_curve=equity_series,
            daily_returns=daily_returns
        )
    
    def _create_empty_result(self, start_date: str, end_date: str) -> BacktestResult:
        """创建空结果"""
        return BacktestResult(
            start_date=start_date,
            end_date=end_date,
            initial_capital=self.initial_capital,
            final_capital=self.initial_capital,
            total_return=0.0,
            annual_return=0.0,
            max_drawdown=0.0,
            sharpe_ratio=0.0,
            calmar_ratio=0.0,
            win_rate=0.0,
            profit_factor=0.0,
            total_trades=0,
            avg_trade_pnl=0.0,
            max_consecutive_wins=0,
            max_consecutive_losses=0,
            trades=[],
            equity_curve=pd.Series(),
            daily_returns=pd.Series()
        )
    
    def get_performance_summary(self, result: BacktestResult) -> Dict:
        """获取性能摘要"""
        return {
            'period': f"{result.start_date} 到 {result.end_date}",
            'returns': {
                'total_return': f"{result.total_return:.2%}",
                'annual_return': f"{result.annual_return:.2%}",
                'max_drawdown': f"{result.max_drawdown:.2%}"
            },
            'risk_metrics': {
                'sharpe_ratio': round(result.sharpe_ratio, 3),
                'calmar_ratio': round(result.calmar_ratio, 3),
                'volatility': f"{result.daily_returns.std() * np.sqrt(252):.2%}" if len(result.daily_returns) > 1 else "0.00%"
            },
            'trading_stats': {
                'total_trades': result.total_trades,
                'win_rate': f"{result.win_rate:.1%}",
                'profit_factor': round(result.profit_factor, 2),
                'avg_trade_pnl': f"{result.avg_trade_pnl:.2f}",
                'max_consecutive_wins': result.max_consecutive_wins,
                'max_consecutive_losses': result.max_consecutive_losses
            },
            'capital': {
                'initial_capital': f"{result.initial_capital:,.0f}",
                'final_capital': f"{result.final_capital:,.0f}",
                'profit_loss': f"{result.final_capital - result.initial_capital:,.0f}"
            }
        }
