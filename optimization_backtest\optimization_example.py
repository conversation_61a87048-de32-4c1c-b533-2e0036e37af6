"""
参数优化回测系统使用示例
展示如何使用新的增强版多品种回测引擎进行参数优化

新增功能：
1. 参数约束系统 - 确保参数之间的逻辑关系正确
   - ping_zy (保本点) < zy (止盈点)
   - AF (加速因子) < AF_max (最大加速因子) 
   - 止盈参数合理差距检查
   - K线周期参数逻辑性检查

2. 自动参数修正 - 当参数违反约束时自动调整
3. 约束验证 - 完整的参数约束检查和错误报告

使用方法：
python optimization_example.py --constraints  # 演示参数约束功能
python optimization_example.py --demo         # 运行简化演示  
python optimization_example.py --full         # 运行完整优化
"""

import sys
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加路径
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

# 导入所需模块
try:
    from vnpy.app.cta_strategy.strategies.fenzhouqiplus_strategy import FenZhouQiPlusStrategy
    from contract_info import ContractInfo
    VNPY_AVAILABLE = True
except ImportError:
    VNPY_AVAILABLE = False
    print("警告: VnPy相关模块未找到，将使用模拟策略")

from enhanced_multi_symbol_backtest import EnhancedMultiSymbolBacktest
from typing import Dict, Tuple


def generate_param_spaces(contract_size: float = 1) -> Dict[str, Tuple]:
    """
    生成参数优化空间
    
    Args:
        contract_size: 合约乘数大小
        
    Returns:
        Dict[str, Tuple]: 参数空间定义
    """
    param_spaces = {
        # 基础K线参数（整数优化）
        'k_1': (1, 10, 1),  # 1分钟K线：3-8
        'k_3': (5, 20, 1),  # 3分钟K线：5-12
        'k_5': (10, 30, 2),  # 5分钟K线：8-20，步长2
        'k_15': (15, 15, 1),  # 15分钟K线：10-25
        'k_30': (30, 30, 1),  # 30分钟K线：20-50

        # 核心参数（特别优化止盈逻辑）
        'atr_window': (30, 30.0001, 1),  # ATR周期：14-30
        'sl_multiplier': (1.5, 9.0, 0.5),  # 止损倍数：1.5-4.0
        'macd_boll_count_fz': (0.08, 0.2, 0.02),  # MACD阈值：0.08-0.18
        'dk_fz': (0.7, 1, 0.05),  # 多空阈值：0.7-0.95
        'ping_zy': (0, 30, 1),  # 保本点：5-25点（盈利后止损抬到开仓价）
        'zy': (5, 60, 5),  # 部分止盈点：10-40点（盈利后平一半仓位）
        'donchian_period': (20, 20.0001, 1),  # 唐奇安通道：15-30

        # 追踪止损参数（基于止盈点调整）
        'AF': (0.0002, 0.002, 0.0002),  # 加速因子：0.0005-0.0015
        'AF_max': (0.01, 0.2, 0.02),  # 最大加速：0.12-0.18
        'trailing_start_ratio': (0.3, 0.9, 0.2),  # 启动比例：0.4-0.8

        # 风控参数
        'daily_loss_limit': (1000, 3000, 500),  # 日损限额：1000-3000

        # 固定参数
        'lots': (1, 1+ 0.000001, 1),
        'use_trailing_stop': (1, 1 + 0.000001, 1),
        'contract_multiplier': (contract_size, contract_size + 0.000001, 1),  # 合约乘数（使用实际合约大小）
    }
    
    return param_spaces


def get_parameter_constraints() -> Dict[str, callable]:
    """
    定义参数之间的约束关系
    
    Returns:
        Dict[str, callable]: 约束函数字典，key为约束名称，value为约束检查函数
        约束函数返回True表示约束满足，False表示违反约束
    """
    constraints = {
        # ping_zy (保本点) 必须小于 zy (部分止盈点)
        'ping_zy_less_than_zy': lambda params: params.get('ping_zy', 0) < params.get('zy', float('inf')),
        
        # AF (加速因子) 必须小于 AF_max (最大加速因子)
        'AF_less_than_AF_max': lambda params: params.get('AF', 0) < params.get('AF_max', float('inf')),
        
        # sl_multiplier (止损倍数) 应该在合理范围内，不能过小
        'sl_multiplier_reasonable': lambda params: params.get('sl_multiplier', 2.0) >= 1.0,
        
        # trailing_start_ratio 应该在0-1之间
        'trailing_start_ratio_valid': lambda params: 0 < params.get('trailing_start_ratio', 0.5) < 1,
        
        # K线周期参数的合理性检查（短周期应该比长周期小或相等）
        'k_periods_logical': lambda params: (
            params.get('k_1', 1) <= params.get('k_3', 3) and
            params.get('k_3', 3) <= params.get('k_5', 5)
        ),
        
        # 止盈参数应该有合理的差距（zy至少比ping_zy大5个点）
        'stop_profit_gap': lambda params: params.get('zy', 15) >= params.get('ping_zy', 5) + 5,
    }
    
    return constraints


def apply_parameter_constraints(params: Dict, param_spaces: Dict[str, Tuple]) -> Dict:
    """
    应用参数约束，自动调整参数以满足约束关系
    
    Args:
        params: 原始参数字典
        param_spaces: 参数空间定义
        
    Returns:
        Dict: 调整后满足约束的参数字典
    """
    constrained_params = params.copy()
    constraints = get_parameter_constraints()
    max_iterations = 10  # 最大调整次数，避免无限循环
    
    for iteration in range(max_iterations):
        all_constraints_satisfied = True
        
        for constraint_name, constraint_func in constraints.items():
            if not constraint_func(constrained_params):
                all_constraints_satisfied = False
                
                # 针对不同约束进行特定的参数调整
                if constraint_name == 'ping_zy_less_than_zy':
                    # 如果ping_zy >= zy，调整ping_zy使其小于zy
                    ping_zy = constrained_params.get('ping_zy', 10)
                    zy = constrained_params.get('zy', 20)
                    
                    if ping_zy >= zy:
                        # 尝试将ping_zy调整为zy的一半或者最小值
                        new_ping_zy = max(param_spaces['ping_zy'][0], min(ping_zy, zy - 5))
                        constrained_params['ping_zy'] = new_ping_zy
                        
                        # 如果还不满足，调整zy
                        if new_ping_zy >= zy:
                            new_zy = max(new_ping_zy + 5, param_spaces['zy'][0])
                            new_zy = min(new_zy, param_spaces['zy'][1])  # 不超过最大值
                            constrained_params['zy'] = new_zy
                
                elif constraint_name == 'AF_less_than_AF_max':
                    # 调整AF和AF_max的关系
                    af = constrained_params.get('AF', 0.001)
                    af_max = constrained_params.get('AF_max', 0.15)
                    
                    if af >= af_max:
                        new_af = min(af, af_max * 0.8)  # AF设为AF_max的80%
                        constrained_params['AF'] = max(new_af, param_spaces['AF'][0])
                
                elif constraint_name == 'stop_profit_gap':
                    # 确保止盈参数有合理差距
                    ping_zy = constrained_params.get('ping_zy', 10)
                    zy = constrained_params.get('zy', 20)
                    
                    if zy < ping_zy + 5:
                        # 优先调整zy，使其至少比ping_zy大5
                        new_zy = min(ping_zy + 5, param_spaces['zy'][1])
                        constrained_params['zy'] = new_zy
                        
                        # 如果zy达到上限仍不满足，调整ping_zy
                        if new_zy < ping_zy + 5:
                            new_ping_zy = max(new_zy - 5, param_spaces['ping_zy'][0])
                            constrained_params['ping_zy'] = new_ping_zy
                
                elif constraint_name == 'k_periods_logical':
                    # 调整K线周期参数
                    k1 = constrained_params.get('k_1', 1)
                    k3 = constrained_params.get('k_3', 5)
                    k5 = constrained_params.get('k_5', 8)
                    
                    # 确保递增关系
                    if k1 > k3:
                        constrained_params['k_3'] = max(k1, param_spaces['k_3'][0])
                    if k3 > k5:
                        constrained_params['k_5'] = max(k3, param_spaces['k_5'][0])
        
        # 如果所有约束都满足，退出循环
        if all_constraints_satisfied:
            break
    
    # 最后再次约束到参数范围内（不使用递归调用）
    final_params = {}
    for param_name, value in constrained_params.items():
        if param_name in param_spaces:
            min_val, max_val, step = param_spaces[param_name]
            
            # 约束到范围内
            constrained_value = max(min_val, min(max_val, value))
            
            # 对齐到步长（如果是整数参数）
            if isinstance(step, int) or (isinstance(step, float) and step == int(step)):
                constrained_value = int(round(constrained_value / step) * step)
            else:
                constrained_value = round(constrained_value / step) * step
            
            # 再次确保在范围内
            constrained_value = max(min_val, min(max_val, constrained_value))
            
            final_params[param_name] = constrained_value
        else:
            # 未定义的参数直接保留
            final_params[param_name] = value
    
    return final_params


def validate_parameter_constraints(params: Dict, symbol: str = "") -> bool:
    """
    验证参数是否满足所有约束条件
    
    Args:
        params: 参数字典
        symbol: 合约代码（用于错误提示）
    
    Returns:
        bool: True如果满足所有约束
    """
    constraints = get_parameter_constraints()
    violations = []
    
    for constraint_name, constraint_func in constraints.items():
        if not constraint_func(params):
            violations.append(constraint_name)
    
    if violations:
        symbol_prefix = f"[{symbol}] " if symbol else ""
        print(f"⚠️ {symbol_prefix}参数约束违反:")
        for violation in violations:
            if violation == 'ping_zy_less_than_zy':
                print(f"   ping_zy ({params.get('ping_zy')}) 必须小于 zy ({params.get('zy')})")
            elif violation == 'AF_less_than_AF_max':
                print(f"   AF ({params.get('AF')}) 必须小于 AF_max ({params.get('AF_max')})")
            elif violation == 'stop_profit_gap':
                print(f"   zy ({params.get('zy')}) 必须比 ping_zy ({params.get('ping_zy')}) 至少大5个点")
            else:
                print(f"   {violation}")
        return False
    
    return True


def validate_and_constrain_params(params: Dict, param_spaces: Dict[str, Tuple]) -> Dict:
    """
    验证并约束参数在定义的范围内，同时应用参数间的约束关系
    
    Args:
        params: 待验证的参数字典
        param_spaces: 参数空间定义
    
    Returns:
        Dict: 经过约束的参数字典
    """
    # 首先进行基本的范围约束
    constrained_params = {}
    for param_name, value in params.items():
        if param_name in param_spaces:
            min_val, max_val, step = param_spaces[param_name]
            
            # 约束到范围内
            constrained_value = max(min_val, min(max_val, value))
            
            # 对齐到步长（如果是整数参数）
            if isinstance(step, int) or (isinstance(step, float) and step == int(step)):
                constrained_value = int(round(constrained_value / step) * step)
            else:
                constrained_value = round(constrained_value / step) * step
            
            # 再次确保在范围内
            constrained_value = max(min_val, min(max_val, constrained_value))
            
            constrained_params[param_name] = constrained_value
        else:
            # 未定义的参数直接保留
            constrained_params[param_name] = value
    
    # 然后应用参数间的约束关系（不会再次调用本函数，避免递归）
    constrained_params = apply_parameter_constraints(constrained_params, param_spaces)
    
    return constrained_params


def check_params_in_range(params: Dict, param_spaces: Dict[str, Tuple], symbol: str = "") -> bool:
    """
    检查参数是否在定义范围内
    
    Args:
        params: 参数字典
        param_spaces: 参数空间定义
        symbol: 合约代码（用于错误提示）
    
    Returns:
        bool: True如果所有参数都在范围内
    """
    is_valid = True
    violations = []
    
    for param_name, value in params.items():
        if param_name in param_spaces:
            min_val, max_val, step = param_spaces[param_name]
            
            if value < min_val or value > max_val:
                is_valid = False
                violations.append(f"{param_name}: {value} 不在范围 [{min_val}, {max_val}] 内")
    
    if not is_valid:
        symbol_prefix = f"[{symbol}] " if symbol else ""
        print(f"⚠️ {symbol_prefix}参数超出范围:")
        for violation in violations:
            print(f"   {violation}")
    
    return is_valid


def main():
    """主函数：演示参数优化回测流程"""
    
    print("=" * 80)
    print("多品种参数优化回测系统")
    print("=" * 80)
    
    # 1. 初始化回测引擎
    print("\n1. 初始化回测引擎...")
    backtester = EnhancedMultiSymbolBacktest(
        results_dir="optimization_backtest/results",
        max_workers=16  # 并行进程数，根据CPU核心数调整
    )
    
    # 2. 配置合约信息
    print("\n2. 配置合约信息...")
    
    # 黑名单：不参与回测的合约
    blacklist = [
        'IC888.CFFEX', 'IF888.CFFEX', 'IH888.CFFEX', 'IM888.CFFEX', 
        'T888.CFFEX', 'TF888.CFFEX', 'TS888.CFFEX', 
        'au888.SHFE', 'wr888.SHFE', 'ec888.INE'
    ]
    
    if VNPY_AVAILABLE:
        try:
            # 从Excel文件加载合约信息
            contract_info = ContractInfo("期货全品种手续费保证金.xls")
            symbols = contract_info.get_888_contracts()
            # symbols = [symbol for symbol in symbols if symbol not in blacklist]

            symbols = [ 'PX888.CZCE', 'TA888.CZCE', 'bu888.SHFE','SH888.CZCE', 'br888.SHFE', 'lu888.INE', ]
            # 获取合约参数
            params = contract_info.get_contract_params()
            rate = params['rate']
            size = params['size']
            pricetick = params['pricetick']
            
        except FileNotFoundError:
            print("合约信息文件未找到，使用默认配置")
            symbols = ['cu888.SHFE', 'al888.SHFE', 'zn888.SHFE']  # 示例合约
            rate = {symbol: 0.0002 for symbol in symbols}
            size = {symbol: 5 for symbol in symbols}
            pricetick = {symbol: 10 for symbol in symbols}
    else:
        # 使用默认测试配置
        symbols = ['TEST1', 'TEST2', 'TEST3']
        rate = {symbol: 0.0002 for symbol in symbols}
        size = {symbol: 10 for symbol in symbols}
        pricetick = {symbol: 1 for symbol in symbols}
    
    print(f"选定合约数量: {len(symbols)}")
    print(f"示例合约: {symbols[:100]}")  # 显示前5个合约
    
    # 3. 设置回测参数
    print("\n3. 设置回测参数...")
    
    backtest_config = {
        'interval': '1m',
        'start': datetime(2023, 1, 1),
        'end': datetime(2025, 6, 1),
        'slippage': 0,
        'capital': 200000
    }
    
    print(f"回测期间: {backtest_config['start']} - {backtest_config['end']}")
    print(f"初始资金: {backtest_config['capital']:,}")
    
    # 4. 定义参数优化空间
    print("\n4. 定义参数优化空间...")
    
    # 为了演示，我们选择一个较小的合约集合进行测试
    test_symbols = symbols[:1000] if len(symbols) > 2 else symbols
    print(f"演示模式：仅对前{len(test_symbols)}个合约进行优化")
    print(f"测试合约: {test_symbols}")
    
    # 显示每个合约的参数配置
    print("\n合约参数配置:")
    for symbol in test_symbols:
        symbol_size = size.get(symbol, 1)
        symbol_pricetick = pricetick.get(symbol, 1)
        symbol_rate = rate.get(symbol, 0.0002)
        print(f"  {symbol}: size={symbol_size}, pricetick={symbol_pricetick}, rate={symbol_rate}")
    
    # 生成基础参数空间（用于显示）
    base_param_spaces = generate_param_spaces(contract_size=1)  # 用于显示的基础模板
    
    print(f"参数空间维度: {len(base_param_spaces)}")
    print("主要优化参数:")
    for param, space in base_param_spaces.items():
        if space[1] != space[0]:  # 只显示需要优化的参数
            print(f"  {param}: {space[0]} - {space[1]} (步长: {space[2] if len(space) > 2 else 'continuous'})")
        elif param == 'contract_multiplier':
            print(f"  {param}: 动态设置为合约大小 (各合约不同)")
    
    # 5. 选择运行模式
    print("\n5. 选择运行模式...")
    
    # 选择策略类
    if VNPY_AVAILABLE:
        try:
            strategy_class = FenZhouQiPlusStrategy
            print("使用策略: FenZhouQiPlusStrategy")
        except:
            print("FenZhouQiPlusStrategy策略未找到，将跳过实际回测")
            strategy_class = None
    else:
        strategy_class = None
        print("使用模拟策略进行演示")
    
    # 6. 运行参数优化
    print("\n6. 开始参数优化...")
    print("=" * 60)
    
    if strategy_class:
        # 运行批量优化，系统会自动为每个合约生成正确的参数空间
        optimization_results = backtester.run_batch_optimization_with_dynamic_params(
            strategy_class=strategy_class,
            symbols=test_symbols,
            param_spaces_generator=generate_param_spaces,  # 传递参数空间生成函数
            interval=backtest_config['interval'],
            start=backtest_config['start'],
            end=backtest_config['end'],
            rate=rate,
            slippage=backtest_config['slippage'],
            size=size,
            pricetick=pricetick,
            capital=backtest_config['capital'],
            target_name='sharpe_ratio'
        )
        
        # 7. 结果分析
        print("\n7. 优化结果分析...")
        print("=" * 60)
        
        for symbol, result in optimization_results.items():
            if 'error' not in result:
                print(f"\n合约: {symbol}")
                print(f"合约大小: {size.get(symbol, 1)}")
                print(f"最佳夏普比率: {result.get('best_target_value', 0):.4f}")
                print(f"优化耗时: {result.get('total_time', 0):.1f}秒")
                
                # 获取参数空间定义
                symbol_size = size.get(symbol, 1)
                symbol_param_spaces = generate_param_spaces(contract_size=symbol_size)
                
                # 显示最佳参数并进行验证
                best_params = result.get('best_params', {})
                print("最佳参数:")
                
                # 验证参数是否在范围内
                is_params_valid = check_params_in_range(best_params, symbol_param_spaces, symbol)
                
                # 验证参数约束
                is_constraints_valid = validate_parameter_constraints(best_params, symbol)
                
                if not is_params_valid or not is_constraints_valid:
                    print("   🔧 正在修正参数...")
                    corrected_params = validate_and_constrain_params(best_params, symbol_param_spaces)
                    print("   修正后的参数:")
                    best_params = corrected_params
                    
                    # 再次验证约束
                    final_constraints_valid = validate_parameter_constraints(best_params, symbol)
                    if final_constraints_valid:
                        print("   ✅ 参数约束修正成功")
                    else:
                        print("   ⚠️ 部分约束仍未满足")
                
                for param, value in best_params.items():
                    if param in symbol_param_spaces:
                        min_val, max_val, step = symbol_param_spaces[param]
                        if param == 'contract_multiplier':
                            print(f"  {param}: {value} (合约大小)")
                        else:
                            # 显示参数值及其范围
                            range_info = f"[{min_val}-{max_val}]"
                            if value < min_val or value > max_val:
                                print(f"  {param}: {value} {range_info} ⚠️ 超出范围")
                            else:
                                print(f"  {param}: {value} {range_info} ✓")
                    else:
                        print(f"  {param}: {value}")
                
                # 显示鲁棒性指标
                validation = result.get('validation_results', {})
                sensitivity = validation.get('sensitivity', {})
                if sensitivity:
                    print(f"稳定性得分: {sensitivity.get('stability_score', 0):.4f}")
                    print(f"成功率: {sensitivity.get('success_rate', 0):.2%}")
            else:
                print(f"\n合约: {symbol} - 优化失败: {result.get('error', '未知错误')}")
    
    else:
        # 演示模式：显示优化流程但不实际运行
        print("演示模式：显示优化流程")
        print("\n预期优化流程:")
        print("1. 参数重要性分析 (Sobol序列，1000样本)")
        print("2. 分层优化:")
        print("   - 粗搜索 (拉丁超立方采样，500样本)")
        print("   - 贝叶斯优化 (100次迭代)")
        print("   - 精细搜索 (局部网格，125样本)")
        print("3. 鲁棒性验证:")
        print("   - Walk-Forward验证")
        print("   - 参数敏感性测试")
        
        print(f"\n预估总样本数: ~1625/合约")
        print(f"预估总耗时: ~{len(test_symbols) * 30}分钟 (假设每个回测2秒)")
        
        # 显示每个合约的contract_multiplier设置
        print(f"\n各合约contract_multiplier设置:")
        for symbol in test_symbols:
            symbol_size = size.get(symbol, 1)
            print(f"  {symbol}: contract_multiplier = {symbol_size}")
        
        # 显示参数范围验证功能
        print(f"\n参数范围验证功能:")
        print("  ✓ 所有输出参数将严格限制在定义范围内")
        print("  ✓ 自动修正超出范围的参数")
        print("  ✓ 提供参数范围违规警告")
    
    # 8. 生成报告
    print("\n8. 生成报告...")
    
    report_file = backtester.generate_report(test_symbols)
    if report_file:
        print(f"详细报告已保存: {report_file}")
    
    print("\n" + "=" * 80)
    print("参数优化回测完成！")
    print("结果文件保存在: optimization_backtest/results/")
    print("✅ 所有优化参数已验证并约束在定义范围内")
    print("=" * 80)


def run_simple_demo():
    """运行简化演示版本"""
    print("运行简化演示...")
    
    # 创建模拟数据进行演示
    from optimization_engine import ParameterSampler, ParameterImportanceAnalyzer
    
    # 定义示例参数空间
    param_spaces = {
        'param1': (1, 10, 1),
        'param2': (0.1, 1.0, 0.1),
        'param3': (5, 50, 5)
    }
    
    print(f"参数空间: {param_spaces}")
    
    # 生成Sobol样本
    print("\n生成Sobol样本...")
    samples = ParameterSampler.sobol_sample(param_spaces, 100)
    print(f"生成了{len(samples)}个样本")
    
    # 模拟性能得分
    import numpy as np
    performance_scores = [
        sample['param1'] * 0.1 + sample['param2'] * 0.5 + np.random.normal(0, 0.1)
        for sample in samples
    ]
    
    # 分析参数重要性
    print("\n分析参数重要性...")
    importance = ParameterImportanceAnalyzer.analyze_importance(samples, performance_scores)
    
    print("参数重要性排序:")
    for param, score in sorted(importance.items(), key=lambda x: x[1], reverse=True):
        print(f"  {param}: {score:.4f}")
    
    print("\n简化演示完成！")


def demo_parameter_constraints():
    """演示参数约束功能的工作原理"""
    print("=" * 60)
    print("参数约束演示")
    print("=" * 60)
    
    # 生成测试参数空间
    param_spaces = generate_param_spaces(contract_size=5)
    
    # 创建一些违反约束的测试参数
    test_cases = [
        {
            'name': '正常参数',
            'params': {'ping_zy': 10, 'zy': 25, 'AF': 0.001, 'AF_max': 0.15}
        },
        {
            'name': '违反止盈约束 (ping_zy >= zy)',
            'params': {'ping_zy': 30, 'zy': 20, 'AF': 0.001, 'AF_max': 0.15}
        },
        {
            'name': '违反加速因子约束 (AF >= AF_max)',
            'params': {'ping_zy': 10, 'zy': 25, 'AF': 0.16, 'AF_max': 0.15}
        },
        {
            'name': '违反止盈差距约束 (zy - ping_zy < 5)',
            'params': {'ping_zy': 22, 'zy': 25, 'AF': 0.001, 'AF_max': 0.15}
        },
        {
            'name': '多重约束违反',
            'params': {'ping_zy': 35, 'zy': 30, 'AF': 0.18, 'AF_max': 0.15, 'k_1': 10, 'k_3': 5}
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试案例: {test_case['name']}")
        print(f"原始参数: {test_case['params']}")
        
        # 检查约束
        is_valid = validate_parameter_constraints(test_case['params'])
        
        if not is_valid:
            # 应用约束修正
            corrected_params = apply_parameter_constraints(test_case['params'], param_spaces)
            print(f"修正后参数: {corrected_params}")
            
            # 验证修正结果
            is_corrected_valid = validate_parameter_constraints(corrected_params)
            print(f"修正是否成功: {'✅' if is_corrected_valid else '❌'}")
        else:
            print("✅ 参数满足所有约束")
    
    print("\n" + "=" * 60)
    print("参数约束演示完成")
    print("=" * 60)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="参数优化回测系统")
    parser.add_argument("--demo", action="store_true", help="运行简化演示")
    parser.add_argument("--full", action="store_true", help="运行完整优化")
    parser.add_argument("--constraints", action="store_true", help="演示参数约束功能")
    
    args = parser.parse_args()
    
    if args.demo:
        run_simple_demo()
    elif args.full:
        main()
    elif args.constraints:
        demo_parameter_constraints()
    else:
        print("请选择运行模式:")
        print("python optimization_example.py --demo         # 运行简化演示")
        print("python optimization_example.py --full         # 运行完整优化")
        print("python optimization_example.py --constraints  # 演示参数约束功能")
        
        # 默认运行简化演示
        # run_simple_demo() 
        main()