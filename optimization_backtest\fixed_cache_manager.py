"""
修复版缓存管理器 - 解决缓存污染问题
Fixed Cache Manager - Solving Cache Pollution Issues

主要修复:
1. 品种间缓存隔离
2. 时间区间缓存隔离  
3. 策略类缓存隔离
4. 合约信息缓存隔离
5. 缓存一致性验证
6. 自动缓存清理
"""

import hashlib
import pickle
import time
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class CacheConfig:
    """缓存配置信息"""
    symbol: str
    contract_size: float
    rate: float
    pricetick: float
    start_date: str
    end_date: str
    strategy_type: str
    fast_mode: bool
    cache_version: str = "1.0"
    created_time: float = None
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = time.time()


class FixedCacheManager:
    """修复版缓存管理器 - 解决所有缓存污染问题"""
    
    def __init__(self, cache_dir: str = "fixed_cache", max_age_days: int = 30):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_age_days = max_age_days
        self.cache_version = "1.0"
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'invalidations': 0,
            'cleanups': 0
        }
        
        logger.info(f"FixedCacheManager 初始化: {self.cache_dir}")
    
    def _generate_comprehensive_cache_key(self, 
                                        symbol: str,
                                        contract_info: Dict[str, Any],
                                        start_date: str,
                                        end_date: str,
                                        strategy_type: str,
                                        fast_mode: bool,
                                        params: Dict[str, Any]) -> str:
        """
        生成包含所有关键信息的缓存键
        
        确保不同品种、时间区间、策略、合约信息的缓存完全隔离
        """
        # 关键隔离因素
        isolation_factors = {
            'symbol': symbol,
            'contract_size': contract_info.get('size', 1),
            'rate': contract_info.get('rate', 0.0002),
            'pricetick': contract_info.get('pricetick', 1.0),
            'start_date': start_date,
            'end_date': end_date,
            'strategy_type': strategy_type,
            'fast_mode': fast_mode,
            'cache_version': self.cache_version
        }
        
        # 参数哈希 (单独计算避免顺序问题)
        params_sorted = sorted(params.items()) if params else []
        params_hash = hashlib.md5(str(params_sorted).encode()).hexdigest()[:8]
        
        # 组合所有因素
        key_components = []
        for key, value in isolation_factors.items():
            key_components.append(f"{key}={value}")
        key_components.append(f"params={params_hash}")
        
        # 生成最终缓存键
        key_string = "|".join(key_components)
        cache_key = hashlib.md5(key_string.encode()).hexdigest()
        
        return cache_key
    
    def _get_cache_file_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.pkl"
    
    def _get_config_file_path(self, cache_key: str) -> Path:
        """获取配置文件路径"""
        return self.cache_dir / f"{cache_key}_config.json"
    
    def _save_cache_config(self, cache_key: str, config: CacheConfig):
        """保存缓存配置信息"""
        config_file = self._get_config_file_path(cache_key)
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                # 转换为字典以便JSON序列化
                config_dict = {
                    'symbol': config.symbol,
                    'contract_size': config.contract_size,
                    'rate': config.rate,
                    'pricetick': config.pricetick,
                    'start_date': config.start_date,
                    'end_date': config.end_date,
                    'strategy_type': config.strategy_type,
                    'fast_mode': config.fast_mode,
                    'cache_version': config.cache_version,
                    'created_time': config.created_time
                }
                json.dump(config_dict, f, indent=2)
        except Exception as e:
            logger.warning(f"保存缓存配置失败: {e}")
    
    def _load_cache_config(self, cache_key: str) -> Optional[CacheConfig]:
        """加载缓存配置信息"""
        config_file = self._get_config_file_path(cache_key)
        if not config_file.exists():
            return None
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
                return CacheConfig(**config_dict)
        except Exception as e:
            logger.warning(f"加载缓存配置失败: {e}")
            return None
    
    def _validate_cache_consistency(self, 
                                  cache_key: str,
                                  current_config: CacheConfig) -> bool:
        """验证缓存的一致性"""
        cached_config = self._load_cache_config(cache_key)
        if cached_config is None:
            return False
        
        # 检查关键字段是否一致
        critical_fields = [
            'symbol', 'contract_size', 'rate', 'pricetick',
            'start_date', 'end_date', 'strategy_type', 'fast_mode'
        ]
        
        for field in critical_fields:
            cached_value = getattr(cached_config, field)
            current_value = getattr(current_config, field)
            
            if cached_value != current_value:
                logger.warning(f"缓存不一致 - {field}: 缓存={cached_value}, 当前={current_value}")
                return False
        
        # 检查缓存版本
        if cached_config.cache_version != current_config.cache_version:
            logger.warning(f"缓存版本不匹配: {cached_config.cache_version} vs {current_config.cache_version}")
            return False
        
        # 检查缓存年龄
        cache_age_days = (time.time() - cached_config.created_time) / (24 * 3600)
        if cache_age_days > self.max_age_days:
            logger.info(f"缓存过期: {cache_age_days:.1f} 天 > {self.max_age_days} 天")
            return False
        
        return True
    
    def get_cached_result(self,
                         symbol: str,
                         contract_info: Dict[str, Any],
                         start_date: str,
                         end_date: str,
                         strategy_type: str,
                         fast_mode: bool,
                         params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        获取缓存的回测结果
        
        Args:
            symbol: 品种代码
            contract_info: 合约信息
            start_date: 开始日期
            end_date: 结束日期
            strategy_type: 策略类型
            fast_mode: 是否快速模式
            params: 参数字典
            
        Returns:
            缓存的结果或None
        """
        # 生成缓存键
        cache_key = self._generate_comprehensive_cache_key(
            symbol, contract_info, start_date, end_date, 
            strategy_type, fast_mode, params
        )
        
        cache_file = self._get_cache_file_path(cache_key)
        
        # 检查缓存文件是否存在
        if not cache_file.exists():
            self.stats['misses'] += 1
            return None
        
        # 验证缓存一致性
        current_config = CacheConfig(
            symbol=symbol,
            contract_size=contract_info.get('size', 1),
            rate=contract_info.get('rate', 0.0002),
            pricetick=contract_info.get('pricetick', 1.0),
            start_date=start_date,
            end_date=end_date,
            strategy_type=strategy_type,
            fast_mode=fast_mode,
            cache_version=self.cache_version
        )
        
        if not self._validate_cache_consistency(cache_key, current_config):
            # 缓存不一致，删除并返回None
            self._invalidate_cache(cache_key)
            self.stats['invalidations'] += 1
            self.stats['misses'] += 1
            return None
        
        # 加载缓存结果
        try:
            with open(cache_file, 'rb') as f:
                result = pickle.load(f)
                self.stats['hits'] += 1
                logger.debug(f"缓存命中: {symbol} {start_date}-{end_date}")
                return result
        except Exception as e:
            logger.warning(f"加载缓存失败: {e}")
            self._invalidate_cache(cache_key)
            self.stats['misses'] += 1
            return None
    
    def save_result_to_cache(self,
                           symbol: str,
                           contract_info: Dict[str, Any],
                           start_date: str,
                           end_date: str,
                           strategy_type: str,
                           fast_mode: bool,
                           params: Dict[str, Any],
                           result: Dict[str, Any]):
        """
        保存回测结果到缓存
        
        Args:
            symbol: 品种代码
            contract_info: 合约信息
            start_date: 开始日期
            end_date: 结束日期
            strategy_type: 策略类型
            fast_mode: 是否快速模式
            params: 参数字典
            result: 回测结果
        """
        # 生成缓存键
        cache_key = self._generate_comprehensive_cache_key(
            symbol, contract_info, start_date, end_date,
            strategy_type, fast_mode, params
        )
        
        cache_file = self._get_cache_file_path(cache_key)
        
        try:
            # 保存结果
            with open(cache_file, 'wb') as f:
                pickle.dump(result, f)
            
            # 保存配置
            config = CacheConfig(
                symbol=symbol,
                contract_size=contract_info.get('size', 1),
                rate=contract_info.get('rate', 0.0002),
                pricetick=contract_info.get('pricetick', 1.0),
                start_date=start_date,
                end_date=end_date,
                strategy_type=strategy_type,
                fast_mode=fast_mode,
                cache_version=self.cache_version
            )
            self._save_cache_config(cache_key, config)
            
            logger.debug(f"缓存保存: {symbol} {start_date}-{end_date}")
            
        except Exception as e:
            logger.warning(f"保存缓存失败: {e}")
    
    def _invalidate_cache(self, cache_key: str):
        """使缓存失效"""
        cache_file = self._get_cache_file_path(cache_key)
        config_file = self._get_config_file_path(cache_key)
        
        try:
            if cache_file.exists():
                cache_file.unlink()
            if config_file.exists():
                config_file.unlink()
        except Exception as e:
            logger.warning(f"删除缓存失败: {e}")
    
    def clear_cache_for_symbol(self, symbol: str):
        """清理特定品种的所有缓存"""
        cleared_count = 0
        
        for config_file in self.cache_dir.glob("*_config.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                    if config_dict.get('symbol') == symbol:
                        cache_key = config_file.stem.replace('_config', '')
                        self._invalidate_cache(cache_key)
                        cleared_count += 1
            except Exception as e:
                logger.warning(f"清理缓存时读取配置失败: {e}")
        
        logger.info(f"清理品种 {symbol} 的缓存: {cleared_count} 个文件")
        return cleared_count
    
    def cleanup_expired_cache(self) -> int:
        """清理过期缓存"""
        cutoff_time = time.time() - (self.max_age_days * 24 * 3600)
        cleaned_count = 0
        
        for config_file in self.cache_dir.glob("*_config.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                    created_time = config_dict.get('created_time', 0)
                    
                    if created_time < cutoff_time:
                        cache_key = config_file.stem.replace('_config', '')
                        self._invalidate_cache(cache_key)
                        cleaned_count += 1
            except Exception as e:
                logger.warning(f"清理过期缓存时读取配置失败: {e}")
        
        self.stats['cleanups'] += cleaned_count
        logger.info(f"清理过期缓存: {cleaned_count} 个文件")
        return cleaned_count
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0
        
        # 统计缓存文件数量
        cache_files = list(self.cache_dir.glob("*.pkl"))
        config_files = list(self.cache_dir.glob("*_config.json"))
        
        return {
            'hit_rate': hit_rate,
            'total_hits': self.stats['hits'],
            'total_misses': self.stats['misses'],
            'total_requests': total_requests,
            'invalidations': self.stats['invalidations'],
            'cleanups': self.stats['cleanups'],
            'cache_files_count': len(cache_files),
            'config_files_count': len(config_files),
            'cache_dir_size_mb': sum(f.stat().st_size for f in cache_files) / (1024 * 1024)
        }
    
    def clear_all_cache(self):
        """清理所有缓存"""
        cleared_count = 0
        
        for cache_file in self.cache_dir.glob("*.pkl"):
            try:
                cache_file.unlink()
                cleared_count += 1
            except Exception as e:
                logger.warning(f"删除缓存文件失败: {e}")
        
        for config_file in self.cache_dir.glob("*_config.json"):
            try:
                config_file.unlink()
            except Exception as e:
                logger.warning(f"删除配置文件失败: {e}")
        
        logger.info(f"清理所有缓存: {cleared_count} 个文件")
        return cleared_count


# 全局缓存管理器实例
_global_cache_manager = None

def get_fixed_cache_manager(cache_dir: str = "fixed_cache", 
                           max_age_days: int = 30) -> FixedCacheManager:
    """获取全局缓存管理器实例"""
    global _global_cache_manager
    
    if _global_cache_manager is None:
        _global_cache_manager = FixedCacheManager(cache_dir, max_age_days)
    
    return _global_cache_manager


def reset_cache_manager():
    """重置全局缓存管理器"""
    global _global_cache_manager
    _global_cache_manager = None
