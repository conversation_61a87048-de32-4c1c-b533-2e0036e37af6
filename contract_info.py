import pandas as pd
from typing import Dict, List
import os

class ContractInfo:
    """合约信息处理类"""
    
    # 交易所映射关系 - 品种代码全部使用大写，方便查找
    EXCHANGE_MAP = {
        # 中金所品种
        "IF": "CFFEX",
        "IH": "CFFEX",
        "IC": "CFFEX",
        "IM": "CFFEX",
        "T": "CFFEX",
        "TF": "CFFEX",
        "TS": "CFFEX",
        
        # 上期所品种
        "CU": "SHFE",
        "AL": "SHFE",
        "ZN": "SHFE",
        "PB": "SHFE",
        "NI": "SHFE",
        "SN": "SHFE",
        "AU": "SHFE",
        "AG": "SHFE",
        "RB": "SHFE",
        "WR": "SHFE",
        "HC": "SHFE",
        "SS": "SHFE",
        "BU": "SHFE",
        "RU": "SHFE",
        "SP": "SHFE",
        "FU": "SHFE",
        "BR": "SHFE",
        "AO": "SHFE",
        "AD": "SHFE",
        
        # 大商所品种
        "C": "DCE",
        "CS": "DCE",
        "A": "DCE",
        "B": "DCE",
        "M": "DCE",
        "Y": "DCE",
        "P": "DCE",
        "J": "DCE",
        "JM": "DCE",
        "I": "DCE",
        "L": "DCE",
        "V": "DCE",
        "PP": "DCE",
        "EG": "DCE",
        "EB": "DCE",
        "PG": "DCE",  # 液化石油气
        "RR": "DCE",  # 粳米
        "LH": "DCE",  # 生猪
        "FB": "DCE",  # 纤维板
        "BB": "DCE",  # 胶合板
        "JD": "DCE",  # 鸡蛋
        
        # 郑商所品种
        "ZC": "CZCE",
        "SF": "CZCE",
        "SM": "CZCE",
        "WH": "CZCE",
        "JR": "CZCE",
        "LR": "CZCE",
        "PM": "CZCE",
        "RI": "CZCE",
        "RS": "CZCE",
        "OI": "CZCE",
        "RM": "CZCE",
        "AP": "CZCE",
        "CJ": "CZCE",
        "SR": "CZCE",
        "CF": "CZCE",
        "CY": "CZCE",
        "MA": "CZCE",
        "TA": "CZCE",
        "UR": "CZCE",
        "SA": "CZCE",
        "FG": "CZCE",
        "PF": "CZCE",
        "PK": "CZCE",  # 花生
        "PX": "CZCE",  # 对二甲苯
        "SH": "CZCE",  # 白糖
        "PR": "CZCE",  # 油菜籽
        
        # 广期所品种
        "SI": "GFEX",
        "LC": "GFEX",
        "PS": "GFEX",

        # 上海国际能源交易中心
        "SC": "INE",
        "BC": "INE",
        "LU": "INE",
        "EC": "INE",  # 原油期权
        "NR": "INE",  # 20号胶
    }
    
    # 使用大写品种代码的交易所
    UPPERCASE_EXCHANGES = ["CFFEX", "CZCE"]
    
    def __init__(self, excel_path: str, blacklist: List[str] = None):
        """初始化
        
        Args:
            excel_path: Excel文件路径，包含合约信息
            blacklist: 合约黑名单列表，这些合约将被排除在回测之外
        """
        self.excel_path = excel_path
        self.blacklist = blacklist or []
        
        # 如果文件不存在，创建默认的合约信息
        if not os.path.exists(excel_path):
            self.create_default_excel()
            
        self.df = pd.read_excel(excel_path)
        
    def create_default_excel(self):
        """创建默认的合约信息Excel文件"""
        # 创建默认的合约信息
        data = {
            '合约代码': [],
            '手续费率': [],  # 万分之几
            '合约乘数': [],
            '价格精度': []
        }
        
        df = pd.DataFrame(data)
        df.to_excel(self.excel_path, index=False)
        print(f"已创建默认合约信息文件: {self.excel_path}")
        
    def get_exchange(self, symbol: str) -> str:
        """根据合约代码获取对应的交易所
        
        Args:
            symbol: 合约代码
            
        Returns:
            str: 交易所代码
        """
        if not symbol:
            return "CFFEX"  # 默认返回中金所
        
        # 先尝试单字母品种代码
        if symbol[0].isalpha():
            product = symbol[0].upper()
            if product in self.EXCHANGE_MAP:
                return self.EXCHANGE_MAP[product]
        
        # 再尝试双字母品种代码
        if len(symbol) >= 2:
            product = symbol[:2].upper()
            if product in self.EXCHANGE_MAP:
                return self.EXCHANGE_MAP[product]
        
        return "CFFEX"  # 默认返回中金所
    
    def format_product_code(self, product: str, exchange: str) -> str:
        """根据交易所格式化品种代码大小写
        
        Args:
            product: 品种代码
            exchange: 交易所代码
            
        Returns:
            str: 格式化后的品种代码
        """
        if exchange in self.UPPERCASE_EXCHANGES:
            return product.upper()
        else:
            return product.lower()
        
    def get_888_contracts(self) -> List[str]:
        """获取所有888合约代码列表"""
        contracts = []
        for symbol in self.df['证券代码']:  # 使用'证券代码'列
            if isinstance(symbol, str) and len(symbol) >= 2:
                # 先尝试双字母品种代码
                product_code = symbol[:2].upper()
                if product_code in self.EXCHANGE_MAP:
                    exchange = self.EXCHANGE_MAP[product_code]
                # 如果双字母不在映射中，且第一个字符是字母，则尝试单字母品种代码
                elif symbol[0].isalpha():
                    product_code = symbol[0].upper()
                    if product_code in self.EXCHANGE_MAP:
                        exchange = self.EXCHANGE_MAP[product_code]
                    else:
                        continue
                else:
                    continue
                
                # 根据交易所格式化品种代码大小写
                formatted_product = self.format_product_code(product_code, exchange)
                
                contract = f"{formatted_product}888.{exchange}"
                # 检查是否在黑名单中
                if contract not in self.blacklist and contract not in contracts:  # 避免重复
                    contracts.append(contract)
        return contracts
    
    def get_contract_params(self) -> Dict:
        """获取所有合约的参数信息
        
        Returns:
            Dict: 包含rate、size、pricetick等参数的字典
        """
        params = {
            'rate': {},
            'size': {},
            'pricetick': {}
        }
        
        for _, row in self.df.iterrows():
            symbol = str(row['证券代码'])
            if not symbol:
                continue
                
            # 获取品种代码
            product = None
            # 先尝试双字母品种代码
            if len(symbol) >= 2:
                product = symbol[:2].upper()
                if product in self.EXCHANGE_MAP:
                    exchange = self.EXCHANGE_MAP[product]
                else:
                    # 如果双字母不在映射中，且第一个字符是字母，则尝试单字母品种代码
                    if symbol[0].isalpha():
                        product = symbol[0].upper()
                        if product in self.EXCHANGE_MAP:
                            exchange = self.EXCHANGE_MAP[product]
                        else:
                            continue
                    else:
                        continue
            else:
                continue
                
            if product and product.isalpha():  # 确保是字母开头的合约
                # 根据交易所格式化品种代码大小写
                formatted_product = self.format_product_code(product, exchange)
                contract = f"{formatted_product}888.{exchange}"
                
                if contract not in params['rate']:
                    size = float(row['合约乘数']) if '合约乘数' in row else 1  # 默认值
                    params['size'][contract] = size

                    pricetick = float(row['最小变动价位']) if '最小变动价位' in row else 1  # 默认值
                    params['pricetick'][contract] = pricetick

                    # 获取收盘价，如果没有则使用默认值1000
                    close_price = float(row['收盘价']) if '收盘价' in row else 1000
                    
                    # 计算成交金额
                    trade_amount = close_price * size
                    
                    # 获取交易手续费和平今手续费
                    trade_fee = float(row['期货交易手续费']) if '期货交易手续费' in row else 0.0002
                    close_today_fee = float(row['期货平今手续费']) if '期货平今手续费' in row else 0.0002
                    
                    # 如果手续费大于0.01，说明是固定金额，需要转换为比例
                    if trade_fee > 0.01:
                        trade_fee = trade_fee / trade_amount
                    if close_today_fee > 0.01:
                        close_today_fee = close_today_fee / trade_amount
                    
                    # 取两种手续费中的最大值
                    rate = max(trade_fee, close_today_fee)
                    params['rate'][contract] = rate
        
        return params 