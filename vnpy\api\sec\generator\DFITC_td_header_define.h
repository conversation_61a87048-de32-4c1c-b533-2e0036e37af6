#define ONFRONTCONNECTED 0
#define ONFRONTDISCONNECTED 1
#define ONRTNNOTICE 2
#define ONRSPERROR 3
#define ONRSPSTOCKUSERLOGIN 4
#define ONRSPSTOCKUSERLOGOUT 5
#define ONRSPSTOCKUSERPASSWORDUPDATE 6
#define ONRSPSTOCKENTRUSTORDER 7
#define ONRSPSTOCKWITHDRAWORDER 8
#define ONRSPSTOCKQRYENTRUSTORDER 9
#define ONRSPSTOCKQRYREALTIMETRADE 10
#define ONRSPSTOCKQRYSERIALTRADE 11
#define ONRSPSTOCKQRYPOSITION 12
#define ONRSPSTOCKQRYCAPITALACCOUNTINFO 13
#define ONRSPSTOCKQRYACCOUNTINFO 14
#define ONRSPSTOCKQRYSHAREHOLDERINFO 15
#define ONRSPSTOCKTRANSFERFUNDS 16
#define ONRSPSTOCKENTRUSTBATCHORDER 17
#define ONRSPSTOCKWITHDRAWBATCHORDER 18
#define ONRSPSTOCKCALCABLEENTRUSTQTY 19
#define ONRSPSTOCKCALCABLEPURCHASEETFQTY 20
#define ONRSPSTOCKQRYFREEZEFUNDSDETAIL 21
#define ONRSPSTOCKQRYFREEZESTOCKDETAIL 22
#define ONRSPSTOCKQRYTRANSFERSTOCKDETAIL 23
#define ONRSPSTOCKQRYTRANSFERFUNDSDETAIL 24
#define ONRSPSTOCKQRYSTOCKINFO 25
#define ONRSPSTOCKQRYSTOCKSTATICINFO 26
#define ONRSPSTOCKQRYTRADETIME 27
#define ONSTOCKENTRUSTORDERRTN 28
#define ONSTOCKTRADERTN 29
#define ONSTOCKWITHDRAWORDERRTN 30
#define ONRSPSOPUSERLOGIN 31
#define ONRSPSOPUSERLOGOUT 32
#define ONRSPSOPUSERPASSWORDUPDATE 33
#define ONRSPSOPENTRUSTORDER 34
#define ONRSPSOPQUOTEENTRUSTORDER 35
#define ONRSPSOPGROUPSPLIT 36
#define ONRSPSOPGROUPEXECTUEORDER 37
#define ONRSPSOPQRYGROUPPOSITION 38
#define ONRSPSOPLOCKOUNLOCKSTOCK 39
#define ONRSPSOPWITHDRAWORDER 40
#define ONRSPSOPQRYENTRUSTORDER 41
#define ONRSPSOPQRYSERIALTRADE 42
#define ONRSPSOPQRYPOSITION 43
#define ONRSPSOPQRYCOLLATERALPOSITION 44
#define ONRSPSOPQRYCAPITALACCOUNTINFO 45
#define ONRSPSOPQRYACCOUNTINFO 46
#define ONRSPSOPQRYSHAREHOLDERINFO 47
#define ONRSPSOPCALCABLEENTRUSTQTY 48
#define ONRSPSOPQRYABLELOCKSTOCK 49
#define ONRSPSOPQRYCONTACTINFO 50
#define ONRSPSOPEXECTUEORDER 51
#define ONRSPSOPQRYEXECASSIINFO 52
#define ONRSPSOPQRYTRADETIME 53
#define ONRSPSOPQRYEXCHANGEINFO 54
#define ONRSPSOPQRYCOMMISSION 55
#define ONRSPSOPQRYDEPOSIT 56
#define ONRSPSOPQRYCONTRACTOBJECTINFO 57
#define ONSOPENTRUSTORDERRTN 58
#define ONSOPTRADERTN 59
#define ONSOPWITHDRAWORDERRTN 60
#define ONRSPFASLUSERLOGIN 61
#define ONRSPFASLUSERLOGOUT 62
#define ONRSPFASLQRYABLEFININFO 63
#define ONRSPFASLQRYABLESLOINFO 64
#define ONRSPFASLTRANSFERCOLLATERAL 65
#define ONRSPFASLDIRECTREPAYMENT 66
#define ONRSPFASLREPAYSTOCKTRANSFER 67
#define ONRSPFASLENTRUSTCRDTORDER 68
#define ONRSPFASLENTRUSTORDER 69
#define ONRSPFASLCALCABLEENTRUSTCRDTQTY 70
#define ONRSPFASLQRYCRDTFUNDS 71
#define ONRSPFASLQRYCRDTCONTRACT 72
#define ONRSPFASLQRYCRDTCONCHANGEINFO 73
#define ONRSPFASLTRANSFERFUNDS 74
#define ONRSPFASLQRYACCOUNTINFO 75
#define ONRSPFASLQRYCAPITALACCOUNTINFO 76
#define ONRSPFASLQRYSHAREHOLDERINFO 77
#define ONRSPFASLQRYPOSITION 78
#define ONRSPFASLQRYENTRUSTORDER 79
#define ONRSPFASLQRYSERIALTRADE 80
#define ONRSPFASLQRYREALTIMETRADE 81
#define ONRSPFASLQRYFREEZEFUNDSDETAIL 82
#define ONRSPFASLQRYFREEZESTOCKDETAIL 83
#define ONRSPFASLQRYTRANSFERFUNDSDETAIL 84
#define ONRSPFASLWITHDRAWORDER 85
#define ONRSPFASLQRYSYSTEMTIME 86
#define ONRSPFASLQRYTRANSFERREDCONTRACT 87
#define ONRSPFASLDESIRABLEFUNDSOUT 88
#define ONRSPFASLQRYGUARANTEEDCONTRACT 89
#define ONRSPFASLQRYUNDERLYINGCONTRACT 90
#define ONFASLENTRUSTORDERRTN 91
#define ONFASLTRADERTN 92
#define ONFASLWITHDRAWORDERRTN 93
