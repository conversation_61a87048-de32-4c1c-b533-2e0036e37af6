{"success_count": 6, "failure_count": 0, "success_rate": 1.0, "performance_stats": {"mean": 3.899309103003372, "std": 0.03640555047309202, "min": 3.8629035525302804, "max": 3.9357146534764644, "median": 3.8993091030033726}, "stability_stats": {"mean": 0.9733511633106832, "std": 0.0007524087568676618, "min": 0.9725987545538155, "max": 0.9741035720675508, "median": 0.9733511633106832}, "time_stats": {"mean": 519.8491923809052, "std": 14.257083233709894, "total": 3119.095154285431}, "parameter_statistics": {"k_1": {"mean": 5.5, "std": 4.5, "min": 1, "max": 10, "median": 5.5}, "k_3": {"mean": 20.0, "std": 0.0, "min": 20, "max": 20, "median": 20.0}, "k_5": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "sl_multiplier": {"mean": 7.0825533792083135, "std": 1.9174466207916883, "min": 5.1651067584166235, "max": 9.0, "median": 7.082553379208312}, "macd_boll_count_fz": {"mean": 0.19999999999999998, "std": 2.7755575615628914e-17, "min": 0.2, "max": 0.2, "median": 0.2}, "dk_fz": {"mean": 0.9379888905704564, "std": 0.06201110942954352, "min": 0.875977781140913, "max": 1.0, "median": 0.9379888905704565}, "ping_zy": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "zy": {"mean": 60.0, "std": 0.0, "min": 60, "max": 60, "median": 60.0}, "AF": {"mean": 0.0014598536760574217, "std": 0.0005401463239425783, "min": 0.0009197073521148435, "max": 0.002, "median": 0.0014598536760574217}, "AF_max": {"mean": 0.11168866358137357, "std": 0.08831133641862646, "min": 0.023377327162747097, "max": 0.2, "median": 0.11168866358137355}, "trailing_start_ratio": {"mean": 0.9, "std": 0.0, "min": 0.9, "max": 0.9, "median": 0.9}, "daily_loss_limit": {"mean": 1618.0, "std": 618.0, "min": 1000, "max": 2236, "median": 1618.0}, "k_15": {"mean": 15.0, "std": 0.0, "min": 15, "max": 15, "median": 15.0}, "k_30": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "atr_window": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "donchian_period": {"mean": 20.0, "std": 0.0, "min": 20, "max": 20, "median": 20.0}, "lots": {"mean": 1.0, "std": 0.0, "min": 1, "max": 1, "median": 1.0}, "use_trailing_stop": {"mean": 1.0, "std": 0.0, "min": 1, "max": 1, "median": 1.0}, "contract_multiplier": {"mean": 10.833333333333334, "std": 8.858454843945541, "min": 5.0, "max": 30.0, "median": 7.5}}, "top_performers": [{"symbol": "PX888.CZCE", "best_performance": 3.9357146534764644, "stability_score": 0.9741035720675508}, {"symbol": "br888.SHFE", "best_performance": 3.9357146534764644, "stability_score": 0.9741035720675508}, {"symbol": "TA888.CZCE", "best_performance": 3.9357146534764644, "stability_score": 0.9741035720675508}, {"symbol": "SH888.CZCE", "best_performance": 3.8629035525302804, "stability_score": 0.9725987545538155}, {"symbol": "bu888.SHFE", "best_performance": 3.8629035525302804, "stability_score": 0.9725987545538155}], "most_stable": [{"symbol": "PX888.CZCE", "best_performance": 3.9357146534764644, "stability_score": 0.9741035720675508}, {"symbol": "br888.SHFE", "best_performance": 3.9357146534764644, "stability_score": 0.9741035720675508}, {"symbol": "TA888.CZCE", "best_performance": 3.9357146534764644, "stability_score": 0.9741035720675508}, {"symbol": "SH888.CZCE", "best_performance": 3.8629035525302804, "stability_score": 0.9725987545538155}, {"symbol": "bu888.SHFE", "best_performance": 3.8629035525302804, "stability_score": 0.9725987545538155}], "failed_symbols": []}