"""
智能期货交易系统主程序
Main Intelligent Futures Trading System

集成所有模块，提供完整的系统化解决方案：
1. 品种动态筛选
2. 参数自适应优化
3. 资金智能管理
4. 实时监控
5. 自动化交易决策
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime, timedelta
import json
import time
import threading

# 导入系统模块
from .core.instrument_selector import InstrumentSelector
from .core.parameter_optimizer import ParameterOptimizer
from .core.risk_manager import RiskManager, PositionInfo
from .core.data_manager import DataManager
from .core.monitor import SystemMonitor, PerformanceMetrics
from .core.backtest_engine import BacktestEngine
from .strategies.adaptive_trend_strategy import AdaptiveTrendStrategy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('intelligent_trading_system.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class IntelligentTradingSystem:
    """智能期货交易系统"""
    
    def __init__(self, account_value: float = 5000000, config: Optional[Dict] = None):
        """
        初始化智能交易系统
        
        Args:
            account_value: 账户净值（默认500万）
            config: 系统配置
        """
        self.account_value = account_value
        self.config = config or self._get_default_config()
        
        # 初始化各模块
        self.data_manager = DataManager(self.config['data_dir'])
        self.instrument_selector = InstrumentSelector(account_value)
        self.parameter_optimizer = ParameterOptimizer()
        self.risk_manager = RiskManager(account_value, self._get_contract_info())
        self.monitor = SystemMonitor(self.config['monitor_interval'])
        self.backtest_engine = BacktestEngine(account_value)
        
        # 策略实例
        self.strategy = AdaptiveTrendStrategy()
        
        # 系统状态
        self.is_running = False
        self.selected_instruments = []
        self.optimized_parameters = {}
        self.current_positions = {}
        self.system_thread = None
        
        # 性能统计
        self.performance_stats = {
            'start_time': datetime.now(),
            'total_signals': 0,
            'successful_trades': 0,
            'total_pnl': 0.0
        }
        
        logger.info(f"智能交易系统初始化完成，账户净值: {account_value:,.0f}")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'data_dir': 'data',
            'results_dir': 'results',
            'monitor_interval': 60,  # 监控间隔（秒）
            'optimization_interval': 7,  # 优化间隔（天）
            'selection_interval': 1,  # 品种筛选间隔（天）
            'max_instruments': 5,  # 最大品种数
            'lookback_days': 120,  # 回看天数
            'enable_auto_trading': False,  # 是否启用自动交易
            'risk_mode': 'conservative'  # 风险模式：conservative, balanced, aggressive
        }
    
    def _get_contract_info(self) -> Dict:
        """获取合约信息"""
        return {
            'rb888.SHFE': {'multiplier': 10, 'tick_size': 1, 'margin_rate': 0.08},
            'hc888.SHFE': {'multiplier': 10, 'tick_size': 1, 'margin_rate': 0.08},
            'i888.DCE': {'multiplier': 100, 'tick_size': 0.5, 'margin_rate': 0.08},
            'j888.DCE': {'multiplier': 100, 'tick_size': 0.5, 'margin_rate': 0.08},
            'cu888.SHFE': {'multiplier': 5, 'tick_size': 10, 'margin_rate': 0.07},
            'al888.SHFE': {'multiplier': 5, 'tick_size': 5, 'margin_rate': 0.07},
            'zn888.SHFE': {'multiplier': 5, 'tick_size': 5, 'margin_rate': 0.07},
            'au888.SHFE': {'multiplier': 1000, 'tick_size': 0.02, 'margin_rate': 0.06},
            'ag888.SHFE': {'multiplier': 15, 'tick_size': 1, 'margin_rate': 0.08},
            'sc888.INE': {'multiplier': 1000, 'tick_size': 0.1, 'margin_rate': 0.10},
            'TA888.CZCE': {'multiplier': 5, 'tick_size': 2, 'margin_rate': 0.06},
            'MA888.CZCE': {'multiplier': 10, 'tick_size': 1, 'margin_rate': 0.07},
            'PX888.CZCE': {'multiplier': 5, 'tick_size': 2, 'margin_rate': 0.06},
            'SH888.CZCE': {'multiplier': 5, 'tick_size': 2, 'margin_rate': 0.06},
            'm888.DCE': {'multiplier': 10, 'tick_size': 1, 'margin_rate': 0.05},
            'y888.DCE': {'multiplier': 10, 'tick_size': 2, 'margin_rate': 0.05},
            'p888.DCE': {'multiplier': 10, 'tick_size': 2, 'margin_rate': 0.05},
            'c888.DCE': {'multiplier': 10, 'tick_size': 1, 'margin_rate': 0.05}
        }
    
    def start_system(self):
        """启动系统"""
        if self.is_running:
            logger.warning("系统已在运行中")
            return
        
        logger.info("🚀 启动智能期货交易系统")
        
        # 启动监控
        self.monitor.start_monitoring()
        
        # 更新模块状态
        self.monitor.update_module_status('data_manager', 'running')
        self.monitor.update_module_status('instrument_selector', 'running')
        self.monitor.update_module_status('parameter_optimizer', 'running')
        self.monitor.update_module_status('risk_manager', 'running')
        self.monitor.update_module_status('strategy', 'running')
        
        # 启动主循环
        self.is_running = True
        self.system_thread = threading.Thread(target=self._main_loop, daemon=True)
        self.system_thread.start()
        
        logger.info("✅ 系统启动成功")
    
    def stop_system(self):
        """停止系统"""
        logger.info("🛑 停止智能期货交易系统")
        
        self.is_running = False
        
        # 停止监控
        self.monitor.stop_monitoring()
        
        # 等待主线程结束
        if self.system_thread:
            self.system_thread.join(timeout=10)
        
        logger.info("✅ 系统已停止")
    
    def _main_loop(self):
        """主循环"""
        last_selection_time = datetime.min
        last_optimization_time = datetime.min
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # 定期品种筛选
                if (current_time - last_selection_time).days >= self.config['selection_interval']:
                    self._run_instrument_selection()
                    last_selection_time = current_time
                
                # 定期参数优化
                if (current_time - last_optimization_time).days >= self.config['optimization_interval']:
                    self._run_parameter_optimization()
                    last_optimization_time = current_time
                
                # 实时风险监控
                self._run_risk_monitoring()
                
                # 更新系统状态
                self._update_system_status()
                
                # 等待下次循环
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"主循环异常: {e}")
                self.monitor.add_alert(f"主循环异常: {e}", 'critical')
                time.sleep(60)
    
    def _run_instrument_selection(self):
        """运行品种筛选"""
        try:
            logger.info("🔍 开始品种筛选")
            
            # 获取市场数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
            
            # 获取所有品种数据
            all_symbols = list(self._get_contract_info().keys())
            market_data = {}
            
            for symbol in all_symbols:
                try:
                    data = self.data_manager.get_historical_data(
                        symbol, start_date, end_date, 'daily', 'simulation'
                    )
                    if not data.empty:
                        market_data[symbol] = data
                except Exception as e:
                    logger.warning(f"获取 {symbol} 数据失败: {e}")
            
            # 执行品种筛选
            if market_data:
                metrics_list = self.instrument_selector.select_instruments(market_data)
                self.selected_instruments = [m.symbol for m in metrics_list if m.is_selected]
                
                logger.info(f"✅ 品种筛选完成，选中: {self.selected_instruments}")
                
                # 更新监控
                self.monitor.update_module_status(
                    'instrument_selector', 'running',
                    performance_metrics={'selected_count': len(self.selected_instruments)}
                )
            
        except Exception as e:
            logger.error(f"品种筛选失败: {e}")
            self.monitor.update_module_status('instrument_selector', 'error', str(e))
    
    def _run_parameter_optimization(self):
        """运行参数优化"""
        if not self.selected_instruments:
            logger.info("没有选中的品种，跳过参数优化")
            return
        
        try:
            logger.info("⚙️ 开始参数优化")
            
            # 获取历史数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=self.config['lookback_days'])).strftime('%Y-%m-%d')
            
            instruments_data = {}
            for symbol in self.selected_instruments:
                try:
                    data = self.data_manager.get_historical_data(
                        symbol, start_date, end_date, 'daily', 'simulation'
                    )
                    if not data.empty:
                        instruments_data[symbol] = data
                except Exception as e:
                    logger.warning(f"获取 {symbol} 优化数据失败: {e}")
            
            # 执行参数优化
            if instruments_data:
                optimization_results = self.parameter_optimizer.batch_optimize(
                    instruments_data, max_workers=4
                )
                
                # 更新策略参数
                for symbol, result in optimization_results.items():
                    if result.is_stable:
                        self.optimized_parameters[symbol] = result.best_params
                
                logger.info(f"✅ 参数优化完成，优化品种: {len(optimization_results)}")
                
                # 更新监控
                stable_count = sum(1 for r in optimization_results.values() if r.is_stable)
                self.monitor.update_module_status(
                    'parameter_optimizer', 'running',
                    performance_metrics={'optimized_count': stable_count}
                )
            
        except Exception as e:
            logger.error(f"参数优化失败: {e}")
            self.monitor.update_module_status('parameter_optimizer', 'error', str(e))
    
    def _run_risk_monitoring(self):
        """运行风险监控"""
        try:
            # 构建持仓信息
            positions = {}
            for symbol in self.selected_instruments:
                # 模拟持仓信息（实际应用中从交易系统获取）
                if symbol in self.optimized_parameters:
                    positions[symbol] = PositionInfo(
                        symbol=symbol,
                        current_lots=2,  # 模拟持仓
                        suggested_lots=3,
                        risk_amount=self.account_value * 0.02,
                        risk_percentage=0.02,
                        atr_value=100.0,
                        contract_value=50000.0
                    )
            
            # 计算风险指标
            risk_metrics = self.risk_manager.calculate_portfolio_risk(positions)
            
            # 检查风险限制
            risk_warnings = self.risk_manager.check_risk_limits(positions)
            
            # 处理风险警告
            for severity, warnings in risk_warnings.items():
                for warning in warnings:
                    self.monitor.add_alert(warning, severity, 'risk_manager')
            
            # 更新监控
            self.monitor.update_module_status(
                'risk_manager', 'running',
                performance_metrics={
                    'portfolio_risk': risk_metrics.portfolio_risk_percentage,
                    'max_single_risk': risk_metrics.max_single_risk,
                    'total_warnings': sum(len(w) for w in risk_warnings.values())
                }
            )
            
        except Exception as e:
            logger.error(f"风险监控失败: {e}")
            self.monitor.update_module_status('risk_manager', 'error', str(e))
    
    def _update_system_status(self):
        """更新系统状态"""
        try:
            # 计算性能指标
            current_time = datetime.now()
            uptime_hours = (current_time - self.performance_stats['start_time']).total_seconds() / 3600
            
            # 模拟性能数据（实际应用中从交易记录计算）
            performance = PerformanceMetrics(
                total_return=0.05,  # 5%总收益
                daily_return=0.001,  # 0.1%日收益
                sharpe_ratio=1.2,
                max_drawdown=0.03,
                win_rate=0.55,
                profit_factor=1.8,
                total_trades=self.performance_stats['total_signals'],
                current_positions=len(self.selected_instruments)
            )
            
            # 更新监控
            self.monitor.update_performance_metrics(performance)
            
        except Exception as e:
            logger.error(f"更新系统状态失败: {e}")
    
    def run_backtest(self, start_date: str, end_date: str) -> Dict:
        """
        运行回测
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            回测结果
        """
        logger.info(f"🔄 开始回测: {start_date} 到 {end_date}")
        
        try:
            # 获取回测数据
            symbols = self.selected_instruments or list(self._get_contract_info().keys())[:5]
            backtest_data = {}
            
            for symbol in symbols:
                data = self.data_manager.get_historical_data(
                    symbol, start_date, end_date, 'daily', 'simulation'
                )
                if not data.empty:
                    backtest_data[symbol] = data
                    
                    # 计算技术指标
                    self.strategy.calculate_indicators(data, symbol)
            
            # 运行回测
            if backtest_data:
                result = self.backtest_engine.run_backtest(
                    self.strategy, backtest_data, start_date, end_date
                )
                
                # 生成报告
                performance_summary = self.backtest_engine.get_performance_summary(result)
                
                logger.info(f"✅ 回测完成: 总收益={result.total_return:.2%}")
                
                return {
                    'result': result,
                    'summary': performance_summary,
                    'symbols': list(backtest_data.keys())
                }
            else:
                logger.warning("没有有效的回测数据")
                return {}
                
        except Exception as e:
            logger.error(f"回测失败: {e}")
            return {'error': str(e)}
    
    def get_system_report(self) -> Dict:
        """生成系统报告"""
        try:
            # 系统概览
            overview = self.monitor.get_system_overview()
            
            # 品种筛选报告
            selection_report = self.instrument_selector.get_selection_report()
            
            # 风险管理报告
            risk_report = self.risk_manager.get_risk_report()
            
            # 策略状态
            strategy_status = self.strategy.get_strategy_status()
            
            return {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'system_overview': overview,
                'instrument_selection': selection_report,
                'risk_management': risk_report,
                'strategy_status': strategy_status,
                'performance_stats': self.performance_stats,
                'config': self.config
            }
            
        except Exception as e:
            logger.error(f"生成系统报告失败: {e}")
            return {'error': str(e)}
    
    def save_system_report(self, filepath: Optional[str] = None):
        """保存系统报告"""
        if not filepath:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filepath = f"results/system_report_{timestamp}.json"
        
        try:
            report = self.get_system_report()
            
            # 确保目录存在
            import os
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"系统报告已保存到: {filepath}")
            
        except Exception as e:
            logger.error(f"保存系统报告失败: {e}")
    
    def update_account_value(self, new_value: float):
        """更新账户净值"""
        self.account_value = new_value
        self.instrument_selector.update_account_value(new_value)
        self.risk_manager.update_account_value(new_value)
        
        logger.info(f"账户净值已更新为: {new_value:,.0f}")
    
    def get_health_score(self) -> float:
        """获取系统健康评分"""
        return self.monitor.get_health_score()


def main():
    """主函数 - 系统使用示例"""
    # 创建系统实例
    system = IntelligentTradingSystem(account_value=5000000)
    
    try:
        # 启动系统
        system.start_system()
        
        # 运行一段时间
        print("系统运行中，按 Ctrl+C 停止...")
        
        # 等待用户中断
        while True:
            time.sleep(10)
            
            # 显示系统状态
            health_score = system.get_health_score()
            print(f"系统健康评分: {health_score:.1f}/100")
            
            # 每分钟保存一次报告
            if int(time.time()) % 60 == 0:
                system.save_system_report()
    
    except KeyboardInterrupt:
        print("\n用户中断，正在停止系统...")
    
    finally:
        # 停止系统
        system.stop_system()
        
        # 保存最终报告
        system.save_system_report()
        
        print("系统已安全停止")


if __name__ == "__main__":
    main()
