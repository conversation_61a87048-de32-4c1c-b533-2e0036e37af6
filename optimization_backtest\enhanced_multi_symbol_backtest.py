"""
增强版多品种回测引擎
整合参数优化回测流程，提供完整的回测分析功能
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional, Callable
import json
import pickle
from pathlib import Path

# 添加上级目录到sys.path，以便导入vnpy相关模块
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

from vnpy.app.cta_strategy.backtesting import BacktestingEngine, OptimizationSetting
from vnpy.trader.constant import Interval
from vnpy.trader.utility import extract_vt_symbol
from vnpy.trader.object import HistoryRequest
from vnpy.trader.constant import Exchange

# 导入本地模块
from optimization_engine import OptimizationEngine, OptimizationLogger


def backtest_function_adapter(params: Dict, 
                             backtester_instance,
                             strategy_class,
                             symbol: str,
                             target_name: str,
                             **kwargs) -> <PERSON><PERSON>[float, Dict]:
    """
    独立的回测函数适配器，用于多进程并行执行
    
    Args:
        params: 参数字典
        backtester_instance: 回测引擎实例
        strategy_class: 策略类
        symbol: 合约代码
        target_name: 优化目标
        **kwargs: 其他回测参数
        
    Returns:
        Tuple[float, Dict]: (目标值, 统计结果)
    """
    try:
        df, stats = backtester_instance.run_single_backtest(
            strategy_class=strategy_class,
            symbol=symbol,
            setting=params,
            **kwargs
        )
        
        target_value = stats.get(target_name, -999.0)
        return target_value, stats
        
    except Exception as e:
        print(f"回测失败，参数: {params}, 错误: {str(e)}")
        return -999.0, {}


class EnhancedMultiSymbolBacktest:
    """增强版多品种回测引擎"""
    
    def __init__(self, results_dir: str = None, max_workers: int = 4):
        """
        初始化
        
        Args:
            results_dir: 结果保存目录
            max_workers: 并行处理的最大工作进程数
        """
        # 设置结果目录
        if results_dir is None:
            self.results_dir = Path("optimization_backtest/results")
        else:
            self.results_dir = Path(results_dir)
        
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建日志文件
        log_file = self.results_dir / f"optimization_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        self.logger = OptimizationLogger(str(log_file))
        
        # 创建优化引擎
        self.optimization_engine = OptimizationEngine(str(log_file), max_workers)
        
        # 存储结果
        self.backtest_results = {}
        self.optimization_results = {}
        
        self.logger.info("增强版多品种回测引擎初始化完成")
        self.logger.info(f"结果保存目录: {self.results_dir}")
        self.logger.info(f"并行工作进程数: {max_workers}")
    
    def run_single_backtest(self,
                           strategy_class,
                           symbol: str,
                           interval: str,
                           start: datetime,
                           end: datetime,
                           rate: float,
                           slippage: float,
                           size: float,
                           pricetick: float,
                           capital: int,
                           setting: dict = None) -> Tuple[pd.DataFrame, Dict]:
        """
        运行单个合约的回测
        
        Args:
            strategy_class: 策略类
            symbol: 合约代码
            interval: K线周期
            start: 回测开始时间
            end: 回测结束时间
            rate: 手续费率
            slippage: 滑点
            size: 合约乘数
            pricetick: 价格精度
            capital: 初始资金
            setting: 策略参数
            
        Returns:
            Tuple[pd.DataFrame, Dict]: (回测结果DataFrame, 统计数据)
        """
        self.logger.info(f"开始回测合约: {symbol}")
        self.logger.info(f"回测参数: 资金={capital}, 费率={rate}, 滑点={slippage}")
        
        try:
            # 创建回测引擎实例
            engine = BacktestingEngine()
            
            # 设置回测参数
            engine.set_parameters(
                vt_symbol=symbol,
                interval=interval,
                start=start,
                end=end,
                rate=rate,
                slippage=slippage,
                size=size,
                pricetick=pricetick,
                capital=capital
            )
            
            # 添加策略
            engine.add_strategy(strategy_class, setting or {})
            
            # 加载数据
            engine.load_data()
            
            # 运行回测
            engine.run_backtesting()
            
            # 计算结果
            df = engine.calculate_result()
            stats = engine.calculate_statistics(output=False)
            
            self.logger.info(f"合约 {symbol} 回测完成")
            self.logger.info(f"总收益率: {stats.get('total_return', 0):.2%}")
            self.logger.info(f"夏普比率: {stats.get('sharpe_ratio', 0):.4f}")
            self.logger.info(f"最大回撤: {stats.get('max_drawdown', 0):.2%}")
            
            return df, stats
            
        except Exception as e:
            self.logger.error(f"合约 {symbol} 回测失败: {str(e)}")
            return pd.DataFrame(), {}
    
    def run_batch_backtest(self,
                          strategy_class,
                          symbols: List[str],
                          interval: str,
                          start: datetime,
                          end: datetime,
                          rate: Dict[str, float],
                          slippage: float,
                          size: Dict[str, float],
                          pricetick: Dict[str, float],
                          capital: int,
                          setting: dict = None) -> Dict[str, Tuple[pd.DataFrame, Dict]]:
        """
        运行多合约批量回测
        
        Args:
            strategy_class: 策略类
            symbols: 合约代码列表
            interval: K线周期
            start: 回测开始时间
            end: 回测结束时间
            rate: 手续费率字典
            slippage: 滑点
            size: 合约乘数字典
            pricetick: 价格精度字典
            capital: 初始资金
            setting: 策略参数
            
        Returns:
            Dict[str, Tuple[pd.DataFrame, Dict]]: {合约代码: (结果DataFrame, 统计数据)}
        """
        self.logger.info("=" * 60)
        self.logger.info("开始批量回测")
        self.logger.info(f"合约数量: {len(symbols)}")
        self.logger.info(f"回测期间: {start} - {end}")
        self.logger.info("=" * 60)
        
        batch_results = {}
        
        for i, symbol in enumerate(symbols, 1):
            self.logger.info(f"进度: {i}/{len(symbols)} - 正在回测 {symbol}")
            
            # 获取该合约的参数
            symbol_rate = rate.get(symbol, 0.0002)
            symbol_size = size.get(symbol, 300)
            symbol_pricetick = pricetick.get(symbol, 0.2)
            
            # 运行单合约回测
            df, stats = self.run_single_backtest(
                strategy_class=strategy_class,
                symbol=symbol,
                interval=interval,
                start=start,
                end=end,
                rate=symbol_rate,
                slippage=slippage,
                size=symbol_size,
                pricetick=symbol_pricetick,
                capital=capital,
                setting=setting
            )
            
            batch_results[symbol] = (df, stats)
            
            # 保存单个合约的结果
            self._save_single_result(symbol, df, stats)
        
        # 保存批量结果汇总
        self._save_batch_summary(batch_results)
        
        self.logger.info("批量回测完成")
        return batch_results
    
    def run_optimization_for_symbol(self,
                                   strategy_class,
                                   symbol: str,
                                   param_spaces: Dict[str, Tuple],
                                   interval: str,
                                   start: datetime,
                                   end: datetime,
                                   rate: float,
                                   slippage: float,
                                   size: float,
                                   pricetick: float,
                                   capital: int,
                                   target_name: str = 'sharpe_ratio') -> Dict:
        """
        为单个合约运行参数优化
        
        Args:
            strategy_class: 策略类
            symbol: 合约代码
            param_spaces: 参数空间定义
            interval: K线周期
            start: 回测开始时间
            end: 回测结束时间
            rate: 手续费率
            slippage: 滑点
            size: 合约乘数
            pricetick: 价格精度
            capital: 初始资金
            target_name: 优化目标
            
        Returns:
            Dict: 优化结果
        """
        self.logger.info("=" * 60)
        self.logger.info(f"开始为合约 {symbol} 进行参数优化")
        self.logger.info(f"参数空间维度: {len(param_spaces)}")
        self.logger.info("=" * 60)
        
        # 创建偏函数，固定其他参数
        from functools import partial
        
        backtest_func = partial(
            backtest_function_adapter,
            backtester_instance=self,
            strategy_class=strategy_class,
            symbol=symbol,
            target_name=target_name,
            interval=interval,
            start=start,
            end=end,
            rate=rate,
            slippage=slippage,
            size=size,
            pricetick=pricetick,
            capital=capital
        )
        
        # 运行优化工作流程
        optimization_result = self.optimization_engine.run_optimization_workflow(
            param_spaces=param_spaces,
            backtest_func=backtest_func,
            target_name=target_name,
            interval=interval,
            start=start,
            end=end,
            rate=rate,
            slippage=slippage,
            size=size,
            pricetick=pricetick,
            capital=capital
        )
        
        # 保存优化结果
        self._save_optimization_result(symbol, optimization_result)
        
        self.logger.info(f"合约 {symbol} 参数优化完成")
        return optimization_result
    
    def run_batch_optimization_with_dynamic_params(self,
                                                  strategy_class,
                                                  symbols: List[str],
                                                  param_spaces_generator: Callable,
                                                  interval: str,
                                                  start: datetime,
                                                  end: datetime,
                                                  rate: Dict[str, float],
                                                  slippage: float,
                                                  size: Dict[str, float],
                                                  pricetick: Dict[str, float],
                                                  capital: int,
                                                  target_name: str = 'sharpe_ratio') -> Dict[str, Dict]:
        """
        为多个合约运行批量参数优化（使用动态参数空间生成）
        
        Args:
            strategy_class: 策略类
            symbols: 合约代码列表
            param_spaces_generator: 参数空间生成函数，接受contract_size参数
            interval: K线周期
            start: 回测开始时间
            end: 回测结束时间
            rate: 手续费率字典
            slippage: 滑点
            size: 合约乘数字典
            pricetick: 价格精度字典
            capital: 初始资金
            target_name: 优化目标
            
        Returns:
            Dict[str, Dict]: {合约代码: 优化结果}
        """
        self.logger.info("=" * 80)
        self.logger.info("开始批量参数优化（动态参数空间）")
        self.logger.info(f"合约数量: {len(symbols)}")
        self.logger.info(f"优化目标: {target_name}")
        self.logger.info("=" * 80)
        
        batch_optimization_results = {}
        
        for i, symbol in enumerate(symbols, 1):
            self.logger.info(f"\n合约优化进度: {i}/{len(symbols)}")
            
            # 获取该合约的参数
            symbol_rate = rate.get(symbol, 0.0002)
            symbol_size = size.get(symbol, 300)
            symbol_pricetick = pricetick.get(symbol, 0.2)
            
            self.logger.info(f"合约: {symbol}")
            self.logger.info(f"  合约乘数: {symbol_size}")
            self.logger.info(f"  价格精度: {symbol_pricetick}")
            self.logger.info(f"  手续费率: {symbol_rate}")
            
            # 使用参数空间生成函数，传入合约大小
            param_spaces = param_spaces_generator(contract_size=symbol_size)
            
            # 记录contract_multiplier的设置
            contract_multiplier_value = param_spaces.get('contract_multiplier', (1, 1, 1))[0]
            self.logger.info(f"  contract_multiplier设置为: {contract_multiplier_value}")
            
            # 调整其他价格相关参数
            adjusted_param_spaces = self._adjust_param_spaces_for_symbol(
                param_spaces, symbol_size, symbol_pricetick
            )
            
            # 运行单合约优化
            try:
                optimization_result = self.run_optimization_for_symbol(
                    strategy_class=strategy_class,
                    symbol=symbol,
                    param_spaces=adjusted_param_spaces,
                    interval=interval,
                    start=start,
                    end=end,
                    rate=symbol_rate,
                    slippage=slippage,
                    size=symbol_size,
                    pricetick=symbol_pricetick,
                    capital=capital,
                    target_name=target_name
                )
                
                batch_optimization_results[symbol] = optimization_result
                
            except Exception as e:
                self.logger.error(f"合约 {symbol} 优化失败: {str(e)}")
                batch_optimization_results[symbol] = {
                    'error': str(e),
                    'best_params': {},
                    'best_target_value': -999.0
                }
        
        # 保存批量优化结果汇总
        self._save_batch_optimization_summary(batch_optimization_results)
        
        self.logger.info("=" * 80)
        self.logger.info("批量参数优化完成")
        self.logger.info("=" * 80)
        
        return batch_optimization_results
    
    def _adjust_param_spaces_for_symbol(self,
                                       param_spaces: Dict[str, Tuple],
                                       symbol_size: float,
                                       symbol_pricetick: float) -> Dict[str, Tuple]:
        """
        根据合约特性调整参数空间
        
        Args:
            param_spaces: 原始参数空间
            symbol_size: 合约乘数
            symbol_pricetick: 价格精度
            
        Returns:
            Dict[str, Tuple]: 调整后的参数空间
        """
        adjusted_spaces = param_spaces.copy()
        
        # 需要乘以pricetick的参数
        price_related_params = ["ping_zy", "zy", "stop_loss_points", "take_profit_points"]
        
        # 需要乘以size的参数（移除contract_multiplier，因为它已经在生成时设置正确）
        size_related_params = ["position_size"]
        
        self.logger.info(f"调整参数空间，pricetick={symbol_pricetick}, size={symbol_size}")
        
        for param_name, space_def in param_spaces.items():
            if param_name in price_related_params:
                # 价格相关参数乘以pricetick
                min_val, max_val = space_def[:2]
                step = space_def[2] if len(space_def) > 2 else None
                
                adjusted_min = min_val * symbol_pricetick
                adjusted_max = max_val * symbol_pricetick
                adjusted_step = step * symbol_pricetick if step else None
                
                if adjusted_step:
                    adjusted_spaces[param_name] = (adjusted_min, adjusted_max, adjusted_step)
                else:
                    adjusted_spaces[param_name] = (adjusted_min, adjusted_max)
                
                self.logger.info(f"  调整价格参数 {param_name}: {space_def} -> {adjusted_spaces[param_name]}")
                    
            elif param_name in size_related_params:
                # 合约乘数相关参数乘以size
                min_val, max_val = space_def[:2]
                step = space_def[2] if len(space_def) > 2 else None
                
                adjusted_min = min_val * symbol_size
                adjusted_max = max_val * symbol_size
                adjusted_step = step * symbol_size if step else None
                
                if adjusted_step:
                    adjusted_spaces[param_name] = (adjusted_min, adjusted_max, adjusted_step)
                else:
                    adjusted_spaces[param_name] = (adjusted_min, adjusted_max)
                
                self.logger.info(f"  调整合约参数 {param_name}: {space_def} -> {adjusted_spaces[param_name]}")
            elif param_name == 'contract_multiplier':
                # contract_multiplier已经在生成时设置为正确值，无需调整
                self.logger.info(f"  保持contract_multiplier参数: {space_def}")
        
        return adjusted_spaces
    
    def _save_single_result(self, symbol: str, df: pd.DataFrame, stats: Dict):
        """保存单个合约的回测结果"""
        try:
            symbol_dir = self.results_dir / "single_backtests" / symbol
            symbol_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存交易记录
            if not df.empty:
                trades_file = symbol_dir / f"{symbol}_trades_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df.to_csv(trades_file, encoding='utf-8-sig', index=False)
            
            # 保存统计结果
            stats_file = symbol_dir / f"{symbol}_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            self.logger.error(f"保存合约 {symbol} 结果失败: {str(e)}")
    
    def _save_batch_summary(self, batch_results: Dict[str, Tuple[pd.DataFrame, Dict]]):
        """保存批量回测结果汇总"""
        try:
            summary_data = []
            
            for symbol, (df, stats) in batch_results.items():
                summary_row = {
                    'symbol': symbol,
                    'total_trades': len(df) if not df.empty else 0,
                    **stats
                }
                summary_data.append(summary_row)
            
            summary_df = pd.DataFrame(summary_data)
            
            # 保存汇总表
            summary_file = self.results_dir / f"batch_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            summary_df.to_csv(summary_file, encoding='utf-8-sig', index=False)
            
            self.logger.info(f"批量回测汇总已保存: {summary_file}")
            
        except Exception as e:
            self.logger.error(f"保存批量回测汇总失败: {str(e)}")
    
    def _save_optimization_result(self, symbol: str, optimization_result: Dict):
        """保存单个合约的优化结果"""
        try:
            opt_dir = self.results_dir / "optimizations" / symbol
            opt_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存完整优化结果
            result_file = opt_dir / f"{symbol}_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
            with open(result_file, 'wb') as f:
                pickle.dump(optimization_result, f)
            
            # 保存关键结果摘要
            summary = {
                'symbol': symbol,
                'best_params': optimization_result.get('best_params', {}),
                'best_target_value': optimization_result.get('best_target_value', -999),
                'total_time': optimization_result.get('total_time', 0),
                'timestamp': str(optimization_result.get('timestamp', datetime.now()))
            }
            
            summary_file = opt_dir / f"{symbol}_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
                
            self.logger.info(f"合约 {symbol} 优化结果已保存")
            
        except Exception as e:
            self.logger.error(f"保存合约 {symbol} 优化结果失败: {str(e)}")
    
    def _save_batch_optimization_summary(self, batch_optimization_results: Dict[str, Dict]):
        """保存批量优化结果汇总"""
        try:
            summary_data = []
            
            for symbol, result in batch_optimization_results.items():
                if 'error' in result:
                    summary_row = {
                        'symbol': symbol,
                        'status': 'failed',
                        'error': result['error'],
                        'best_target_value': -999.0
                    }
                else:
                    summary_row = {
                        'symbol': symbol,
                        'status': 'success',
                        'best_target_value': result.get('best_target_value', -999),
                        'total_time': result.get('total_time', 0),
                        'n_stages': len(result.get('optimization_history', [])),
                        'stability_score': result.get('validation_results', {}).get('sensitivity', {}).get('stability_score', 0)
                    }
                    
                    # 添加最佳参数
                    best_params = result.get('best_params', {})
                    for param_name, param_value in best_params.items():
                        summary_row[f'best_{param_name}'] = param_value
                
                summary_data.append(summary_row)
            
            summary_df = pd.DataFrame(summary_data)
            
            # 按目标值排序
            summary_df = summary_df.sort_values('best_target_value', ascending=False)
            
            # 保存汇总表
            summary_file = self.results_dir / f"optimization_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            summary_df.to_csv(summary_file, encoding='utf-8-sig', index=False)
            
            # 保存完整结果
            full_result_file = self.results_dir / f"full_optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
            with open(full_result_file, 'wb') as f:
                pickle.dump(batch_optimization_results, f)
            
            self.logger.info(f"批量优化汇总已保存: {summary_file}")
            
        except Exception as e:
            self.logger.error(f"保存批量优化汇总失败: {str(e)}")
    
    def load_optimization_results(self, result_file: str) -> Dict:
        """加载已保存的优化结果"""
        try:
            with open(result_file, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            self.logger.error(f"加载优化结果失败: {str(e)}")
            return {}
    
    def generate_report(self, symbols: List[str] = None) -> str:
        """生成回测报告"""
        try:
            report_lines = []
            report_lines.append("=" * 80)
            report_lines.append("多品种回测优化报告")
            report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_lines.append("=" * 80)
            
            # TODO: 实现详细的报告生成逻辑
            # 包括：
            # 1. 总体性能统计
            # 2. 各合约表现排名
            # 3. 参数分布分析
            # 4. 风险指标汇总
            # 5. 稳定性分析结果
            
            report_content = "\n".join(report_lines)
            
            # 保存报告
            report_file = self.results_dir / f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"报告已生成: {report_file}")
            return str(report_file)
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {str(e)}")
            return "" 