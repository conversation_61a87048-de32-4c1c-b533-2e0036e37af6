{"success_count": 3, "failure_count": 0, "success_rate": 1.0, "performance_stats": {"mean": 3.4331800660627647, "std": 0.0, "min": 3.4331800660627647, "max": 3.4331800660627647, "median": 3.4331800660627647}, "stability_stats": {"mean": 0.9478396264770135, "std": 0.0, "min": 0.9478396264770135, "max": 0.9478396264770135, "median": 0.9478396264770135}, "time_stats": {"mean": 0.7056380907694498, "std": 0.02932685276743715, "total": 2.1169142723083496}, "parameter_statistics": {"k_3": {"mean": 5.0, "std": 0.0, "min": 5, "max": 5, "median": 5.0}, "sl_multiplier": {"mean": 6.0, "std": 0.0, "min": 6.0, "max": 6.0, "median": 6.0}, "macd_boll_count_fz": {"mean": 0.20000000000000004, "std": 2.7755575615628914e-17, "min": 0.2, "max": 0.2, "median": 0.2}, "dk_fz": {"mean": 0.8000000000000002, "std": 1.1102230246251565e-16, "min": 0.8, "max": 0.8, "median": 0.8}, "ping_zy": {"mean": 0.0, "std": 0.0, "min": 0, "max": 0, "median": 0.0}, "AF": {"mean": 0.002, "std": 0.0, "min": 0.002, "max": 0.002, "median": 0.002}, "AF_max": {"mean": 0.08, "std": 0.0, "min": 0.08, "max": 0.08, "median": 0.08}, "trailing_start_ratio": {"mean": 0.20000000000000004, "std": 2.7755575615628914e-17, "min": 0.2, "max": 0.2, "median": 0.2}, "k_15": {"mean": 15.0, "std": 0.0, "min": 15, "max": 15, "median": 15.0}, "k_30": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "atr_window": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "donchian_period": {"mean": 20.0, "std": 0.0, "min": 20, "max": 20, "median": 20.0}, "lots": {"mean": 1.0, "std": 0.0, "min": 1, "max": 1, "median": 1.0}, "use_trailing_stop": {"mean": 1.0, "std": 0.0, "min": 1, "max": 1, "median": 1.0}, "contract_multiplier": {"mean": 5.0, "std": 0.0, "min": 5, "max": 5, "median": 5.0}}, "top_performers": [{"symbol": "TEST3", "best_performance": 3.4331800660627647, "stability_score": 0.9478396264770135}, {"symbol": "TEST2", "best_performance": 3.4331800660627647, "stability_score": 0.9478396264770135}, {"symbol": "TEST1", "best_performance": 3.4331800660627647, "stability_score": 0.9478396264770135}], "most_stable": [{"symbol": "TEST3", "best_performance": 3.4331800660627647, "stability_score": 0.9478396264770135}, {"symbol": "TEST2", "best_performance": 3.4331800660627647, "stability_score": 0.9478396264770135}, {"symbol": "TEST1", "best_performance": 3.4331800660627647, "stability_score": 0.9478396264770135}], "failed_symbols": []}