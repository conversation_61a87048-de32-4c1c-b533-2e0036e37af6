
"""
基于增强稳定性优化器的多品种参数优化系统
基于 optimization_example.py 改进，使用 EnhancedStabilityOptimizer 进行高质量参数优化

新增功能：
1. 增强稳定性优化器 - 三层稳定性验证机制
2. 参数重要性分析 - 自动识别关键参数
3. 自适应采样策略 - 基于重要性的智能采样
4. 鲁棒性验证 - 蒙特卡洛 + Walk-Forward 验证
5. 稳定性评分系统 - 综合稳定性指标

使用方法：
python enhanced_stability_optimization_example.py --demo      # 演示模式
python enhanced_stability_optimization_example.py --single   # 单品种优化
python enhanced_stability_optimization_example.py --batch    # 批量优化
python enhanced_stability_optimization_example.py --full     # 完整优化流程
"""

import sys
from pathlib import Path
from datetime import datetime
import warnings
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import json
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

warnings.filterwarnings('ignore')

# 添加路径
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

# 导入所需模块
try:
    from vnpy.app.cta_strategy.strategies.fenzhouqiplus_strategy import FenZhouQiPlusStrategy
    from contract_info import ContractInfo
    VNPY_AVAILABLE = True
except ImportError:
    VNPY_AVAILABLE = False
    print("警告: VnPy相关模块未找到，将使用模拟策略")

from enhanced_stability_optimizer import EnhancedStabilityOptimizer, StabilityMetrics, OptimizationResult

# 尝试导入多品种回测模块
try:
    from enhanced_multi_symbol_backtest import EnhancedMultiSymbolBacktest
    MULTI_SYMBOL_AVAILABLE = True
except ImportError:
    MULTI_SYMBOL_AVAILABLE = False
    print("注意: 多品种回测模块不可用，将使用独立的稳定性优化器")


def generate_enhanced_param_spaces(contract_size: float = 1) -> Dict[str, Tuple]:
    """
    生成增强稳定性优化的参数空间
    基于 optimization_example.py 中的参数配置，适配 EnhancedStabilityOptimizer
    
    Args:
        contract_size: 合约乘数大小
        
    Returns:
        Dict[str, Tuple]: 参数空间定义 {参数名: (最小值, 最大值, 步长, 类型)}
    """
    param_spaces = {
        # 基础K线参数（整数优化）
        'k_1': (1, 10, 1, int),  # 1分钟K线
        'k_3': (5, 20, 1, int),  # 3分钟K线
        'k_5': (10, 30, 2, int),  # 5分钟K线
        
        # 核心参数（浮点数优化）
        'sl_multiplier': (1.5, 10.0, 0.5, float),  # 止损倍数
        'macd_boll_count_fz': (0.08, 0.3, 0.02, float),  # MACD阈值
        'dk_fz': (0.7, 1.0, 0.05, float),  # 多空阈值
        'ping_zy': (0, 30, 1, int),  # 保本点
        'zy': (5, 100, 5, int),  # 部分止盈点
        
        # 追踪止损参数
        'AF': (0.0002, 0.002, 0.0002, float),  # 加速因子
        'AF_max': (0.01, 0.2, 0.02, float),  # 最大加速
        'trailing_start_ratio': (0.3, 0.9, 0.2, float),  # 启动比例
        
        # 风控参数
        'daily_loss_limit': (1000, 3000, 500, int),  # 日损限额
    }
    
    return param_spaces


# 定义可序列化的约束函数（避免lambda函数序列化问题）
def constraint_ping_zy_less_than_zy(params: Dict[str, Any]) -> bool:
    """保本点必须小于止盈点"""
    return params.get('ping_zy', 0) < params.get('zy', float('inf'))

def constraint_af_less_than_af_max(params: Dict[str, Any]) -> bool:
    """加速因子必须小于最大加速因子"""
    return params.get('AF', 0) < params.get('AF_max', float('inf'))

def constraint_sl_multiplier_reasonable(params: Dict[str, Any]) -> bool:
    """止损倍数应该在合理范围内"""
    return params.get('sl_multiplier', 2.0) >= 1.0

def constraint_trailing_start_ratio_valid(params: Dict[str, Any]) -> bool:
    """启动比例应该在0-1之间"""
    return 0 < params.get('trailing_start_ratio', 0.5) < 1

def constraint_k_periods_logical(params: Dict[str, Any]) -> bool:
    """K线周期参数的合理性检查"""
    return (params.get('k_1', 1) <= params.get('k_3', 3) and
            params.get('k_3', 3) <= params.get('k_5', 5))

def constraint_stop_profit_gap(params: Dict[str, Any]) -> bool:
    """止盈参数应该有合理的差距"""
    return params.get('zy', 15) >= params.get('ping_zy', 5) + 5

# 全局约束函数映射（可序列化）
CONSTRAINT_FUNCTIONS = {
    'ping_zy_less_than_zy': constraint_ping_zy_less_than_zy,
    'AF_less_than_AF_max': constraint_af_less_than_af_max,
    'sl_multiplier_reasonable': constraint_sl_multiplier_reasonable,
    'trailing_start_ratio_valid': constraint_trailing_start_ratio_valid,
    'k_periods_logical': constraint_k_periods_logical,
    'stop_profit_gap': constraint_stop_profit_gap,
}

def get_enhanced_parameter_constraints() -> Dict[str, callable]:
    """
    定义增强参数约束关系

    Returns:
        Dict[str, callable]: 约束函数字典
    """
    return CONSTRAINT_FUNCTIONS.copy()


def clamp_to_bounds(value: float, min_val: float, max_val: float) -> float:
    """将值约束到指定边界内"""
    return max(min_val, min(max_val, value))

def align_to_step(value: float, step: float, param_type: type, min_val: float, max_val: float):
    """将值对齐到步长并确保在边界内"""
    if param_type == int:
        aligned_value = int(round(value / step) * step)
        return max(int(min_val), min(int(max_val), aligned_value))
    else:
        aligned_value = round(value / step) * step
        return max(min_val, min(max_val, aligned_value))

def apply_bounds_constraints(params: Dict, param_spaces: Dict[str, Tuple]) -> Dict:
    """应用基本边界约束"""
    constrained_params = params.copy()
    for param_name, value in constrained_params.items():
        if param_name in param_spaces:
            min_val, max_val, step, param_type = param_spaces[param_name]
            constrained_params[param_name] = clamp_to_bounds(value, min_val, max_val)
    return constrained_params

def fix_ping_zy_constraint(params: Dict, param_spaces: Dict[str, Tuple]) -> Dict:
    """修复保本点和止盈点约束"""
    ping_zy = params.get('ping_zy', 10)
    zy = params.get('zy', 20)

    if ping_zy >= zy:
        # 优先调整保本点
        if 'ping_zy' in param_spaces:
            min_val, max_val, step, param_type = param_spaces['ping_zy']
            new_ping_zy = clamp_to_bounds(min(ping_zy, zy - 5), min_val, max_val)
            params['ping_zy'] = new_ping_zy

            # 如果调整后仍不满足，则调整止盈点
            if new_ping_zy >= zy and 'zy' in param_spaces:
                min_val, max_val, step, param_type = param_spaces['zy']
                new_zy = clamp_to_bounds(max(new_ping_zy + 5, min_val), min_val, max_val)
                params['zy'] = new_zy

    return params

def fix_af_constraint(params: Dict, param_spaces: Dict[str, Tuple]) -> Dict:
    """修复加速因子约束"""
    af = params.get('AF', 0.001)
    af_max = params.get('AF_max', 0.15)

    if af >= af_max and 'AF' in param_spaces:
        min_val, max_val, step, param_type = param_spaces['AF']
        new_af = min(af, af_max * 0.8)
        params['AF'] = clamp_to_bounds(new_af, min_val, max_val)

    return params

def fix_k_periods_constraint(params: Dict, param_spaces: Dict[str, Tuple]) -> Dict:
    """修复K线周期约束"""
    k1 = params.get('k_1', 1)
    k3 = params.get('k_3', 5)
    k5 = params.get('k_5', 8)

    if k1 > k3 and 'k_3' in param_spaces:
        min_val, max_val, step, param_type = param_spaces['k_3']
        params['k_3'] = clamp_to_bounds(max(k1, min_val), min_val, max_val)
        k3 = params['k_3']  # 更新k3值

    if k3 > k5 and 'k_5' in param_spaces:
        min_val, max_val, step, param_type = param_spaces['k_5']
        params['k_5'] = clamp_to_bounds(max(k3, min_val), min_val, max_val)

    return params

# 约束修复函数映射
CONSTRAINT_FIXERS = {
    'ping_zy_less_than_zy': fix_ping_zy_constraint,
    'stop_profit_gap': fix_ping_zy_constraint,  # 使用相同的修复逻辑
    'AF_less_than_AF_max': fix_af_constraint,
    'k_periods_logical': fix_k_periods_constraint,
}

def apply_enhanced_parameter_constraints(params: Dict, param_spaces: Dict[str, Tuple]) -> Dict:
    """
    应用增强参数约束，自动调整参数以满足约束关系

    Args:
        params: 原始参数字典
        param_spaces: 参数空间定义

    Returns:
        Dict: 调整后满足约束的参数字典
    """
    # 首先应用基本边界约束
    constrained_params = apply_bounds_constraints(params, param_spaces)

    max_iterations = 10
    constraints = CONSTRAINT_FUNCTIONS  # 直接使用全局约束函数

    for iteration in range(max_iterations):
        all_constraints_satisfied = True

        for constraint_name, constraint_func in constraints.items():
            if not constraint_func(constrained_params):
                all_constraints_satisfied = False

                # 使用对应的修复函数
                if constraint_name in CONSTRAINT_FIXERS:
                    constrained_params = CONSTRAINT_FIXERS[constraint_name](
                        constrained_params, param_spaces
                    )

        # 每次迭代后重新应用边界约束
        constrained_params = apply_bounds_constraints(constrained_params, param_spaces)

        if all_constraints_satisfied:
            break

    # 最终步长对齐和边界检查
    final_params = {}
    for param_name, value in constrained_params.items():
        if param_name in param_spaces:
            min_val, max_val, step, param_type = param_spaces[param_name]
            final_params[param_name] = align_to_step(value, step, param_type, min_val, max_val)
        else:
            final_params[param_name] = value

    return final_params


def get_fixed_parameters(contract_size: float = 1) -> Dict[str, Any]:
    """
    获取固定参数值
    
    Args:
        contract_size: 合约大小
        
    Returns:
        Dict: 固定参数字典
    """
    return {
        # K线参数 (固定)
        'k_15': 15,
        'k_30': 30,
        
        # 技术指标参数
        'atr_window': 30,
        'donchian_period': 20,
        
        # 交易参数
        'lots': 1,
        'use_trailing_stop': 1,
        'contract_multiplier': contract_size,
    }


class EnhancedBacktester:
    """
    增强回测器类 - 使用真实VnPy回测引擎，支持快速模式
    """
    def __init__(self, symbol: str, contract_size: float, rate: float = 0.0002,
                 pricetick: float = 1.0, strategy_class=None,
                 start_date: str = "20220101", end_date: str = "20231231",
                 fast_mode: bool = False, data_cache: Optional[Dict] = None):
        self.symbol = symbol
        self.contract_size = contract_size
        self.rate = rate
        self.pricetick = pricetick
        self.strategy_class = strategy_class
        self.start_date = start_date
        self.end_date = end_date
        self.fast_mode = fast_mode
        self.data_cache = data_cache or {}

        # 快速模式配置
        if fast_mode:
            # 缩短回测周期用于快速筛选
            from datetime import datetime, timedelta
            start_dt = datetime.strptime(start_date, "%Y%m%d")
            end_dt = datetime.strptime(end_date, "%Y%m%d")
            total_days = (end_dt - start_dt).days

            # 快速模式使用30%的数据
            fast_days = max(30, int(total_days * 0.3))
            self.fast_end_date = (start_dt + timedelta(days=fast_days)).strftime("%Y%m%d")
        else:
            self.fast_end_date = end_date
        
    def __call__(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行回测
        
        Args:
            params: 参数字典
            
        Returns:
            Dict: 回测结果
        """
        # 获取参数空间和固定参数
        param_spaces = generate_enhanced_param_spaces(self.contract_size)
        fixed_params = get_fixed_parameters(self.contract_size)
        
        # 应用参数约束
        constrained_params = apply_enhanced_parameter_constraints(params, param_spaces)
        
        # 合并固定参数
        full_params = {**constrained_params, **fixed_params}
        
        # 选择回测模式
        actual_end_date = self.fast_end_date if self.fast_mode else self.end_date

        # 使用真实回测引擎
        if self.strategy_class and VNPY_AVAILABLE and not self.fast_mode:
            try:
                return self._run_real_backtest(full_params, actual_end_date)
            except (ImportError, ModuleNotFoundError) as e:
                if not self.fast_mode:
                    print(f"VnPy模块导入失败，使用模拟回测: {e}")
                return simulate_backtest_with_constraints(
                    full_params, self.symbol, self.contract_size, self.rate,
                    self.start_date, actual_end_date
                )
            except (ValueError, KeyError) as e:
                if not self.fast_mode:
                    print(f"参数错误，使用模拟回测: {e}")
                return simulate_backtest_with_constraints(
                    full_params, self.symbol, self.contract_size, self.rate,
                    self.start_date, actual_end_date
                )
            except FileNotFoundError as e:
                if not self.fast_mode:
                    print(f"数据文件未找到，使用模拟回测: {e}")
                return simulate_backtest_with_constraints(
                    full_params, self.symbol, self.contract_size, self.rate,
                    self.start_date, actual_end_date
                )
            except Exception as e:
                if not self.fast_mode:
                    print(f"回测引擎未知错误，使用模拟回测: {type(e).__name__}: {e}")
                return simulate_backtest_with_constraints(
                    full_params, self.symbol, self.contract_size, self.rate,
                    self.start_date, actual_end_date
                )
        else:
            # 使用模拟回测（快速模式或无策略类）
            return simulate_backtest_with_constraints(
                full_params, self.symbol, self.contract_size, self.rate,
                self.start_date, actual_end_date
            )
    
    def _run_real_backtest(self, params: Dict[str, Any], end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        运行真实VnPy回测

        Args:
            params: 参数字典
            end_date: 结束日期，如果为None则使用默认结束日期

        Returns:
            Dict: 回测结果
        """
        from vnpy.app.cta_strategy.backtesting import BacktestingEngine
        from vnpy.trader.constant import Interval
        from datetime import datetime

        # 创建回测引擎
        engine = BacktestingEngine()

        # 转换日期格式
        start_dt = datetime.strptime(self.start_date, "%Y%m%d")
        actual_end_date = end_date or self.end_date
        end_dt = datetime.strptime(actual_end_date, "%Y%m%d")
        
        # 设置回测参数
        engine.set_parameters(
            vt_symbol=self.symbol,
            interval=Interval.MINUTE,  # 使用分钟级数据
            start=start_dt,
            end=end_dt,
            rate=self.rate,
            slippage=0.0,  # 滑点设为0，在手续费中体现
            size=self.contract_size,
            pricetick=self.pricetick,
            capital=1000000,  # 100万初始资金
            inverse=False
        )
        
        # 添加策略
        engine.add_strategy(self.strategy_class, params)
        
        # 加载数据并运行回测
        engine.load_data()
        engine.run_backtesting()
        
        # 计算结果
        daily_df = engine.calculate_result()
        statistics = engine.calculate_statistics(output=False)
        
        # 处理回测结果
        if daily_df is None or daily_df.empty:
            return {
                'total_return': 0.0,
                'annual_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'volatility': 0.0,
                'win_rate': 0.0,
                'profit_loss_ratio': 1.0,
                'total_trades': 0,
                'profit_trades': 0,
                'error': 'No trading data generated'
            }
        
        # 提取关键指标
        total_return = statistics.get('total_return', 0.0)
        annual_return = statistics.get('annual_return', 0.0)
        max_drawdown = abs(statistics.get('max_drawdown', 0.0))
        sharpe_ratio = statistics.get('sharpe_ratio', 0.0)
        
        # 计算其他指标
        if not daily_df.empty:
            returns = daily_df['net_pnl'].pct_change().dropna()
            volatility = returns.std() * (252 ** 0.5) if len(returns) > 1 else 0.0
            
            # 交易相关统计
            all_trades = engine.get_all_trades()
            total_trades = len(all_trades)
            
            if total_trades > 0:
                profit_trades = len([t for t in all_trades if t.pnl > 0])
                win_rate = profit_trades / total_trades
                
                # 计算盈亏比
                profits = [t.pnl for t in all_trades if t.pnl > 0]
                losses = [abs(t.pnl) for t in all_trades if t.pnl < 0]
                
                avg_profit = sum(profits) / len(profits) if profits else 0
                avg_loss = sum(losses) / len(losses) if losses else 1
                profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 1.0
            else:
                win_rate = 0.0
                profit_loss_ratio = 1.0
                profit_trades = 0
        else:
            volatility = 0.0
            total_trades = 0
            profit_trades = 0
            win_rate = 0.0
            profit_loss_ratio = 1.0
        
        return {
            'total_return': float(total_return),
            'annual_return': float(annual_return),
            'sharpe_ratio': float(sharpe_ratio),
            'max_drawdown': float(max_drawdown),
            'volatility': float(volatility),
            'win_rate': float(win_rate),
            'profit_loss_ratio': float(profit_loss_ratio),
            'total_trades': int(total_trades),
            'profit_trades': int(profit_trades),
            'backtest_days': (datetime.strptime(self.end_date, "%Y%m%d") - 
                            datetime.strptime(self.start_date, "%Y%m%d")).days,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'engine_type': 'real_vnpy_backtest'
        }


def create_enhanced_backtester(symbol: str, contract_size: float, rate: float = 0.0002,
                             pricetick: float = 1.0, strategy_class=None,
                             start_date: str = "20220101", end_date: str = "20231231",
                             fast_mode: bool = False):
    """
    创建增强回测器

    Args:
        symbol: 合约代码
        contract_size: 合约大小
        rate: 手续费率
        pricetick: 最小价格变动
        strategy_class: 策略类
        start_date: 回测开始日期 (YYYYMMDD)
        end_date: 回测结束日期 (YYYYMMDD)
        fast_mode: 是否启用快速模式

    Returns:
        EnhancedBacktester: 回测器实例
    """
    return EnhancedBacktester(
        symbol=symbol,
        contract_size=contract_size,
        rate=rate,
        pricetick=pricetick,
        strategy_class=strategy_class,
        start_date=start_date,
        end_date=end_date,
        fast_mode=fast_mode
    )


def simulate_backtest_with_constraints(params: Dict[str, Any], symbol: str, 
                                     contract_size: float, rate: float, 
                                     start_date: str, end_date: str) -> Dict[str, Any]:
    """
    带约束的模拟回测函数
    
    Args:
        params: 参数字典
        symbol: 合约代码
        contract_size: 合约大小
        rate: 手续费率
        start_date: 回测开始日期 (YYYYMMDD)
        end_date: 回测结束日期 (YYYYMMDD)
        
    Returns:
        Dict: 回测结果
    """
    from datetime import datetime
    
    # 计算回测时间长度影响
    start_dt = datetime.strptime(start_date, "%Y%m%d")
    end_dt = datetime.strptime(end_date, "%Y%m%d")
    backtest_days = (end_dt - start_dt).days
    time_factor = min(backtest_days / 365.0, 3.0)  # 标准化到年，最多3年影响
    
    # 基础性能指标
    base_return = 0.15
    base_volatility = 0.25
    base_sharpe = base_return / base_volatility
    
    # 参数影响因子（基于参数合理性）
    sl_factor = min(params.get('sl_multiplier', 3.0) / 3.0, 1.5)
    zy_factor = min(params.get('zy', 50) / 50.0, 2.0)
    af_factor = min(params.get('AF', 0.001) * 1000, 2.0)
    
    # 约束满足度影响（满足约束的参数表现更好）
    constraints = get_enhanced_parameter_constraints()
    constraint_satisfaction = sum(1 for constraint_func in constraints.values() 
                                if constraint_func(params)) / len(constraints)
    
    # 合约特性影响
    contract_factor = min(contract_size / 5.0, 2.0)  # 标准化到5倍合约
    
    # 时间区间影响（更长的回测期可能有更稳定的结果，但也可能有更多噪声）
    time_stability_factor = 1.0 + 0.1 * min(time_factor, 2.0)  # 时间越长稳定性略好
    
    # 添加随机噪声
    noise = np.random.normal(0, 0.02)
    
    # 计算调整后的指标
    performance_multiplier = (0.7 + 0.3 * sl_factor * zy_factor * af_factor * 
                            constraint_satisfaction * contract_factor * time_stability_factor)
    
    adjusted_return = base_return * performance_multiplier + noise
    adjusted_volatility = base_volatility * (1.3 - 0.3 * performance_multiplier)
    adjusted_sharpe = adjusted_return / adjusted_volatility
    
    # 其他指标
    max_drawdown = abs(np.random.normal(0.08, 0.02)) * (2.0 - performance_multiplier)
    win_rate = min(max(0.4 + 0.2 * performance_multiplier + np.random.normal(0, 0.05), 0.2), 0.8)
    profit_loss_ratio = max(1.0 + 0.5 * performance_multiplier + np.random.normal(0, 0.2), 0.5)
    
    # 交易次数（受参数和时间长度影响）
    base_trades = 200
    trade_frequency_factor = (params.get('k_1', 1) + params.get('k_3', 3) + params.get('k_5', 5)) / 30.0
    total_trades = int(base_trades * trade_frequency_factor * time_factor * np.random.uniform(0.8, 1.2))
    
    return {
        'total_return': float(adjusted_return),
        'annual_return': float(adjusted_return),
        'sharpe_ratio': float(adjusted_sharpe),
        'max_drawdown': float(max_drawdown),
        'volatility': float(adjusted_volatility),
        'win_rate': float(win_rate),
        'profit_loss_ratio': float(profit_loss_ratio),
        'total_trades': int(total_trades),
        'profit_trades': int(win_rate * total_trades),
        'constraint_satisfaction': float(constraint_satisfaction),
        'performance_multiplier': float(performance_multiplier),
        'backtest_days': int(backtest_days),
        'start_date': start_date,
        'end_date': end_date,
    }


def optimize_single_symbol_enhanced(symbol: str, contract_info: Dict[str, Any],
                                   strategy_class=None, max_evaluations: int = 500,
                                   max_workers: int = 4, safe_mode: bool = True,
                                   start_date: str = "20220101", end_date: str = "20231231",
                                   use_fast_mode: bool = True) -> Dict[str, Any]:
    """
    使用增强稳定性优化器优化单个品种

    Args:
        symbol: 合约代码
        contract_info: 合约信息
        strategy_class: 策略类
        max_evaluations: 最大评估次数
        max_workers: 最大并行工作进程数 (可调节)
        safe_mode: 安全模式 (True=安全模式, False=高性能模式)
        start_date: 回测开始日期 (YYYYMMDD)
        end_date: 回测结束日期 (YYYYMMDD)
        use_fast_mode: 是否使用快速模式（分层优化和快速筛选）

    Returns:
        Dict: 优化结果
    """
    print(f"\n{'='*20} 优化品种: {symbol} {'='*20}")
    
    # 获取合约信息
    contract_size = contract_info.get('size', 5)
    rate = contract_info.get('rate', 0.0002)
    pricetick = contract_info.get('pricetick', 1.0)
    
    print(f"合约大小: {contract_size}, 手续费率: {rate}, 最小变动: {pricetick}")
    print(f"回测区间: {start_date} - {end_date}")
    print(f"并行度: {max_workers}, 模式: {'安全模式' if safe_mode else '高性能模式'}")
    
    # 生成参数空间
    param_spaces = generate_enhanced_param_spaces(contract_size)
    
    # 创建回测器（支持快速模式）
    backtester = create_enhanced_backtester(
        symbol, contract_size, rate, pricetick, strategy_class, start_date, end_date, use_fast_mode
    )
    
    # 创建增强稳定性优化器
    optimizer = EnhancedStabilityOptimizer(
        backtester=backtester,
        param_spaces=param_spaces,
        safe_mode=safe_mode,              # 可配置的安全模式
        enable_multiprocessing=True,      # 启用多进程
        max_workers=max_workers           # 可调节的并行度
    )
    
    try:
        # 执行稳定性优化
        start_time = time.time()
        result = optimizer.stability_optimization(
            max_evaluations=max_evaluations,
            n_initial_samples=min(200, max_evaluations // 3),
            convergence_threshold=1e-6,
            max_stagnant_iterations=50
        )

        optimization_time = time.time() - start_time

        # 合并固定参数
        fixed_params = get_fixed_parameters(contract_size)
        final_params = {**result.best_params, **fixed_params}

        # 验证参数约束
        constraints = get_enhanced_parameter_constraints()
        constraint_violations = []
        for constraint_name, constraint_func in constraints.items():
            if not constraint_func(final_params):
                constraint_violations.append(constraint_name)

        print(f"优化完成 - 性能: {result.best_performance:.4f}, 稳定性: {result.stability_score:.4f}")
        print(f"优化耗时: {optimization_time:.1f}秒")

        if constraint_violations:
            print(f"⚠️ 约束违反: {constraint_violations}")
        else:
            print("✅ 所有约束满足")

        return {
            'symbol': symbol,
            'best_performance': result.best_performance,
            'stability_score': result.stability_score,
            'best_params': final_params,
            'stability_metrics': result.stability_metrics,
            'parameter_importance': result.parameter_importance,
            'optimization_time': optimization_time,
            'constraint_violations': constraint_violations,
            'contract_info': contract_info,
            'optimization_config': {
                'max_workers': max_workers,
                'safe_mode': safe_mode
            }
        }

    except (ImportError, ModuleNotFoundError) as e:
        error_msg = f"优化器模块导入失败: {e}"
        print(f"❌ {error_msg}")
        return {
            'symbol': symbol,
            'error': error_msg,
            'error_type': 'import_error',
            'contract_info': contract_info
        }
    except (ValueError, TypeError) as e:
        error_msg = f"参数配置错误: {e}"
        print(f"❌ {error_msg}")
        return {
            'symbol': symbol,
            'error': error_msg,
            'error_type': 'parameter_error',
            'contract_info': contract_info
        }
    except MemoryError as e:
        error_msg = f"内存不足: {e}"
        print(f"❌ {error_msg}")
        return {
            'symbol': symbol,
            'error': error_msg,
            'error_type': 'memory_error',
            'contract_info': contract_info
        }
    except KeyboardInterrupt:
        error_msg = "用户中断优化"
        print(f"⚠️ {error_msg}")
        return {
            'symbol': symbol,
            'error': error_msg,
            'error_type': 'user_interrupt',
            'contract_info': contract_info
        }
    except Exception as e:
        error_msg = f"未知错误 ({type(e).__name__}): {e}"
        print(f"❌ {error_msg}")
        return {
            'symbol': symbol,
            'error': error_msg,
            'error_type': 'unknown_error',
            'contract_info': contract_info
        }


def run_batch_enhanced_optimization(symbols: List[str], contract_info: Dict[str, Dict], 
                                  strategy_class=None, max_evaluations: int = 500,
                                  max_workers: int = 4, safe_mode: bool = True,
                                  start_date: str = "20220101", end_date: str = "20231231") -> Dict[str, Any]:
    """
    批量运行增强稳定性优化（顺序执行）
    
    Args:
        symbols: 合约代码列表
        contract_info: 合约信息字典
        strategy_class: 策略类
        max_evaluations: 每个品种的最大评估次数
        max_workers: 最大并行工作进程数 (可调节)
        safe_mode: 安全模式 (True=安全模式, False=高性能模式)
        start_date: 回测开始日期 (YYYYMMDD)
        end_date: 回测结束日期 (YYYYMMDD)
        
    Returns:
        Dict: 批量优化结果
    """
    print("=" * 80)
    print("批量增强稳定性参数优化（顺序执行）")
    print("=" * 80)
    
    print(f"优化品种数量: {len(symbols)}")
    print(f"每品种评估次数: {max_evaluations}")
    print(f"并行度设置: {max_workers}")
    print(f"运行模式: {'安全模式' if safe_mode else '高性能模式'}")
    print(f"执行模式: 顺序执行（一个接一个）")
    print(f"回测区间: {start_date} - {end_date}")
    
    batch_results = {}
    
    # 顺序执行优化任务
    for i, symbol in enumerate(symbols, 1):
        print(f"\n{'='*60}")
        print(f"正在优化第 {i}/{len(symbols)} 个品种: {symbol}")
        print(f"{'='*60}")
        
        try:
            # 检查合约信息是否存在
            if symbol not in contract_info:
                error_msg = f"合约信息缺失: {symbol}"
                print(f"❌ {error_msg}")
                batch_results[symbol] = {
                    'symbol': symbol,
                    'error': error_msg,
                    'error_type': 'missing_contract_info'
                }
                continue

            # 优化单个品种，传递并行度和模式参数
            result = optimize_single_symbol_enhanced(
                symbol=symbol,
                contract_info=contract_info[symbol],
                strategy_class=strategy_class,
                max_evaluations=max_evaluations,
                max_workers=max_workers,  # 传递可调节的并行度
                safe_mode=safe_mode,      # 传递安全模式设置
                start_date=start_date,
                end_date=end_date
            )
            batch_results[symbol] = result

            # 显示当前品种优化结果摘要
            if 'error' not in result:
                print(f"✅ {symbol} 优化成功:")
                print(f"   性能: {result['best_performance']:.4f}")
                print(f"   稳定性: {result['stability_score']:.4f}")
                print(f"   耗时: {result['optimization_time']:.1f}秒")
                if 'optimization_config' in result:
                    config = result['optimization_config']
                    print(f"   配置: 并行度={config['max_workers']}, "
                          f"模式={'安全' if config['safe_mode'] else '高性能'}")
            else:
                error_type = result.get('error_type', 'unknown')
                print(f"❌ {symbol} 优化失败 ({error_type}): {result['error']}")

        except KeyboardInterrupt:
            print(f"⚠️ 用户中断批量优化")
            batch_results[symbol] = {
                'symbol': symbol,
                'error': '用户中断优化',
                'error_type': 'user_interrupt'
            }
            break  # 中断整个批量优化
        except MemoryError as e:
            error_msg = f"内存不足: {e}"
            print(f"❌ 品种 {symbol} 内存错误: {error_msg}")
            batch_results[symbol] = {
                'symbol': symbol,
                'error': error_msg,
                'error_type': 'memory_error'
            }
        except Exception as e:
            error_msg = f"未知异常 ({type(e).__name__}): {e}"
            print(f"❌ 品种 {symbol} 优化异常: {error_msg}")
            batch_results[symbol] = {
                'symbol': symbol,
                'error': error_msg,
                'error_type': 'unknown_error'
            }
        
        # 显示总体进度
        completed = i
        remaining = len(symbols) - i
        print(f"\n进度: {completed}/{len(symbols)} 完成, {remaining} 个品种待处理")
    
    print(f"\n{'='*80}")
    print("批量优化全部完成！")
    print(f"{'='*80}")
    
    # 添加批量配置信息到结果中
    batch_config = {
        'total_symbols': len(symbols),
        'max_evaluations': max_evaluations,
        'max_workers': max_workers,
        'safe_mode': safe_mode,
        'start_date': start_date,
        'end_date': end_date
    }
    
    return {
        'results': batch_results,
        'batch_config': batch_config
    }


def analyze_batch_results(batch_results: Dict[str, Any]) -> Dict[str, Any]:
    """
    分析批量优化结果
    
    Args:
        batch_results: 批量优化结果 (包含'results'和'batch_config'字段)
        
    Returns:
        Dict: 分析报告
    """
    # 处理新的批量结果格式
    if 'results' in batch_results:
        actual_results = batch_results['results']
        batch_config = batch_results.get('batch_config', {})
    else:
        # 兼容旧格式
        actual_results = batch_results
        batch_config = {}
    
    successful_results = []
    failed_results = []
    
    for symbol, result in actual_results.items():
        if 'error' not in result:
            successful_results.append(result)
        else:
            failed_results.append(result)
    
    if not successful_results:
        return {
            'success_count': 0,
            'failure_count': len(failed_results),
            'failed_symbols': [r['symbol'] for r in failed_results],
            'batch_config': batch_config
        }
    
    # 计算统计指标
    performances = [r['best_performance'] for r in successful_results]
    stability_scores = [r['stability_score'] for r in successful_results]
    optimization_times = [r['optimization_time'] for r in successful_results]
    
    # 参数统计
    param_stats = {}
    for result in successful_results:
        for param_name, value in result['best_params'].items():
            if param_name not in param_stats:
                param_stats[param_name] = []
            if isinstance(value, (int, float)):
                param_stats[param_name].append(value)
    
    # 计算参数统计值
    param_statistics = {}
    for param_name, values in param_stats.items():
        if values:
            param_statistics[param_name] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'median': np.median(values)
            }
    
    analysis_report = {
        'success_count': len(successful_results),
        'failure_count': len(failed_results),
        'success_rate': len(successful_results) / len(actual_results),
        'batch_config': batch_config,  # 包含批量配置信息
        'performance_stats': {
            'mean': np.mean(performances),
            'std': np.std(performances),
            'min': np.min(performances),
            'max': np.max(performances),
            'median': np.median(performances)
        },
        'stability_stats': {
            'mean': np.mean(stability_scores),
            'std': np.std(stability_scores),
            'min': np.min(stability_scores),
            'max': np.max(stability_scores),
            'median': np.median(stability_scores)
        },
        'time_stats': {
            'mean': np.mean(optimization_times),
            'std': np.std(optimization_times),
            'total': np.sum(optimization_times)
        },
        'parameter_statistics': param_statistics,
        'top_performers': sorted(successful_results, 
                               key=lambda x: x['best_performance'], 
                               reverse=True)[:5],
        'most_stable': sorted(successful_results, 
                            key=lambda x: x['stability_score'], 
                            reverse=True)[:5],
        'failed_symbols': [r['symbol'] for r in failed_results] if failed_results else []
    }
    
    return analysis_report


def safe_json_convert(obj):
    """
    安全地转换对象为JSON可序列化的格式
    
    Args:
        obj: 需要转换的对象
        
    Returns:
        转换后的对象
    """
    import numpy as np
    
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: safe_json_convert(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [safe_json_convert(item) for item in obj]
    else:
        return obj


def save_enhanced_results(batch_results: Dict[str, Any], analysis_report: Dict[str, Any], 
                        output_dir: str = "results"):
    """
    保存增强优化结果
    
    Args:
        batch_results: 批量结果
        analysis_report: 分析报告
        output_dir: 输出目录
    """
    from pathlib import Path
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细结果
    detailed_results_file = output_path / f"enhanced_stability_results_{timestamp}.json"
    with open(detailed_results_file, 'w', encoding='utf-8') as f:
        # 转换结果为可序列化格式
        serializable_results = {}
        for symbol, result in batch_results.items():
            if 'error' not in result:
                serializable_results[symbol] = {
                    'symbol': result['symbol'],
                    'best_performance': safe_json_convert(result['best_performance']),
                    'stability_score': safe_json_convert(result['stability_score']),
                    'best_params': {k: safe_json_convert(v) for k, v in result['best_params'].items()},
                    'stability_metrics': {
                        'performance_stability': safe_json_convert(result['stability_metrics'].performance_stability),
                        'parameter_sensitivity': safe_json_convert(result['stability_metrics'].parameter_sensitivity),
                        'temporal_consistency': safe_json_convert(result['stability_metrics'].temporal_consistency),
                        'robustness_score': safe_json_convert(result['stability_metrics'].robustness_score),
                        'overall_stability': safe_json_convert(result['stability_metrics'].overall_stability),
                    },
                    'parameter_importance': {k: safe_json_convert(v) for k, v in result['parameter_importance'].items()},
                    'optimization_time': safe_json_convert(result['optimization_time']),
                    'constraint_violations': result['constraint_violations'],
                    'contract_info': safe_json_convert(result['contract_info'])
                }
            else:
                serializable_results[symbol] = safe_json_convert(result)
        
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    # 保存分析报告
    analysis_file = output_path / f"enhanced_stability_analysis_{timestamp}.json"
    with open(analysis_file, 'w', encoding='utf-8') as f:
        # 处理不可序列化的对象
        serializable_analysis = safe_json_convert(analysis_report.copy())
        
        # 简化top_performers和most_stable
        if 'top_performers' in serializable_analysis:
            serializable_analysis['top_performers'] = [
                {
                    'symbol': r['symbol'],
                    'best_performance': safe_json_convert(r['best_performance']),
                    'stability_score': safe_json_convert(r['stability_score'])
                } for r in analysis_report['top_performers']
            ]
        
        if 'most_stable' in serializable_analysis:
            serializable_analysis['most_stable'] = [
                {
                    'symbol': r['symbol'],
                    'best_performance': safe_json_convert(r['best_performance']),
                    'stability_score': safe_json_convert(r['stability_score'])
                } for r in analysis_report['most_stable']
            ]
        
        json.dump(serializable_analysis, f, indent=2, ensure_ascii=False)
    
    print(f"\n结果已保存:")
    print(f"详细结果: {detailed_results_file}")
    print(f"分析报告: {analysis_file}")


def run_demo_mode():
    """运行演示模式"""
    print("=" * 80)
    print("增强稳定性参数优化系统 - 演示模式")
    print("=" * 80)
    
    # 展示参数空间
    print("\n1. 参数空间配置")
    print("-" * 40)
    
    param_spaces = generate_enhanced_param_spaces(contract_size=5)
    print(f"优化参数数量: {len(param_spaces)}")
    
    for param_name, (min_val, max_val, step, param_type) in param_spaces.items():
        print(f"  {param_name}: {min_val} - {max_val} (步长: {step}, 类型: {param_type.__name__})")
    
    # 展示固定参数
    print("\n固定参数:")
    fixed_params = get_fixed_parameters(contract_size=5)
    for param_name, value in fixed_params.items():
        print(f"  {param_name}: {value}")
    
    # 展示约束关系
    print("\n2. 参数约束关系")
    print("-" * 40)
    
    constraints = get_enhanced_parameter_constraints()
    constraint_descriptions = {
        'ping_zy_less_than_zy': '保本点 < 止盈点',
        'AF_less_than_AF_max': '加速因子 < 最大加速因子',
        'sl_multiplier_reasonable': '止损倍数 >= 1.0',
        'trailing_start_ratio_valid': '0 < 启动比例 < 1',
        'k_periods_logical': 'K线周期递增关系',
        'stop_profit_gap': '止盈点至少比保本点大5个点',
    }
    
    for constraint_name in constraints:
        description = constraint_descriptions.get(constraint_name, constraint_name)
        print(f"  ✓ {description}")
    
    # 展示优化特性
    print("\n3. 增强稳定性优化器特性")
    print("-" * 40)
    
    features = [
        "三层稳定性验证机制",
        "参数重要性分析 (XGBoost/随机森林)",
        "自适应采样策略",
        "蒙特卡洛鲁棒性测试",
        "Walk-Forward时间一致性验证",
        "分层优化流程 (全局→贝叶斯→精细)",
        "稳定性评分系统",
        "参数约束自动修正",
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"  {i}. {feature}")
    
    # 预估计算量
    print("\n4. 计算量预估")
    print("-" * 40)
    
    test_symbols = ['cu888.SHFE', 'al888.SHFE', 'zn888.SHFE']
    evaluations_per_symbol = 500  # 固定配置
    total_evaluations = len(test_symbols) * evaluations_per_symbol
    estimated_time = total_evaluations * 2 / 60  # 假设每次评估2秒
    
    # 回测时间区间配置
    backtest_start = "20220101"
    backtest_end = "20231231"
    
    print(f"测试品种: {test_symbols}")
    print(f"每品种评估次数: {evaluations_per_symbol}")
    print(f"总评估次数: {total_evaluations}")
    print(f"回测时间区间: {backtest_start} - {backtest_end}")
    print(f"预估耗时: {estimated_time:.1f} 分钟")
    
    print("\n" + "=" * 80)
    print("演示模式完成！")
    print("使用 --single 运行单品种优化")
    print("使用 --batch 运行批量优化")
    print("使用 --full 运行完整优化流程")
    print("=" * 80)


def run_single_symbol_demo():
    """运行单品种优化演示"""
    print("=" * 80)
    print("单品种增强稳定性优化演示")
    print("=" * 80)
    
    # 测试配置
    symbol = 'cu888.SHFE'
    contract_info = {
        'size': 5,
        'rate': 0.0002,
        'pricetick': 10
    }
    
    # 可调节的优化参数
    max_workers = 4      # 可调节的并行度
    safe_mode = True     # 可调节的安全模式
    max_evaluations = 100  # 减少评估次数用于演示
    
    # 回测时间区间
    start_date = "20240101"
    end_date = "20250531"
    
    print(f"优化配置:")
    print(f"并行度: {max_workers}")
    print(f"运行模式: {'安全模式' if safe_mode else '高性能模式'}")
    print(f"评估次数: {max_evaluations}")
    print(f"回测时间区间: {start_date} - {end_date}")
    
    # 运行优化
    result = optimize_single_symbol_enhanced(
        symbol=symbol,
        contract_info=contract_info,
        max_evaluations=max_evaluations,
        max_workers=max_workers,    # 传递可调节的并行度
        safe_mode=safe_mode,        # 传递可调节的安全模式
        start_date=start_date,
        end_date=end_date
    )
    
    # 显示结果
    if 'error' not in result:
        print(f"\n优化结果摘要:")
        print(f"品种: {result['symbol']}")
        print(f"最佳性能: {result['best_performance']:.4f}")
        print(f"稳定性得分: {result['stability_score']:.4f}")
        print(f"优化耗时: {result['optimization_time']:.1f}秒")
        
        # 显示配置信息
        if 'optimization_config' in result:
            config = result['optimization_config']
            print(f"实际配置: 并行度={config['max_workers']}, "
                  f"模式={'安全' if config['safe_mode'] else '高性能'}")
        
        print(f"\n关键参数:")
        key_params = ['sl_multiplier', 'zy', 'ping_zy', 'AF', 'AF_max']
        for param in key_params:
            if param in result['best_params']:
                value = result['best_params'][param]
                print(f"  {param}: {value}")
        
        print(f"\n稳定性指标:")
        metrics = result['stability_metrics']
        print(f"  性能稳定性: {metrics.performance_stability:.4f}")
        print(f"  参数敏感性: {metrics.parameter_sensitivity:.4f}")
        print(f"  时间一致性: {metrics.temporal_consistency:.4f}")
        print(f"  鲁棒性得分: {metrics.robustness_score:.4f}")
        print(f"  总体稳定性: {metrics.overall_stability:.4f}")
    else:
        print(f"优化失败: {result['error']}")


def run_fast_mode():
    """极速模式 - 快速参数探索 (20倍加速)"""
    print("🚀 极速模式 - 快速参数探索")
    print("=" * 60)

    # 极速模式配置
    max_evaluations = 200
    max_workers = 8
    safe_mode = False
    use_fast_mode = True

    # 品种选择 - 可以修改这里选择品种
    # 选项1: 自选品种
    selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE']

    # 选项2: 全部品种 (注释掉上面一行，取消注释下面的代码)
    # try:
    #     contract_info_loader = ContractInfo("期货全品种手续费保证金.xls")
    #     selected_symbols = contract_info_loader.get_888_contracts()[:20]  # 限制前20个品种
    # except:
    #     selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE']

    print(f"配置: 评估次数={max_evaluations}, 并行度={max_workers}, 快速模式=启用")
    print(f"优化品种: {selected_symbols}")

    # 加载合约信息
    contract_info = load_contract_info_for_symbols(selected_symbols)

    # 执行批量优化
    batch_results = run_batch_optimization_with_config(
        symbols=selected_symbols,
        contract_info=contract_info,
        max_evaluations=max_evaluations,
        max_workers=max_workers,
        safe_mode=safe_mode,
        use_fast_mode=use_fast_mode
    )

    # 分析和保存结果
    analysis_report = analyze_batch_results(batch_results)
    save_enhanced_results(batch_results, analysis_report)
    print_optimization_summary(analysis_report, "极速模式")


def run_balanced_mode():
    """平衡模式 - 速度与精度平衡 (10倍加速)"""
    print("⚖️ 平衡模式 - 速度与精度平衡")
    print("=" * 60)

    # 平衡模式配置
    max_evaluations = 500
    max_workers = 10
    safe_mode = False
    use_fast_mode = True

    # 品种选择 - 可以修改这里选择品种
    # 选项1: 自选品种
    selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE', 'PX888.CZCE', 'SH888.CZCE']

    # 选项2: 全部品种 (注释掉上面一行，取消注释下面的代码)
    # try:
    #     contract_info_loader = ContractInfo("期货全品种手续费保证金.xls")
    #     selected_symbols = contract_info_loader.get_888_contracts()[:50]  # 限制前50个品种
    # except:
    #     selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE', 'PX888.CZCE', 'SH888.CZCE']

    print(f"配置: 评估次数={max_evaluations}, 并行度={max_workers}, 快速模式=启用")
    print(f"优化品种: {selected_symbols}")

    # 加载合约信息
    contract_info = load_contract_info_for_symbols(selected_symbols)

    # 执行批量优化
    batch_results = run_batch_optimization_with_config(
        symbols=selected_symbols,
        contract_info=contract_info,
        max_evaluations=max_evaluations,
        max_workers=max_workers,
        safe_mode=safe_mode,
        use_fast_mode=use_fast_mode
    )

    # 分析和保存结果
    analysis_report = analyze_batch_results(batch_results)
    save_enhanced_results(batch_results, analysis_report)
    print_optimization_summary(analysis_report, "平衡模式")


def run_precise_mode():
    """精确模式 - 高精度优化 (3倍加速)"""
    print("🎯 精确模式 - 高精度优化")
    print("=" * 60)

    # 精确模式配置
    max_evaluations = 1000
    max_workers = 8
    safe_mode = True
    use_fast_mode = False

    # 品种选择 - 可以修改这里选择品种
    # 选项1: 自选品种
    selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE', 'PX888.CZCE', 'SH888.CZCE', 'br888.SHFE', 'lu888.INE', 'MA888.CZCE']

    # 选项2: 全部品种 (注释掉上面一行，取消注释下面的代码)
    # try:
    #     contract_info_loader = ContractInfo("期货全品种手续费保证金.xls")
    #     selected_symbols = contract_info_loader.get_888_contracts()  # 全部品种
    # except:
    #     selected_symbols = ['TA888.CZCE', 'bu888.SHFE', 'rb888.SHFE', 'PX888.CZCE', 'SH888.CZCE', 'br888.SHFE', 'lu888.INE', 'MA888.CZCE']

    print(f"配置: 评估次数={max_evaluations}, 并行度={max_workers}, 安全模式=启用")
    print(f"优化品种: {selected_symbols}")

    # 加载合约信息
    contract_info = load_contract_info_for_symbols(selected_symbols)

    # 执行批量优化
    batch_results = run_batch_optimization_with_config(
        symbols=selected_symbols,
        contract_info=contract_info,
        max_evaluations=max_evaluations,
        max_workers=max_workers,
        safe_mode=safe_mode,
        use_fast_mode=use_fast_mode
    )

    # 分析和保存结果
    analysis_report = analyze_batch_results(batch_results)
    save_enhanced_results(batch_results, analysis_report)
    print_optimization_summary(analysis_report, "精确模式")


def load_contract_info_for_symbols(symbols):
    """为指定品种加载合约信息"""
    contract_info = {}

    try:
        # 尝试从Excel文件加载
        contract_info_loader = ContractInfo("期货全品种手续费保证金.xls")
        params = contract_info_loader.get_contract_params()

        for symbol in symbols:
            try:
                contract_info[symbol] = {
                    'size': params['size'].get(symbol, 5),
                    'rate': params['rate'].get(symbol, 0.0002),
                    'pricetick': params['pricetick'].get(symbol, 1)
                }
            except Exception as e:
                print(f"⚠️ 获取 {symbol} 合约信息失败，使用默认值: {e}")
                contract_info[symbol] = {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}
    except Exception as e:
        print(f"⚠️ 无法加载合约信息文件，使用默认值: {e}")
        for symbol in symbols:
            contract_info[symbol] = {'size': 5, 'rate': 0.0002, 'pricetick': 1.0}

    return contract_info


def run_batch_optimization_with_config(symbols, contract_info, max_evaluations, max_workers, safe_mode, use_fast_mode):
    """使用指定配置运行批量优化"""
    batch_results = {}
    start_date = "20230101"
    end_date = "20250531"

    print(f"\n开始批量优化 {len(symbols)} 个品种...")

    for i, symbol in enumerate(symbols, 1):
        print(f"\n{'='*50}")
        print(f"正在优化第 {i}/{len(symbols)} 个品种: {symbol}")
        print(f"{'='*50}")

        try:
            result = optimize_single_symbol_enhanced(
                symbol=symbol,
                contract_info=contract_info[symbol],
                strategy_class=None,
                max_evaluations=max_evaluations,
                max_workers=max_workers,
                safe_mode=safe_mode,
                start_date=start_date,
                end_date=end_date,
                use_fast_mode=use_fast_mode
            )
            batch_results[symbol] = result

            # 显示结果摘要
            if 'error' not in result:
                print(f"✅ {symbol} 优化成功: 性能={result['best_performance']:.4f}, 稳定性={result['stability_score']:.4f}")
            else:
                print(f"❌ {symbol} 优化失败: {result['error']}")

        except Exception as e:
            print(f"❌ {symbol} 优化异常: {e}")
            batch_results[symbol] = {'symbol': symbol, 'error': str(e)}

    return batch_results


def print_optimization_summary(analysis_report, mode_name):
    """打印优化结果摘要"""
    print(f"\n{'='*60}")
    print(f"{mode_name} 优化结果摘要")
    print(f"{'='*60}")

    print(f"✅ 成功优化: {analysis_report['success_count']} 个品种")
    print(f"❌ 失败品种: {analysis_report['failure_count']} 个品种")
    print(f"📊 成功率: {analysis_report['success_rate']:.1%}")

    if analysis_report['success_count'] > 0:
        perf_stats = analysis_report['performance_stats']
        print(f"\n📈 性能统计:")
        print(f"   平均性能: {perf_stats['mean']:.4f}")
        print(f"   最佳性能: {perf_stats['max']:.4f}")
        print(f"   性能标准差: {perf_stats['std']:.4f}")

        print(f"\n🏆 表现最佳品种:")
        for i, result in enumerate(analysis_report['top_performers'][:5], 1):
            print(f"   {i}. {result['symbol']}: {result['best_performance']:.4f}")

    if analysis_report['failed_symbols']:
        print(f"\n❌ 失败品种: {analysis_report['failed_symbols']}")


if __name__ == "__main__":
    print("🚀 增强稳定性参数优化")
    print("=" * 60)
    print("请选择运行模式 (修改下面的注释来选择):")
    print("1. 极速模式 - 快速参数探索 (20倍加速)")
    print("2. 平衡模式 - 速度与精度平衡 (10倍加速)")
    print("3. 精确模式 - 高精度优化 (3倍加速)")

    print("4. 演示模式 - 单品种演示")
    print()

    # ==================== 选择运行模式 ====================
    # 取消注释下面其中一行来选择运行模式:

    # run_fast_mode()        # 极速模式 - 快速参数探索
    run_balanced_mode()    # 平衡模式 - 速度与精度平衡 (默认)
    # run_precise_mode()     # 精确模式 - 高精度优化
    # run_demo_mode()        # 演示模式 - 单品种演示

    # ====================================================
