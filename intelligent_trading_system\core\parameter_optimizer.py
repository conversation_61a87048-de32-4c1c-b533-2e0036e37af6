"""
参数自适应优化模块
Parameter Adaptive Optimization Module

功能：
1. 动态窗口优化 - 滚动120个交易日数据回测
2. 参数空间搜索 - 关键参数范围搜索
3. 目标函数优化 - 最大化Calmar比率
4. 过拟合防护 - Walk-Forward分析和参数稳定性检验
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any
from dataclasses import dataclass
import logging
from datetime import datetime, timedelta
from concurrent.futures import ProcessPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class ParameterSpace:
    """参数空间定义"""
    name: str
    min_value: float
    max_value: float
    step: float
    param_type: type = float
    
    def get_values(self) -> List:
        """获取参数值列表"""
        if self.param_type == int:
            return list(range(int(self.min_value), int(self.max_value) + 1, int(self.step)))
        else:
            values = []
            current = self.min_value
            while current <= self.max_value:
                values.append(round(current, 3))
                current += self.step
            return values

@dataclass
class OptimizationResult:
    """优化结果"""
    symbol: str
    best_params: Dict[str, Any]
    performance_metrics: Dict[str, float]
    stability_score: float
    is_stable: bool
    optimization_time: float
    sample_count: int

@dataclass
class BacktestMetrics:
    """回测指标"""
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    calmar_ratio: float
    win_rate: float
    profit_factor: float
    total_trades: int

class ParameterOptimizer:
    """参数自适应优化器"""
    
    def __init__(self, lookback_days: int = 120, train_ratio: float = 0.7):
        """
        初始化参数优化器
        
        Args:
            lookback_days: 回看天数
            train_ratio: 训练集比例
        """
        self.lookback_days = lookback_days
        self.train_ratio = train_ratio
        self.parameter_spaces = self._define_parameter_spaces()
        self.optimization_history = []
        
    def _define_parameter_spaces(self) -> Dict[str, ParameterSpace]:
        """定义参数搜索空间"""
        return {
            'ma_period': ParameterSpace('ma_period', 5, 30, 5, int),
            'stop_atr_mult': ParameterSpace('stop_atr_mult', 1.5, 3.0, 0.5, float),
            'profit_atr_mult': ParameterSpace('profit_atr_mult', 2.0, 5.0, 0.5, float),
            'atr_period': ParameterSpace('atr_period', 10, 20, 5, int),
            'volume_filter': ParameterSpace('volume_filter', 0.5, 2.0, 0.5, float),
            'trend_strength': ParameterSpace('trend_strength', 0.1, 0.5, 0.1, float)
        }
    
    def optimize_parameters(self, symbol: str, data: pd.DataFrame, 
                          max_workers: int = 4) -> OptimizationResult:
        """
        执行参数优化
        
        Args:
            symbol: 品种代码
            data: 价格数据
            max_workers: 最大并行工作数
            
        Returns:
            优化结果
        """
        start_time = datetime.now()
        logger.info(f"开始优化 {symbol} 参数，数据长度: {len(data)}")
        
        # 数据预处理
        if len(data) < self.lookback_days:
            logger.warning(f"{symbol} 数据不足，跳过优化")
            return self._create_default_result(symbol)
        
        # 截取最近数据
        recent_data = data.iloc[-self.lookback_days:].copy()
        
        # 分割训练集和测试集
        split_point = int(len(recent_data) * self.train_ratio)
        train_data = recent_data.iloc[:split_point]
        test_data = recent_data.iloc[split_point:]
        
        if len(train_data) < 30 or len(test_data) < 10:
            logger.warning(f"{symbol} 分割后数据不足，跳过优化")
            return self._create_default_result(symbol)
        
        # 生成参数组合
        param_combinations = self._generate_parameter_combinations()
        logger.info(f"生成 {len(param_combinations)} 个参数组合")
        
        # 并行优化
        best_result = self._parallel_optimization(
            symbol, train_data, test_data, param_combinations, max_workers
        )
        
        # 计算优化时间
        optimization_time = (datetime.now() - start_time).total_seconds()
        
        # 创建结果
        result = OptimizationResult(
            symbol=symbol,
            best_params=best_result['params'],
            performance_metrics=best_result['metrics'],
            stability_score=best_result['stability'],
            is_stable=best_result['stability'] > 0.7,
            optimization_time=optimization_time,
            sample_count=len(param_combinations)
        )
        
        # 记录历史
        self.optimization_history.append({
            'timestamp': datetime.now(),
            'symbol': symbol,
            'result': result
        })
        
        logger.info(f"{symbol} 优化完成，最佳Calmar比率: {best_result['metrics']['calmar_ratio']:.3f}")
        
        return result
    
    def _generate_parameter_combinations(self) -> List[Dict[str, Any]]:
        """生成参数组合"""
        combinations = []
        
        # 获取各参数的值列表
        param_values = {}
        for name, space in self.parameter_spaces.items():
            param_values[name] = space.get_values()
        
        # 生成笛卡尔积（限制组合数量避免过度计算）
        import itertools
        
        # 为了控制计算量，采用网格采样
        keys = list(param_values.keys())
        values = list(param_values.values())
        
        # 限制每个参数最多取5个值
        sampled_values = []
        for value_list in values:
            if len(value_list) <= 5:
                sampled_values.append(value_list)
            else:
                # 等间距采样5个值
                indices = np.linspace(0, len(value_list)-1, 5, dtype=int)
                sampled_values.append([value_list[i] for i in indices])
        
        # 生成组合
        for combination in itertools.product(*sampled_values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def _parallel_optimization(self, symbol: str, train_data: pd.DataFrame, 
                             test_data: pd.DataFrame, param_combinations: List[Dict],
                             max_workers: int) -> Dict:
        """并行参数优化"""
        best_result = {
            'params': {},
            'metrics': {'calmar_ratio': -np.inf},
            'stability': 0.0
        }
        
        if max_workers <= 1:
            # 串行处理
            for params in param_combinations:
                result = self._evaluate_parameters(symbol, train_data, test_data, params)
                if result['metrics']['calmar_ratio'] > best_result['metrics']['calmar_ratio']:
                    best_result = result
        else:
            # 并行处理
            try:
                with ProcessPoolExecutor(max_workers=max_workers) as executor:
                    futures = {
                        executor.submit(self._evaluate_parameters, symbol, train_data, test_data, params): params
                        for params in param_combinations
                    }
                    
                    for future in as_completed(futures):
                        try:
                            result = future.result(timeout=30)  # 30秒超时
                            if result['metrics']['calmar_ratio'] > best_result['metrics']['calmar_ratio']:
                                best_result = result
                        except Exception as e:
                            logger.warning(f"参数评估失败: {e}")
                            continue
                            
            except Exception as e:
                logger.error(f"并行优化失败，回退到串行: {e}")
                # 回退到串行处理
                for params in param_combinations[:50]:  # 限制数量
                    result = self._evaluate_parameters(symbol, train_data, test_data, params)
                    if result['metrics']['calmar_ratio'] > best_result['metrics']['calmar_ratio']:
                        best_result = result
        
        return best_result
    
    def _evaluate_parameters(self, symbol: str, train_data: pd.DataFrame, 
                           test_data: pd.DataFrame, params: Dict[str, Any]) -> Dict:
        """评估参数组合"""
        try:
            # 训练集回测
            train_metrics = self._backtest_strategy(train_data, params)
            
            # 测试集验证
            test_metrics = self._backtest_strategy(test_data, params)
            
            # 计算稳定性评分
            stability = self._calculate_stability_score(train_metrics, test_metrics)
            
            # 综合评分（主要看训练集Calmar比率，但要求测试集不能太差）
            if test_metrics.calmar_ratio < 0.5:  # 测试集表现太差
                final_calmar = train_metrics.calmar_ratio * 0.5
            else:
                final_calmar = train_metrics.calmar_ratio
            
            return {
                'params': params,
                'metrics': {
                    'calmar_ratio': final_calmar,
                    'sharpe_ratio': train_metrics.sharpe_ratio,
                    'max_drawdown': train_metrics.max_drawdown,
                    'win_rate': train_metrics.win_rate,
                    'total_trades': train_metrics.total_trades
                },
                'stability': stability,
                'train_metrics': train_metrics,
                'test_metrics': test_metrics
            }
            
        except Exception as e:
            logger.warning(f"参数评估异常: {e}")
            return {
                'params': params,
                'metrics': {'calmar_ratio': -np.inf},
                'stability': 0.0
            }
    
    def _backtest_strategy(self, data: pd.DataFrame, params: Dict[str, Any]) -> BacktestMetrics:
        """回测策略"""
        df = data.copy()
        
        # 计算技术指标
        df['ma'] = df['close'].rolling(params['ma_period']).mean()
        df['atr'] = self._calculate_atr(df, params['atr_period'])
        df['volume_ma'] = df['volume'].rolling(20).mean()
        
        # 生成交易信号
        df['signal'] = 0
        df.loc[df['close'] > df['ma'], 'signal'] = 1  # 多头信号
        df.loc[df['close'] < df['ma'], 'signal'] = -1  # 空头信号
        
        # 过滤低流动性
        volume_threshold = df['volume_ma'] * params['volume_filter']
        df.loc[df['volume'] < volume_threshold, 'signal'] = 0
        
        # 计算交易
        trades = self._calculate_trades(df, params)
        
        # 计算绩效指标
        return self._calculate_metrics(trades, df)
    
    def _calculate_atr(self, data: pd.DataFrame, period: int) -> pd.Series:
        """计算ATR"""
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()
    
    def _calculate_trades(self, df: pd.DataFrame, params: Dict[str, Any]) -> List[Dict]:
        """计算交易记录"""
        trades = []
        position = 0
        entry_price = 0
        entry_atr = 0
        
        for i, row in df.iterrows():
            if position == 0 and row['signal'] != 0:
                # 开仓
                position = row['signal']
                entry_price = row['close']
                entry_atr = row['atr']
                
            elif position != 0:
                # 检查止损止盈
                if position == 1:  # 多头
                    stop_loss = entry_price - entry_atr * params['stop_atr_mult']
                    take_profit = entry_price + entry_atr * params['profit_atr_mult']
                    
                    if row['low'] <= stop_loss or row['high'] >= take_profit or row['signal'] <= 0:
                        # 平仓
                        if row['low'] <= stop_loss:
                            exit_price = stop_loss
                        elif row['high'] >= take_profit:
                            exit_price = take_profit
                        else:
                            exit_price = row['close']
                            
                        pnl = (exit_price - entry_price) * position
                        trades.append({
                            'entry_price': entry_price,
                            'exit_price': exit_price,
                            'pnl': pnl,
                            'position': position
                        })
                        position = 0
                        
                else:  # 空头
                    stop_loss = entry_price + entry_atr * params['stop_atr_mult']
                    take_profit = entry_price - entry_atr * params['profit_atr_mult']
                    
                    if row['high'] >= stop_loss or row['low'] <= take_profit or row['signal'] >= 0:
                        # 平仓
                        if row['high'] >= stop_loss:
                            exit_price = stop_loss
                        elif row['low'] <= take_profit:
                            exit_price = take_profit
                        else:
                            exit_price = row['close']
                            
                        pnl = (entry_price - exit_price) * abs(position)
                        trades.append({
                            'entry_price': entry_price,
                            'exit_price': exit_price,
                            'pnl': pnl,
                            'position': position
                        })
                        position = 0
        
        return trades
    
    def _calculate_metrics(self, trades: List[Dict], df: pd.DataFrame) -> BacktestMetrics:
        """计算绩效指标"""
        if not trades:
            return BacktestMetrics(0, 0, 0, 0, 0, 0, 0, 0)
        
        # 基础统计
        pnls = [trade['pnl'] for trade in trades]
        total_return = sum(pnls)
        win_trades = [pnl for pnl in pnls if pnl > 0]
        lose_trades = [pnl for pnl in pnls if pnl < 0]
        
        # 胜率
        win_rate = len(win_trades) / len(trades) if trades else 0
        
        # 盈亏比
        avg_win = np.mean(win_trades) if win_trades else 0
        avg_loss = abs(np.mean(lose_trades)) if lose_trades else 1
        profit_factor = avg_win / avg_loss if avg_loss > 0 else 0
        
        # 计算权益曲线
        equity_curve = np.cumsum([0] + pnls)
        
        # 最大回撤
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (peak - equity_curve) / np.maximum(peak, 1)
        max_drawdown = np.max(drawdown)
        
        # 年化收益率
        trading_days = len(df)
        annual_return = total_return * 252 / trading_days if trading_days > 0 else 0
        
        # 夏普比率
        if len(pnls) > 1:
            returns_std = np.std(pnls) * np.sqrt(252)
            sharpe_ratio = annual_return / returns_std if returns_std > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Calmar比率
        calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
        
        return BacktestMetrics(
            total_return=total_return,
            annual_return=annual_return,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            calmar_ratio=calmar_ratio,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=len(trades)
        )
    
    def _calculate_stability_score(self, train_metrics: BacktestMetrics, 
                                 test_metrics: BacktestMetrics) -> float:
        """计算稳定性评分"""
        if train_metrics.calmar_ratio <= 0 or test_metrics.calmar_ratio <= 0:
            return 0.0
        
        # 计算各指标的稳定性
        calmar_stability = min(test_metrics.calmar_ratio / train_metrics.calmar_ratio, 1.0)
        winrate_stability = 1 - abs(test_metrics.win_rate - train_metrics.win_rate)
        
        # 综合稳定性评分
        stability = (calmar_stability * 0.7 + winrate_stability * 0.3)
        
        return max(0, min(1, stability))
    
    def _create_default_result(self, symbol: str) -> OptimizationResult:
        """创建默认结果"""
        default_params = {
            'ma_period': 15,
            'stop_atr_mult': 2.5,
            'profit_atr_mult': 3.0,
            'atr_period': 14,
            'volume_filter': 1.0,
            'trend_strength': 0.2
        }
        
        return OptimizationResult(
            symbol=symbol,
            best_params=default_params,
            performance_metrics={'calmar_ratio': 0.0},
            stability_score=0.0,
            is_stable=False,
            optimization_time=0.0,
            sample_count=0
        )

    def get_optimization_report(self, symbol: str) -> Dict:
        """生成优化报告"""
        # 查找最近的优化结果
        recent_results = [h for h in self.optimization_history if h['symbol'] == symbol]
        if not recent_results:
            return {}

        latest = recent_results[-1]
        result = latest['result']

        return {
            'symbol': symbol,
            'timestamp': latest['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
            'optimization_time': f"{result.optimization_time:.2f}秒",
            'sample_count': result.sample_count,
            'is_stable': result.is_stable,
            'stability_score': round(result.stability_score, 3),
            'best_parameters': result.best_params,
            'performance_metrics': {
                'calmar_ratio': round(result.performance_metrics.get('calmar_ratio', 0), 3),
                'sharpe_ratio': round(result.performance_metrics.get('sharpe_ratio', 0), 3),
                'max_drawdown': round(result.performance_metrics.get('max_drawdown', 0), 3),
                'win_rate': round(result.performance_metrics.get('win_rate', 0), 3),
                'total_trades': result.performance_metrics.get('total_trades', 0)
            }
        }

    def batch_optimize(self, instruments_data: Dict[str, pd.DataFrame],
                      max_workers: int = 4) -> Dict[str, OptimizationResult]:
        """批量优化多个品种"""
        results = {}

        logger.info(f"开始批量优化 {len(instruments_data)} 个品种")

        for symbol, data in instruments_data.items():
            try:
                result = self.optimize_parameters(symbol, data, max_workers)
                results[symbol] = result

                logger.info(f"{symbol} 优化完成: Calmar={result.performance_metrics.get('calmar_ratio', 0):.3f}, "
                          f"稳定性={result.stability_score:.3f}")

            except Exception as e:
                logger.error(f"{symbol} 优化失败: {e}")
                results[symbol] = self._create_default_result(symbol)

        return results
