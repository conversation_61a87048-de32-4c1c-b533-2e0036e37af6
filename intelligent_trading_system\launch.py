#!/usr/bin/env python3
"""
智能期货交易系统启动器
Intelligent Futures Trading System Launcher

一键启动完整的智能期货交易系统
解决所有路径和依赖问题
"""

import sys
import os
import traceback
from datetime import datetime, timedelta

# 确保路径正确
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("🚀 智能期货交易系统 v1.0")
print("="*80)
print("解决期货策略三大痛点的系统化解决方案")
print("1. 品种选择困难 → 动态品种筛选引擎")
print("2. 参数调整迷茫 → 参数自适应优化模块")  
print("3. 净值停滞问题 → 资金智能管理系统")
print("="*80)

def check_environment():
    """检查运行环境"""
    print("🔧 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    print(f"  ✅ Python {sys.version.split()[0]}")
    
    # 检查必要的包
    required_packages = ['pandas', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} (缺失)")
    
    if missing_packages:
        print(f"\n⚠️  请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    # 检查可选包
    optional_packages = {'matplotlib': 'GUI图表', 'tkinter': 'GUI界面'}
    for package, desc in optional_packages.items():
        try:
            __import__(package)
            print(f"  ✅ {package} ({desc})")
        except ImportError:
            print(f"  ⚠️  {package} (可选，用于{desc})")
    
    print("✅ 环境检查完成")
    return True

def run_quick_demo():
    """运行快速演示"""
    print("\n🎯 快速演示 - 品种筛选功能")
    print("-" * 50)
    
    try:
        # 导入核心模块
        from core.instrument_selector import InstrumentSelector
        from core.data_manager import DataManager
        from contract_manager import get_contract_manager
        
        # 初始化
        print("📊 初始化系统组件...")
        contract_manager = get_contract_manager()
        data_manager = DataManager()
        selector = InstrumentSelector(account_value=5000000)
        
        print(f"  ✅ 加载了 {len(contract_manager.get_all_contracts())} 个期货品种")
        
        # 获取测试数据
        print("📈 获取市场数据...")
        test_symbols = ['rb888.SHFE', 'cu888.SHFE', 'au888.SHFE', 'TA888.CZCE', 'm888.DCE']
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
        
        market_data = {}
        for symbol in test_symbols:
            try:
                data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
                if not data.empty:
                    market_data[symbol] = data
                    print(f"  ✅ {symbol}: {len(data)} 条数据")
            except Exception as e:
                print(f"  ⚠️  {symbol}: 使用模拟数据 ({e})")
        
        if market_data:
            # 执行品种筛选
            print("\n🔍 执行品种筛选...")
            metrics_list = selector.select_instruments(market_data)
            selected_symbols = [m.symbol for m in metrics_list if m.is_selected]
            
            print(f"  ✅ 筛选完成！从 {len(metrics_list)} 个品种中选出 {len(selected_symbols)} 个")
            print(f"  📋 选中品种: {', '.join(selected_symbols)}")
            
            # 显示详细结果
            print("\n📊 筛选结果详情:")
            print(f"{'品种代码':<12} {'状态':<8} {'动量评分':<10} {'策略胜率':<10} {'综合评分':<10}")
            print("-" * 60)
            
            for metrics in metrics_list:
                status = "✅选中" if metrics.is_selected else "❌未选"
                print(f"{metrics.symbol:<12} {status:<8} {metrics.momentum_score:>8.3f} "
                      f"{metrics.strategy_winrate:>8.1%} {metrics.overall_score:>8.3f}")
            
            # 显示推荐仓位
            if selected_symbols:
                print(f"\n💰 资金配置建议 (500万账户):")
                positions = selector.get_recommended_positions([m for m in metrics_list if m.is_selected])
                total_risk = 0
                
                for symbol, lots in positions.items():
                    contract_info = contract_manager.get_contract_info(symbol)
                    risk_amount = 100000  # 简化计算，每个品种10万风险
                    total_risk += risk_amount
                    print(f"  {symbol:<12} {lots:>3}手  风险金额: {risk_amount:>8,}元")
                
                print(f"  {'总计':<12} {'':>3}   总风险暴露: {total_risk:>8,}元 ({total_risk/5000000:.1%})")
        
        print("\n✅ 快速演示完成！")
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        traceback.print_exc()
        return False

def run_backtest_demo():
    """运行回测演示"""
    print("\n📈 回测演示")
    print("-" * 50)
    
    try:
        from core.backtest_engine import BacktestEngine
        from strategies.adaptive_trend_strategy import AdaptiveTrendStrategy
        from core.data_manager import DataManager
        
        print("🔧 初始化回测组件...")
        backtest_engine = BacktestEngine(initial_capital=5000000)
        strategy = AdaptiveTrendStrategy()
        data_manager = DataManager()
        
        # 获取回测数据
        symbols = ['rb888.SHFE', 'cu888.SHFE']
        start_date = '2024-01-01'
        end_date = '2024-06-01'
        
        print(f"📊 获取回测数据: {start_date} 到 {end_date}")
        backtest_data = {}
        
        for symbol in symbols:
            data = data_manager.get_historical_data(symbol, start_date, end_date, source='simulation')
            if not data.empty:
                backtest_data[symbol] = data
                strategy.calculate_indicators(data, symbol)
                print(f"  ✅ {symbol}: {len(data)} 条数据")
        
        if backtest_data:
            print("\n🚀 开始回测...")
            result = backtest_engine.run_backtest(strategy, backtest_data, start_date, end_date)
            
            print(f"\n📊 回测结果:")
            print(f"  初始资金: {result.initial_capital:>12,.0f} 元")
            print(f"  最终资金: {result.final_capital:>12,.0f} 元")
            print(f"  总收益率: {result.total_return:>12.2%}")
            print(f"  年化收益: {result.annual_return:>12.2%}")
            print(f"  最大回撤: {result.max_drawdown:>12.2%}")
            print(f"  夏普比率: {result.sharpe_ratio:>12.3f}")
            print(f"  Calmar比率: {result.calmar_ratio:>12.3f}")
            print(f"  胜率: {result.win_rate:>12.1%}")
            print(f"  盈亏比: {result.profit_factor:>12.2f}")
            print(f"  交易次数: {result.total_trades:>12d}")
            
            if result.trades:
                print(f"\n📋 最近交易记录:")
                for i, trade in enumerate(result.trades[-3:], 1):
                    direction = "多头" if trade.direction == 1 else "空头"
                    print(f"  {i}. {trade.symbol} {direction} {trade.quantity}手 "
                          f"盈亏: {trade.pnl:>8.2f}元 "
                          f"时间: {trade.exit_time.strftime('%m-%d')}")
        
        print("\n✅ 回测演示完成！")
        return True
        
    except Exception as e:
        print(f"❌ 回测演示失败: {e}")
        traceback.print_exc()
        return False

def start_gui():
    """启动GUI界面"""
    print("\n🖥️  启动GUI界面...")
    
    try:
        import tkinter as tk
        from gui_interface import TradingSystemGUI
        
        print("  🎨 创建图形界面...")
        app = TradingSystemGUI()
        
        print("  🚀 启动GUI界面...")
        print("  💡 GUI界面已打开，请在新窗口中操作")
        app.run()
        
    except ImportError as e:
        print(f"❌ GUI启动失败: {e}")
        print("可能缺少tkinter或matplotlib，请安装: pip install matplotlib")
        return False
    except Exception as e:
        print(f"❌ GUI运行失败: {e}")
        traceback.print_exc()
        return False

def show_contract_info():
    """显示合约信息"""
    print("\n📋 期货合约信息")
    print("-" * 50)
    
    try:
        from contract_manager import get_contract_manager
        
        contract_manager = get_contract_manager()
        contracts = contract_manager.get_all_contracts()
        
        print(f"共支持 {len(contracts)} 个期货品种:")
        print(f"{'品种代码':<15} {'品种名称':<10} {'合约乘数':<8} {'最小变动':<8} {'手续费率'}")
        print("-" * 70)
        
        for symbol in contracts[:15]:  # 显示前15个
            info = contract_manager.get_contract_info(symbol)
            print(f"{symbol:<15} {info['name']:<10} {info['size']:>6} "
                  f"{info['pricetick']:>8} {info['rate']:>8.4f}")
        
        if len(contracts) > 15:
            print(f"... 还有 {len(contracts) - 15} 个品种")
        
        print(f"\n📊 按交易所分类:")
        exchanges = ['SHFE', 'DCE', 'CZCE', 'INE']
        for exchange in exchanges:
            exchange_contracts = contract_manager.get_contracts_by_exchange(exchange)
            print(f"  {exchange}: {len(exchange_contracts)} 个品种")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取合约信息失败: {e}")
        return False

def main():
    """主函数"""
    # 检查环境
    if not check_environment():
        input("\n按回车键退出...")
        return
    
    # 主菜单循环
    while True:
        print(f"\n{'='*60}")
        print("🎯 请选择功能:")
        print("1. 快速演示 (品种筛选)")
        print("2. 回测演示 (策略回测)")
        print("3. 启动GUI界面")
        print("4. 查看合约信息")
        print("5. 系统说明")
        print("0. 退出")
        print("="*60)
        
        try:
            choice = input("请输入选择 (0-5): ").strip()
            
            if choice == "0":
                print("\n👋 感谢使用智能期货交易系统！")
                print("🎯 系统特点:")
                print("  ✅ 解决品种选择困难")
                print("  ✅ 解决参数调整迷茫") 
                print("  ✅ 解决净值停滞问题")
                break
                
            elif choice == "1":
                run_quick_demo()
                
            elif choice == "2":
                run_backtest_demo()
                
            elif choice == "3":
                start_gui()
                
            elif choice == "4":
                show_contract_info()
                
            elif choice == "5":
                print(f"\n📖 系统说明:")
                print("="*60)
                print("🎯 核心功能:")
                print("  1. 品种动态筛选 - 自动识别最适合的交易品种")
                print("  2. 参数自适应优化 - 根据市场变化调整策略参数")
                print("  3. 资金智能管理 - 实时监控和控制交易风险")
                print("  4. 完整监控体系 - 全方位系统状态监控")
                print("  5. 模块化设计 - 易于扩展和定制")
                print("\n💡 解决方案:")
                print("  ❌ 品种选择困难 → ✅ 系统自动推荐最优品种")
                print("  ❌ 参数调整迷茫 → ✅ 自动优化找到最佳参数")
                print("  ❌ 净值停滞问题 → ✅ 智能风控提升稳定性")
                print("\n🚀 使用建议:")
                print("  1. 先运行快速演示了解功能")
                print("  2. 使用GUI界面进行完整操作")
                print("  3. 根据实际需求调整参数")
                print("  4. 从小资金开始逐步扩大规模")
                
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"❌ 运行出错: {e}")
            traceback.print_exc()

if __name__ == "__main__":
    main()
