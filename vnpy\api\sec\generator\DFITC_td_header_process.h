void processFrontConnected(Task *task);

void processFrontDisconnected(Task *task);

void processRtnNotice(Task *task);

void processRspError(Task *task);

void processRspStockUserLogin(Task *task);

void processRspStockUserLogout(Task *task);

void processRspStockUserPasswordUpdate(Task *task);

void processRspStockEntrustOrder(Task *task);

void processRspStockWithdrawOrder(Task *task);

void processRspStockQryEntrustOrder(Task *task);

void processRspStockQryRealTimeTrade(Task *task);

void processRspStockQrySerialTrade(Task *task);

void processRspStockQryPosition(Task *task);

void processRspStockQryCapitalAccountInfo(Task *task);

void processRspStockQryAccountInfo(Task *task);

void processRspStockQryShareholderInfo(Task *task);

void processRspStockTransferFunds(Task *task);

void processRspStockEntrustBatchOrder(Task *task);

void processRspStockWithdrawBatchOrder(Task *task);

void processRspStockCalcAbleEntrustQty(Task *task);

void processRspStockCalcAblePurchaseETFQty(Task *task);

void processRspStockQryFreezeFundsDetail(Task *task);

void processRspStockQryFreezeStockDetail(Task *task);

void processRspStockQryTransferStockDetail(Task *task);

void processRspStockQryTransferFundsDetail(Task *task);

void processRspStockQryStockInfo(Task *task);

void processRspStockQryStockStaticInfo(Task *task);

void processRspStockQryTradeTime(Task *task);

void processStockEntrustOrderRtn(Task *task);

void processStockTradeRtn(Task *task);

void processStockWithdrawOrderRtn(Task *task);

void processRspSOPUserLogin(Task *task);

void processRspSOPUserLogout(Task *task);

void processRspSOPUserPasswordUpdate(Task *task);

void processRspSOPEntrustOrder(Task *task);

void processRspSOPQuoteEntrustOrder(Task *task);

void processRspSOPGroupSplit(Task *task);

void processRspSOPGroupExectueOrder(Task *task);

void processRspSOPQryGroupPosition(Task *task);

void processRspSOPLockOUnLockStock(Task *task);

void processRspSOPWithdrawOrder(Task *task);

void processRspSOPQryEntrustOrder(Task *task);

void processRspSOPQrySerialTrade(Task *task);

void processRspSOPQryPosition(Task *task);

void processRspSOPQryCollateralPosition(Task *task);

void processRspSOPQryCapitalAccountInfo(Task *task);

void processRspSOPQryAccountInfo(Task *task);

void processRspSOPQryShareholderInfo(Task *task);

void processRspSOPCalcAbleEntrustQty(Task *task);

void processRspSOPQryAbleLockStock(Task *task);

void processRspSOPQryContactInfo(Task *task);

void processRspSOPExectueOrder(Task *task);

void processRspSOPQryExecAssiInfo(Task *task);

void processRspSOPQryTradeTime(Task *task);

void processRspSOPQryExchangeInfo(Task *task);

void processRspSOPQryCommission(Task *task);

void processRspSOPQryDeposit(Task *task);

void processRspSOPQryContractObjectInfo(Task *task);

void processSOPEntrustOrderRtn(Task *task);

void processSOPTradeRtn(Task *task);

void processSOPWithdrawOrderRtn(Task *task);

void processRspFASLUserLogin(Task *task);

void processRspFASLUserLogout(Task *task);

void processRspFASLQryAbleFinInfo(Task *task);

void processRspFASLQryAbleSloInfo(Task *task);

void processRspFASLTransferCollateral(Task *task);

void processRspFASLDirectRepayment(Task *task);

void processRspFASLRepayStockTransfer(Task *task);

void processRspFASLEntrustCrdtOrder(Task *task);

void processRspFASLEntrustOrder(Task *task);

void processRspFASLCalcAbleEntrustCrdtQty(Task *task);

void processRspFASLQryCrdtFunds(Task *task);

void processRspFASLQryCrdtContract(Task *task);

void processRspFASLQryCrdtConChangeInfo(Task *task);

void processRspFASLTransferFunds(Task *task);

void processRspFASLQryAccountInfo(Task *task);

void processRspFASLQryCapitalAccountInfo(Task *task);

void processRspFASLQryShareholderInfo(Task *task);

void processRspFASLQryPosition(Task *task);

void processRspFASLQryEntrustOrder(Task *task);

void processRspFASLQrySerialTrade(Task *task);

void processRspFASLQryRealTimeTrade(Task *task);

void processRspFASLQryFreezeFundsDetail(Task *task);

void processRspFASLQryFreezeStockDetail(Task *task);

void processRspFASLQryTransferFundsDetail(Task *task);

void processRspFASLWithdrawOrder(Task *task);

void processRspFASLQrySystemTime(Task *task);

void processRspFASLQryTransferredContract(Task *task);

void processRspFASLDesirableFundsOut(Task *task);

void processRspFASLQryGuaranteedContract(Task *task);

void processRspFASLQryUnderlyingContract(Task *task);

void processFASLEntrustOrderRtn(Task *task);

void processFASLTradeRtn(Task *task);

void processFASLWithdrawOrderRtn(Task *task);

