.def("reqStockUserLogin", &MdApi::reqStockUserLogin)
.def("reqStockUserLogout", &MdApi::reqStockUserLogout)
.def("reqSOPUserLogin", &MdApi::reqSOPUserLogin)
.def("reqFASLUserLogin", &MdApi::reqFASLUserLogin)
.def("reqSOPUserLogout", &MdApi::reqSOPUserLogout)
.def("reqFASLUserLogout", &MdApi::reqFASLUserLogout)
.def("reqStockAvailableQuotQry", &MdApi::reqStockAvailableQuotQry)
.def("reqSopAvailableQuotQry", &MdApi::reqSopAvailableQuotQry)
.def("reqUserMDPasswordUpdate", &MdApi::reqUserMDPasswordUpdate)

.def("onFrontConnected", &MdApi::onFrontConnected)
.def("onFrontDisconnected", &MdApi::onFrontDisconnected)
.def("onRtnNotice", &MdApi::onRtnNotice)
.def("onRspError", &MdApi::onRspError)
.def("onRspStockUserLogin", &MdApi::onRspStockUserLogin)
.def("onRspStockUserLogout", &MdApi::onRspStockUserLogout)
.def("onRspSOPUserLogin", &MdApi::onRspSOPUserLogin)
.def("onRspSOPUserLogout", &MdApi::onRspSOPUserLogout)
.def("onRspFASLUserLogin", &MdApi::onRspFASLUserLogin)
.def("onRspFASLUserLogout", &MdApi::onRspFASLUserLogout)
.def("onRspStockSubMarketData", &MdApi::onRspStockSubMarketData)
.def("onRspStockUnSubMarketData", &MdApi::onRspStockUnSubMarketData)
.def("onRspSOPSubMarketData", &MdApi::onRspSOPSubMarketData)
.def("onRspSOPUnSubMarketData", &MdApi::onRspSOPUnSubMarketData)
.def("onStockMarketData", &MdApi::onStockMarketData)
.def("onSOPMarketData", &MdApi::onSOPMarketData)
.def("onRspStockAvailableQuot", &MdApi::onRspStockAvailableQuot)
.def("onRspSopAvailableQuot", &MdApi::onRspSopAvailableQuot)
.def("onRspUserMDPasswordUpdate", &MdApi::onRspUserMDPasswordUpdate)
;
