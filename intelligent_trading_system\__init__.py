"""
智能期货交易系统
Intelligent Futures Trading System

一套完整的系统化解决方案，解决期货策略三大痛点：
1. 品种选择困难 - 动态品种筛选引擎
2. 参数调整迷茫 - 参数自适应优化模块  
3. 净值停滞 - 资金智能管理系统

主要模块：
- instrument_selector: 品种动态筛选引擎
- parameter_optimizer: 参数自适应优化模块
- risk_manager: 资金智能管理系统
- data_manager: 数据管理模块
- monitor: 实时监控模块
- backtest_engine: 回测引擎
- strategy_framework: 策略框架
"""

__version__ = "1.0.0"
__author__ = "Intelligent Trading System"

# 导入主要模块
from .core.instrument_selector import InstrumentSelector
from .core.parameter_optimizer import ParameterOptimizer
from .core.risk_manager import RiskManager
from .core.data_manager import DataManager
from .core.monitor import SystemMonitor
from .core.backtest_engine import BacktestEngine
from .strategies.adaptive_trend_strategy import AdaptiveTrendStrategy

__all__ = [
    "InstrumentSelector",
    "ParameterOptimizer", 
    "RiskManager",
    "DataManager",
    "SystemMonitor",
    "BacktestEngine",
    "AdaptiveTrendStrategy"
]
