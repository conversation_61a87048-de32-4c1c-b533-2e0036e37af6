{"success_count": 5, "failure_count": 0, "success_rate": 1.0, "batch_config": {}, "performance_stats": {"mean": 3.612144921760952, "std": 0.6590589356320993, "min": 3.009698314156136, "max": 4.7681051596180986, "median": 3.3493510269075033}, "stability_stats": {"mean": 0.9626407102250747, "std": 0.0013557800540246203, "min": 0.9606975459430145, "max": 0.9648810377135976, "median": 0.9625923654723285}, "time_stats": {"mean": 33.32743711471558, "std": 14.250787713918305, "total": 166.63718557357788}, "parameter_statistics": {"k_3": {"mean": 20.0, "std": 0.0, "min": 20, "max": 20, "median": 20.0}, "k_5": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "sl_multiplier": {"mean": 7.8, "std": 1.16619037896906, "min": 7.0, "max": 10.0, "median": 7.0}, "macd_boll_count_fz": {"mean": 0.22000000000000003, "std": 0.07483314773547882, "min": 0.08, "max": 0.3, "median": 0.24}, "dk_fz": {"mean": 0.9199999999999999, "std": 0.0748331477354788, "min": 0.8, "max": 1.0, "median": 0.9}, "ping_zy": {"mean": 0.0, "std": 0.0, "min": 0, "max": 0, "median": 0.0}, "zy": {"mean": 100.0, "std": 0.0, "min": 100, "max": 100, "median": 100.0}, "AF": {"mean": 0.00184, "std": 0.00031999999999999997, "min": 0.0012000000000000001, "max": 0.002, "median": 0.002}, "AF_max": {"mean": 0.11600000000000002, "std": 0.0783836717690617, "min": 0.0, "max": 0.2, "median": 0.12}, "trailing_start_ratio": {"mean": 0.5200000000000001, "std": 0.20396078054371142, "min": 0.2, "max": 0.8, "median": 0.6000000000000001}, "k_15": {"mean": 15.0, "std": 0.0, "min": 15, "max": 15, "median": 15.0}, "k_30": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "atr_window": {"mean": 30.0, "std": 0.0, "min": 30, "max": 30, "median": 30.0}, "donchian_period": {"mean": 20.0, "std": 0.0, "min": 20, "max": 20, "median": 20.0}, "lots": {"mean": 1.0, "std": 0.0, "min": 1, "max": 1, "median": 1.0}, "use_trailing_stop": {"mean": 1.0, "std": 0.0, "min": 1, "max": 1, "median": 1.0}, "contract_multiplier": {"mean": 12.0, "std": 9.273618495495704, "min": 5.0, "max": 30.0, "median": 10.0}}, "top_performers": [{"symbol": "SH888.CZCE", "best_performance": 4.7681051596180986, "stability_score": 0.9648810377135976}, {"symbol": "bu888.SHFE", "best_performance": 3.8918072906643912, "stability_score": 0.9620905953693035}, {"symbol": "rb888.SHFE", "best_performance": 3.3493510269075033, "stability_score": 0.9606975459430145}, {"symbol": "TA888.CZCE", "best_performance": 3.041762817458628, "stability_score": 0.9629420066271293}, {"symbol": "PX888.CZCE", "best_performance": 3.009698314156136, "stability_score": 0.9625923654723285}], "most_stable": [{"symbol": "SH888.CZCE", "best_performance": 4.7681051596180986, "stability_score": 0.9648810377135976}, {"symbol": "TA888.CZCE", "best_performance": 3.041762817458628, "stability_score": 0.9629420066271293}, {"symbol": "PX888.CZCE", "best_performance": 3.009698314156136, "stability_score": 0.9625923654723285}, {"symbol": "bu888.SHFE", "best_performance": 3.8918072906643912, "stability_score": 0.9620905953693035}, {"symbol": "rb888.SHFE", "best_performance": 3.3493510269075033, "stability_score": 0.9606975459430145}], "failed_symbols": []}